import React, {useState} from 'react';
import {View, Button, Text} from 'react-native';
import {testInterceptors} from './src/middleWare/ApiCaller';

const TestComponent = () => {
  const [testStatus, setTestStatus] = useState('');

  const handleTestPress = async () => {
    try {
      setTestStatus('Testing...');
      await testInterceptors();
      setTestStatus('Test completed');
    } catch (error) {
      setTestStatus('Test failed: ' + (error.message || 'Unknown error'));
      console.error('Test error:', error);
    }
  };

  return (
    <View style={{padding: 20}}>
      <Button title="Test Interceptors" onPress={handleTestPress} />
      {testStatus ? <Text style={{marginTop: 10}}>{testStatus}</Text> : null}
    </View>
  );
};

export default TestComponent;
