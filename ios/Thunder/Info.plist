<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Thunder</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>dev.apis.thunder.softoo.co</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
			</dict>
		</dict>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<string>Roboto-Black.ttf</string>
		<string>Roboto-BlackItalic.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-BoldItalic.ttf</string>
		<string>Roboto-Italic.ttf</string>
		<string>Roboto-Light.ttf</string>
		<string>Roboto-LightItalic.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>Roboto-MediumItalic.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Roboto-Thin.ttf</string>
		<string>Roboto-ThinItalic.ttf</string>
		<string>BaiJamjuree-Bold.ttf</string>
		<string>BaiJamjuree-BoldItalic.ttf</string>
		<string>BaiJamjuree-ExtraLight.ttf</string>
		<string>BaiJamjuree-ExtraLightItalic.ttf</string>
		<string>BaiJamjuree-Italic.ttf</string>
		<string>BaiJamjuree-Light.ttf</string>
		<string>BaiJamjuree-LightItalic.ttf</string>
		<string>BaiJamjuree-Medium.ttf</string>
		<string>BaiJamjuree-MediumItalic.ttf</string>
		<string>BaiJamjuree-Regular.ttf</string>
		<string>BaiJamjuree-SemiBold.ttf</string>
		<string>BaiJamjuree-SemiBoldItalic.ttf</string>
	</array>
</dict>
</plist>
