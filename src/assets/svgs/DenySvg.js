import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const DenySvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.00009 10.1216L13.3031 15.4246C13.5845 15.706 13.9661 15.8641 14.3641 15.8641C14.762 15.8641 15.1437 15.706 15.4251 15.4246C15.7065 15.1432 15.8646 14.7616 15.8646 14.3636C15.8646 13.9657 15.7065 13.584 15.4251 13.3026L10.1201 7.99962L15.4241 2.69662C15.5634 2.55729 15.6738 2.39189 15.7492 2.20987C15.8245 2.02785 15.8633 1.83277 15.8632 1.63577C15.8632 1.43877 15.8243 1.24371 15.7489 1.06172C15.6735 0.879735 15.5629 0.714389 15.4236 0.575122C15.2843 0.435855 15.1189 0.325396 14.9368 0.25005C14.7548 0.174704 14.5597 0.135948 14.3627 0.135995C14.1657 0.136041 13.9707 0.174889 13.7887 0.25032C13.6067 0.325752 13.4414 0.43629 13.3021 0.575622L8.00009 5.87862L2.69709 0.575622C2.55879 0.432293 2.39333 0.317943 2.21036 0.239244C2.02739 0.160546 1.83058 0.119074 1.63141 0.11725C1.43224 0.115426 1.23471 0.153286 1.05033 0.22862C0.86595 0.303955 0.698421 0.415255 0.557516 0.556027C0.416611 0.696799 0.305153 0.864224 0.229644 1.04853C0.154136 1.23284 0.11609 1.43034 0.117726 1.62951C0.119363 1.82868 0.160648 2.02553 0.239174 2.20857C0.3177 2.39161 0.431894 2.55718 0.575092 2.69562L5.88009 7.99962L0.576093 13.3036C0.432894 13.4421 0.3187 13.6076 0.240174 13.7907C0.161648 13.9737 0.120363 14.1706 0.118726 14.3697C0.11709 14.5689 0.155136 14.7664 0.230644 14.9507C0.306153 15.135 0.417611 15.3024 0.558516 15.4432C0.69942 15.584 0.86695 15.6953 1.05133 15.7706C1.23571 15.846 1.43324 15.8838 1.63241 15.882C1.83158 15.8802 2.02839 15.8387 2.21136 15.76C2.39433 15.6813 2.55979 15.5669 2.69809 15.4236L8.00009 10.1216Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(DenySvg);
