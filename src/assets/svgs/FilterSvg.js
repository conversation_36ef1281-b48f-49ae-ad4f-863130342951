import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const FilterSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M23 11.9994H8.30757M3.12151 11.9994H1M3.12151 11.9994C3.12151 11.3415 3.39464 10.7105 3.88082 10.2452C4.36699 9.78 5.02639 9.51863 5.71395 9.51863C6.4015 9.51863 7.0609 9.78 7.54707 10.2452C8.03325 10.7105 8.30638 11.3415 8.30638 11.9994C8.30638 12.6574 8.03325 13.2884 7.54707 13.7536C7.0609 14.2189 6.4015 14.4802 5.71395 14.4802C5.02639 14.4802 4.36699 14.2189 3.88082 13.7536C3.39464 13.2884 3.12151 12.6574 3.12151 11.9994ZM23 19.5181H16.1645M16.1645 19.5181C16.1645 20.1762 15.8908 20.8079 15.4045 21.2732C14.9182 21.7386 14.2586 22 13.5709 22C12.8834 22 12.224 21.7375 11.7378 21.2723C11.2516 20.807 10.9785 20.176 10.9785 19.5181M16.1645 19.5181C16.1645 18.86 15.8908 18.2294 15.4045 17.764C14.9182 17.2987 14.2586 17.0373 13.5709 17.0373C12.8834 17.0373 12.224 17.2986 11.7378 17.7639C11.2516 18.2291 10.9785 18.8601 10.9785 19.5181M10.9785 19.5181H1M23 4.4808H19.3076M14.1215 4.4808H1M14.1215 4.4808C14.1215 3.82285 14.3946 3.19185 14.8808 2.72661C15.367 2.26137 16.0264 2 16.7139 2C17.0544 2 17.3915 2.06417 17.706 2.18884C18.0206 2.31351 18.3063 2.49625 18.5471 2.72661C18.7878 2.95697 18.9788 3.23045 19.109 3.53144C19.2393 3.83242 19.3064 4.15501 19.3064 4.4808C19.3064 4.80658 19.2393 5.12917 19.109 5.43016C18.9788 5.73114 18.7878 6.00462 18.5471 6.23498C18.3063 6.46535 18.0206 6.64808 17.706 6.77275C17.3915 6.89743 17.0544 6.96159 16.7139 6.96159C16.0264 6.96159 15.367 6.70022 14.8808 6.23498C14.3946 5.76974 14.1215 5.13874 14.1215 4.4808Z"
      stroke={color}
      strokeWidth={2}
      strokeMiterlimit={10}
      strokeLinecap="round"
    />
  </Svg>
);
export default React.memo(FilterSvg);
