import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const InfraSvg = props => (
  <Svg
    width={14}
    height={14}
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M11 9.5C10.75 9.5 10.5 9.4 10.3 9.2L8.8 7.7C8.6 7.5 8.5 7.25 8.5 7C8.5 6.75 8.6 6.5 8.8 6.3L10.3 4.8C10.5 4.6 10.75 4.5 11 4.5C11.25 4.5 11.5 4.6 11.7 4.8L13.2 6.3C13.4 6.5 13.5 6.75 13.5 7C13.5 7.25 13.4 7.5 13.2 7.7L11.7 9.2C11.5 9.4 11.25 9.5 11 9.5ZM11 5.5L9.5 7L11 8.5L12.5 7L11 5.5ZM7 5.5C6.75 5.5 6.5 5.4 6.3 5.2L4.8 3.7C4.6 3.5 4.5 3.25 4.5 3C4.5 2.75 4.6 2.5 4.8 2.3L6.3 0.8C6.5 0.6 6.75 0.5 7 0.5C7.25 0.5 7.5 0.6 7.7 0.8L9.2 2.3C9.4 2.5 9.5 2.75 9.5 3C9.5 3.25 9.4 3.5 9.2 3.7L7.7 5.2C7.5 5.4 7.25 5.5 7 5.5ZM7 1.5L5.5 3L7 4.5L8.5 3L7 1.5ZM7 13.5C6.75 13.5 6.5 13.4 6.3 13.2L4.8 11.7C4.6 11.5 4.5 11.25 4.5 11C4.5 10.75 4.6 10.5 4.8 10.3L6.3 8.8C6.5 8.6 6.75 8.5 7 8.5C7.25 8.5 7.5 8.6 7.7 8.8L9.2 10.3C9.4 10.5 9.5 10.75 9.5 11C9.5 11.25 9.4 11.5 9.2 11.7L7.7 13.2C7.5 13.4 7.25 13.5 7 13.5ZM7 9.5L5.5 11L7 12.5L8.5 11L7 9.5ZM3 9.5C2.75 9.5 2.5 9.4 2.3 9.2L0.8 7.7C0.6 7.5 0.5 7.25 0.5 7C0.5 6.75 0.6 6.5 0.8 6.3L2.3 4.8C2.5 4.6 2.75 4.5 3 4.5C3.25 4.5 3.5 4.6 3.7 4.8L5.2 6.3C5.4 6.5 5.5 6.75 5.5 7C5.5 7.25 5.4 7.5 5.2 7.7L3.7 9.2C3.5 9.4 3.25 9.5 3 9.5ZM3 5.5L1.5 7L3 8.5L4.5 7L3 5.5Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(InfraSvg);
