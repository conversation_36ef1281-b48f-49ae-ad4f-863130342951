import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const LocationPin = props => (
  <Svg
    width={12}
    height={14}
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M2.2665 2.17351C3.26005 1.19983 4.5977 0.657592 5.98879 0.664618C7.37988 0.671644 8.71199 1.22737 9.69564 2.21103C10.6793 3.19469 11.235 4.5268 11.2421 5.91788C11.2491 7.30897 10.7068 8.64663 9.73317 9.64017L6.9425 12.4308C6.69246 12.6808 6.35339 12.8212 5.99983 12.8212C5.64628 12.8212 5.3072 12.6808 5.05717 12.4308L2.2665 9.64017C1.27643 8.65 0.720215 7.30709 0.720215 5.90684C0.720215 4.50659 1.27643 3.16368 2.2665 2.17351Z"
      stroke="#96999E"
      strokeLinejoin="round"
    />
    <Path
      d="M6 7.90674C7.10457 7.90674 8 7.01131 8 5.90674C8 4.80217 7.10457 3.90674 6 3.90674C4.89543 3.90674 4 4.80217 4 5.90674C4 7.01131 4.89543 7.90674 6 7.90674Z"
      stroke="#96999E"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(LocationPin);
