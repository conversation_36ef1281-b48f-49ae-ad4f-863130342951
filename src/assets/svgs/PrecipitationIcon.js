import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const PrecipitationIcon = props => (
  <Svg
    fill="#000000"
    width="30px"
    height="30px"
    viewBox="0 0 24 24"
    id="rain"
    data-name="Line Color"
    xmlns="http://www.w3.org/2000/svg"
    className="icon line-color"
    {...props}>
    <Path
      id="secondary"
      d="M6,17v2m4-2v4m4-4v2m4-2v4"
      style={{
        fill: 'none',
        stroke: '#96999E',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        strokeWidth: 1,
      }}
    />
    <Path
      id="primary"
      d="M21,9a4,4,0,0,1-4,4H6A3,3,0,1,1,7.08,7.21a5,5,0,0,1,9-2.09A4.08,4.08,0,0,1,17,5,4,4,0,0,1,21,9Z"
      style={{
        fill: 'none',
        stroke: '#96999E',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        strokeWidth: 1,
      }}
    />
  </Svg>
);
export default React.memo(PrecipitationIcon);
