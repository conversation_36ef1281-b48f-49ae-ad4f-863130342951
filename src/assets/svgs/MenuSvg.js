import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const MenuSvg = props => (
  <Svg
    width={4}
    height={18}
    viewBox="0 0 4 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M2 14C2.53043 14 3.03914 14.2107 3.41421 14.5858C3.78929 14.9609 4 15.4696 4 16C4 16.5304 3.78929 17.0391 3.41421 17.4142C3.03914 17.7893 2.53043 18 2 18C1.46957 18 0.960859 17.7893 0.585787 17.4142C0.210714 17.0391 -7.89833e-08 16.5304 -1.07492e-07 16C-1.36001e-07 15.4696 0.210714 14.9609 0.585787 14.5858C0.960859 14.2107 1.46957 14 2 14ZM2 7C2.53043 7 3.03914 7.21071 3.41421 7.58579C3.78928 7.96086 4 8.46957 4 9C4 9.53043 3.78928 10.0391 3.41421 10.4142C3.03914 10.7893 2.53043 11 2 11C1.46957 11 0.960859 10.7893 0.585786 10.4142C0.210714 10.0391 -4.55205e-07 9.53043 -4.83714e-07 9C-5.12222e-07 8.46957 0.210713 7.96086 0.585786 7.58579C0.960859 7.21071 1.46957 7 2 7ZM2 -7.11006e-08C2.53043 -8.99576e-08 3.03914 0.210714 3.41421 0.585787C3.78928 0.960859 4 1.46957 4 2C4 2.53043 3.78928 3.03914 3.41421 3.41421C3.03914 3.78929 2.53043 4 2 4C1.46957 4 0.960859 3.78929 0.585786 3.41421C0.210713 3.03914 -8.31427e-07 2.53043 -8.59936e-07 2C-8.88444e-07 1.46957 0.210713 0.960859 0.585786 0.585787C0.960858 0.210714 1.46957 -5.22435e-08 2 -7.11006e-08Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(MenuSvg);
