import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const EmptyFolder = props => (
  <Svg
    width={62}
    height={62}
    viewBox="0 0 62 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M57.2912 22.415C57.7994 19.6671 58.6825 17.0177 59.2034 14.4189H38.9303L36.0466 19.4655L30.6889 21.2678L24.8722 19.4655L21.9885 14.4189H2.79688C2.80499 14.4189 3.14611 15.8797 2.79688 14.4189C3.63339 18.6366 4.95804 22.9052 5.89433 27.3717L26.773 35.0402L57.2912 22.415Z"
      fill="#3F3F3F"
    />
    <Path
      d="M62 22.4141H42.3125L40.8404 27.3707H0L7.35947 58.1457H54.6405L62 22.4141Z"
      fill="#616161"
    />
    <Path
      d="M36.5091 7.46776L25.0182 18.9587M30.6393 21.6283C32.2791 21.6283 33.882 21.142 35.2454 20.231C36.6088 19.32 37.6714 18.0252 38.2989 16.5103C38.9264 14.9954 39.0906 13.3284 38.7707 11.7201C38.4508 10.1119 37.6612 8.63464 36.5017 7.47517C35.3423 6.31569 33.865 5.52608 32.2568 5.20618C30.6485 4.88628 28.9815 5.05047 27.4666 5.67797C25.9517 6.30547 24.6569 7.36811 23.7459 8.73151C22.8349 10.0949 22.3486 11.6978 22.3486 13.3376C22.3486 15.5364 23.2221 17.6452 24.7769 19.2C26.3317 20.7548 28.4405 21.6283 30.6393 21.6283Z"
      stroke="#96999E"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(EmptyFolder);
