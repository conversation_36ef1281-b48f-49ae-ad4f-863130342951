import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const DrawerIcon = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={18}
    height={16}
    viewBox="0 0 18 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 1.33333C0 0.979711 0.135459 0.640573 0.376577 0.390525C0.617695 0.140476 0.944722 0 1.28571 0H16.7143C17.0553 0 17.3823 0.140476 17.6234 0.390525C17.8645 0.640573 18 0.979711 18 1.33333C18 1.68696 17.8645 2.02609 17.6234 2.27614C17.3823 2.52619 17.0553 2.66667 16.7143 2.66667H1.28571C0.944722 2.66667 0.617695 2.52619 0.376577 2.27614C0.135459 2.02609 0 1.68696 0 1.33333ZM0 8C0 7.64638 0.135459 7.30724 0.376577 7.05719C0.617695 6.80714 0.944722 6.66667 1.28571 6.66667H9C9.34099 6.66667 9.66802 6.80714 9.90914 7.05719C10.1503 7.30724 10.2857 7.64638 10.2857 8C10.2857 8.35362 10.1503 8.69276 9.90914 8.94281C9.66802 9.19286 9.34099 9.33333 9 9.33333H1.28571C0.944722 9.33333 0.617695 9.19286 0.376577 8.94281C0.135459 8.69276 0 8.35362 0 8ZM0 14.6667C0 14.313 0.135459 13.9739 0.376577 13.7239C0.617695 13.4738 0.944722 13.3333 1.28571 13.3333H16.7143C17.0553 13.3333 17.3823 13.4738 17.6234 13.7239C17.8645 13.9739 18 14.313 18 14.6667C18 15.0203 17.8645 15.3594 17.6234 15.6095C17.3823 15.8595 17.0553 16 16.7143 16H1.28571C0.944722 16 0.617695 15.8595 0.376577 15.6095C0.135459 15.3594 0 15.0203 0 14.6667Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(DrawerIcon);
