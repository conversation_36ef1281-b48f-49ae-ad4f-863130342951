import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const ChatBotSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={15}
    height={17}
    viewBox="0 0 15 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M7.7999 3.9498C7.56121 3.9498 7.33229 4.04463 7.16351 4.21341C6.99472 4.38219 6.8999 4.61111 6.8999 4.8498C6.8999 5.0885 6.99472 5.31742 7.16351 5.4862C7.33229 5.65498 7.56121 5.7498 7.7999 5.7498C8.0386 5.7498 8.26752 5.65498 8.4363 5.4862C8.60508 5.31742 8.6999 5.0885 8.6999 4.8498C8.6999 4.61111 8.60508 4.38219 8.4363 4.21341C8.26752 4.04463 8.0386 3.9498 7.7999 3.9498ZM3.2999 4.8498C3.2999 4.61111 3.39472 4.38219 3.56351 4.21341C3.73229 4.04463 3.96121 3.9498 4.1999 3.9498C4.4386 3.9498 4.66752 4.04463 4.8363 4.21341C5.00508 4.38219 5.0999 4.61111 5.0999 4.8498C5.0999 5.0885 5.00508 5.31742 4.8363 5.4862C4.66752 5.65498 4.4386 5.7498 4.1999 5.7498C3.96121 5.7498 3.73229 5.65498 3.56351 5.4862C3.39472 5.31742 3.2999 5.0885 3.2999 4.8498ZM6.4499 1.2498C6.4499 1.13046 6.40249 1.016 6.3181 0.931607C6.23371 0.847215 6.11925 0.799805 5.9999 0.799805C5.88055 0.799805 5.7661 0.847215 5.6817 0.931607C5.59731 1.016 5.5499 1.13046 5.5499 1.2498V1.6998H2.8499C2.49186 1.6998 2.14848 1.84204 1.89531 2.09521C1.64213 2.34838 1.4999 2.69176 1.4999 3.0498V6.64981C1.4999 7.00785 1.64213 7.35123 1.89531 7.6044C2.14848 7.85757 2.49186 7.9998 2.8499 7.9998H8.5145L8.8079 7.0998H2.8499C2.73055 7.0998 2.6161 7.05239 2.5317 6.968C2.44731 6.88361 2.3999 6.76915 2.3999 6.64981V3.0498C2.3999 2.93046 2.44731 2.816 2.5317 2.73161C2.6161 2.64722 2.73055 2.5998 2.8499 2.5998H9.1499C9.26925 2.5998 9.38371 2.64722 9.4681 2.73161C9.55249 2.816 9.5999 2.93046 9.5999 3.0498V6.27451C9.89172 6.17 10.2116 6.17512 10.4999 6.2889V3.0498C10.4999 2.69176 10.3577 2.34838 10.1045 2.09521C9.85132 1.84204 9.50794 1.6998 9.1499 1.6998H6.4499V1.2498ZM4.6499 9.3498H6.3671C6.1316 9.58906 5.99969 9.91138 5.9999 10.2471V10.2525H1.7789C1.58556 10.2525 1.40014 10.3293 1.26342 10.466C1.12671 10.6027 1.0499 10.7882 1.0499 10.9815V11.3748C1.0499 11.9958 1.1678 12.6852 1.7717 13.2369C2.3909 13.8012 3.6059 14.2998 5.9999 14.2998C7.3949 14.2998 8.3894 14.1306 9.0995 13.8768C9.26207 14.059 9.47406 14.19 9.7097 14.2539C9.65168 14.3784 9.61604 14.5121 9.6044 14.649C8.78 14.9793 7.6811 15.18 6.2249 15.198V15.1998H5.7749V15.198C3.4205 15.1683 1.9994 14.6625 1.1651 13.902C0.377602 13.182 0.185002 12.3027 0.154402 11.6016H0.149902V10.9806C0.149902 10.0815 0.878902 9.35251 1.7789 9.35251H4.6499V9.3498ZM10.3901 7.3536L10.7033 8.3175C10.8005 8.61078 10.965 8.87724 11.1837 9.09556C11.4023 9.31388 11.669 9.478 11.9624 9.57481L12.9263 9.888L12.9452 9.8934C13.0195 9.91958 13.0839 9.96817 13.1295 10.0325C13.175 10.0968 13.1995 10.1737 13.1995 10.2525C13.1995 10.3313 13.175 10.4082 13.1295 10.4725C13.0839 10.5368 13.0195 10.5854 12.9452 10.6116L11.9813 10.9248C11.6879 11.0216 11.4212 11.1857 11.2026 11.404C10.9839 11.6224 10.8194 11.8888 10.7222 12.1821L10.409 13.1451C10.3828 13.2194 10.3342 13.2838 10.2699 13.3294C10.2056 13.3749 10.1287 13.3994 10.0499 13.3994C9.97109 13.3994 9.89421 13.3749 9.82989 13.3294C9.76557 13.2838 9.71697 13.2194 9.6908 13.1451L9.3767 12.1821C9.26491 11.8414 9.06272 11.5375 8.7917 11.3028C8.59474 11.1319 8.36596 11.0016 8.1185 10.9194L7.1546 10.6062C7.08026 10.58 7.01587 10.5314 6.97032 10.4671C6.92478 10.4028 6.90031 10.3259 6.90031 10.2471C6.90031 10.1683 6.92478 10.0914 6.97032 10.0271C7.01587 9.96277 7.08026 9.91418 7.1546 9.888L8.1185 9.57481C8.40826 9.47539 8.67099 9.31009 8.88604 9.09192C9.10108 8.87375 9.26257 8.60866 9.3578 8.3175L9.671 7.35451C9.69698 7.27985 9.74556 7.21513 9.80999 7.16933C9.87442 7.12354 9.9515 7.09893 10.0306 7.09893C10.1096 7.09893 10.1867 7.12354 10.2511 7.16933C10.3155 7.21513 10.3641 7.27985 10.3901 7.35451M14.8046 14.4924L14.1152 14.2692C13.9061 14.1994 13.7161 14.0819 13.5602 13.926C13.4044 13.7701 13.2868 13.5801 13.217 13.371L12.992 12.6825C12.9734 12.6293 12.9387 12.5833 12.8927 12.5507C12.8468 12.5181 12.7918 12.5006 12.7355 12.5006C12.6792 12.5006 12.6242 12.5181 12.5783 12.5507C12.5323 12.5833 12.4976 12.6293 12.479 12.6825L12.2558 13.371C12.1872 13.5788 12.0716 13.7679 11.918 13.9237C11.7644 14.0795 11.577 14.1977 11.3702 14.2692L10.6817 14.4924C10.6421 14.5065 10.6063 14.5296 10.577 14.5599C10.5478 14.5901 10.526 14.6267 10.5133 14.6668C10.5006 14.7069 10.4973 14.7494 10.5037 14.7909C10.5101 14.8325 10.5261 14.872 10.5503 14.9064C10.5827 14.9514 10.6286 14.9874 10.6817 15.0054L11.3702 15.2295C11.58 15.2995 11.7706 15.4174 11.9268 15.574C12.0831 15.7305 12.2007 15.9214 12.2702 16.1313L12.4934 16.8189C12.512 16.8721 12.5467 16.9181 12.5927 16.9507C12.6386 16.9833 12.6936 17.0008 12.7499 17.0008C12.8062 17.0008 12.8612 16.9833 12.9071 16.9507C12.9531 16.9181 12.9878 16.8721 13.0064 16.8189L13.2305 16.1313C13.3003 15.9219 13.4179 15.7317 13.574 15.5757C13.73 15.4196 13.9202 15.302 14.1296 15.2322L14.8181 15.009C14.8713 14.9904 14.9173 14.9557 14.9499 14.9097C14.9825 14.8638 15 14.8088 15 14.7525C15 14.6962 14.9825 14.6412 14.9499 14.5953C14.9173 14.5493 14.8713 14.5146 14.8181 14.496L14.8046 14.4924Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(ChatBotSvg);
