import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const SendSvg = props => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M18.0401 0.323441C19.0561 -0.0315589 20.0321 0.944441 19.6771 1.96044L13.7521 18.8904C13.3671 19.9884 11.8371 20.0504 11.3651 18.9874L8.5061 12.5554L12.5301 8.53044C12.6626 8.38827 12.7347 8.20022 12.7313 8.00592C12.7278 7.81162 12.6491 7.62623 12.5117 7.48882C12.3743 7.35141 12.1889 7.27269 11.9946 7.26927C11.8003 7.26584 11.6123 7.33796 11.4701 7.47044L7.4451 11.4944L1.0131 8.63544C-0.0499011 8.16244 0.0130987 6.63344 1.1101 6.24844L18.0401 0.323441Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(SendSvg);
