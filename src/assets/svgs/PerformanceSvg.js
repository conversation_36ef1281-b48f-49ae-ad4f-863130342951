import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const PerformanceSvg = props => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M0.666504 15.6665V0.666504H2.33317V10.3432L4.14984 8.5265C4.25453 8.78752 4.41098 9.02464 4.60977 9.22357C4.80856 9.4225 5.04556 9.57913 5.3065 9.684L2.33317 12.6573V15.6665H17.3332V17.3332H2.33317C1.89114 17.3332 1.46722 17.1576 1.15466 16.845C0.842099 16.5325 0.666504 16.1085 0.666504 15.6665Z"
      fill="#96999E"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.6554 9.10246C13.8334 9.37209 13.9462 9.67941 13.985 10.0001C14.0237 10.3209 13.9874 10.6462 13.8788 10.9505C13.7701 11.2547 13.5923 11.5296 13.3592 11.7533C13.1261 11.9769 12.8441 12.1434 12.5357 12.2393C12.2272 12.3353 11.9006 12.3583 11.5818 12.3063C11.2629 12.2544 10.9605 12.129 10.6984 11.9401C10.4364 11.7512 10.2218 11.5039 10.0717 11.2178C9.92168 10.9317 9.84021 10.6146 9.83377 10.2916L7.48877 9.28746C7.70751 9.08756 7.88109 8.8433 7.99792 8.57098C8.11475 8.29866 8.17214 8.00456 8.16627 7.70829L10.5104 8.71246C10.7492 8.49322 11.036 8.33296 11.3479 8.24454C11.6597 8.15612 11.988 8.14199 12.3063 8.20329L13.5104 6.39746C13.6637 6.63006 13.8621 6.82955 14.0938 6.98414C14.3256 7.13872 14.586 7.24526 14.8596 7.29746L13.6554 9.10246ZM12.3329 10.25C12.3329 10.3605 12.289 10.4664 12.2109 10.5446C12.1328 10.6227 12.0268 10.6666 11.9163 10.6666C11.8058 10.6666 11.6998 10.6227 11.6216 10.5446C11.5435 10.4664 11.4996 10.3605 11.4996 10.25C11.4996 10.1395 11.5435 10.0335 11.6216 9.95533C11.6998 9.87719 11.8058 9.83329 11.9163 9.83329C12.0268 9.83329 12.1328 9.87719 12.2109 9.95533C12.289 10.0335 12.3329 10.1395 12.3329 10.25Z"
      fill="#96999E"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.1666 7.70817C8.15554 7.15564 7.92545 6.63012 7.52694 6.24724C7.12842 5.86435 6.59413 5.65545 6.0416 5.6665C5.48906 5.67755 4.96355 5.90765 4.58066 6.30616C4.19777 6.70468 3.98888 7.23897 3.99993 7.7915C4.01098 8.34404 4.24107 8.86955 4.63959 9.25244C5.0381 9.63533 5.57239 9.84422 6.12493 9.83317C6.67746 9.82212 7.20298 9.59203 7.58586 9.19351C7.96875 8.795 8.17765 8.2607 8.1666 7.70817ZM6.49993 7.74984C6.49993 7.86034 6.45603 7.96633 6.37789 8.04447C6.29975 8.12261 6.19377 8.1665 6.08326 8.1665C5.97276 8.1665 5.86677 8.12261 5.78863 8.04447C5.71049 7.96633 5.6666 7.86034 5.6666 7.74984C5.6666 7.63933 5.71049 7.53335 5.78863 7.45521C5.86677 7.37707 5.97276 7.33317 6.08326 7.33317C6.19377 7.33317 6.29975 7.37707 6.37789 7.45521C6.45603 7.53335 6.49993 7.63933 6.49993 7.74984ZM15.2499 7.33317C15.8025 7.33317 16.3324 7.11368 16.7231 6.72298C17.1138 6.33228 17.3333 5.80237 17.3333 5.24984C17.3333 4.6973 17.1138 4.1674 16.7231 3.7767C16.3324 3.386 15.8025 3.1665 15.2499 3.1665C14.6974 3.1665 14.1675 3.386 13.7768 3.7767C13.3861 4.1674 13.1666 4.6973 13.1666 5.24984C13.1666 5.80237 13.3861 6.33228 13.7768 6.72298C14.1675 7.11368 14.6974 7.33317 15.2499 7.33317ZM15.2499 5.6665C15.3604 5.6665 15.4664 5.62261 15.5446 5.54446C15.6227 5.46632 15.6666 5.36034 15.6666 5.24984C15.6666 5.13933 15.6227 5.03335 15.5446 4.95521C15.4664 4.87707 15.3604 4.83317 15.2499 4.83317C15.1394 4.83317 15.0334 4.87707 14.9553 4.95521C14.8772 5.03335 14.8333 5.13933 14.8333 5.24984C14.8333 5.36034 14.8772 5.46632 14.9553 5.54446C15.0334 5.62261 15.1394 5.6665 15.2499 5.6665Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(PerformanceSvg);
