import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const KpiMonitoringSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={18}
    height={16}
    viewBox="0 0 18 16"
    fill={color}
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M15.75 11V2H2.25V11H15.75ZM15.75 0.5C16.1478 0.5 16.5294 0.658035 16.8107 0.93934C17.092 1.22064 17.25 1.60218 17.25 2V11C17.25 11.3978 17.092 11.7794 16.8107 12.0607C16.5294 12.342 16.1478 12.5 15.75 12.5H10.5V14H12V15.5H6V14H7.5V12.5H2.25C1.85218 12.5 1.47064 12.342 1.18934 12.0607C0.908035 11.7794 0.75 11.3978 0.75 11V2C0.75 1.1675 1.4175 0.5 2.25 0.5H15.75ZM3.75 3.5H10.5V7.25H3.75V3.5ZM11.25 3.5H14.25V5H11.25V3.5ZM14.25 5.75V9.5H11.25V5.75H14.25ZM3.75 8H6.75V9.5H3.75V8ZM7.5 8H10.5V9.5H7.5V8Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(KpiMonitoringSvg);
