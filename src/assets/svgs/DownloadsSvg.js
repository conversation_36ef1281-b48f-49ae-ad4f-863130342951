import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const DownloadsSvg = props => (
  <Svg
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M11 12.25V1M4.75 13.5H2.25C1.91848 13.5 1.60054 13.6317 1.36612 13.8661C1.1317 14.1005 1 14.4185 1 14.75V19.75C1 20.0815 1.1317 20.3995 1.36612 20.6339C1.60054 20.8683 1.91848 21 2.25 21H19.75C20.0815 21 20.3995 20.8683 20.6339 20.6339C20.8683 20.3995 21 20.0815 21 19.75V14.75C21 14.4185 20.8683 14.1005 20.6339 13.8661C20.3995 13.6317 20.0815 13.5 19.75 13.5H17.25M16 7.25L11 13.5L6 7.25M17.25 17.25H17.2625"
      stroke="white"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(DownloadsSvg);
