import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const WindDirectionSvg = props => (
  <Svg
    width={110}
    height={110}
    viewBox="0 0 89 90"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M7.74232 56.7721C-1.9141 51.8554 -1.91411 38.1447 7.74231 33.228L69.0803 1.99688C79.6184 -3.36872 91.4361 6.57521 87.842 17.7838L80.4024 40.9854C79.5649 43.5974 79.5649 46.4033 80.4024 49.0153L87.8419 72.2161C91.436 83.4247 79.6183 93.3687 69.0802 88.0031L7.74232 56.7721Z"
      fill="#7D5228"
    />
  </Svg>
);
export default React.memo(WindDirectionSvg);
