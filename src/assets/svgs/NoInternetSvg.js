import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const NoInternetSvg = props => (
  <Svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M6.66651 6.6665C5.40035 7.89185 4.39371 9.35944 3.70652 10.9819C3.01933 12.6044 2.66564 14.3485 2.66651 16.1105C2.66651 23.4132 8.63584 29.3332 15.9998 29.3332C19.4854 29.3416 22.8357 27.9847 25.3332 25.5532"
      stroke="#96999E"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M20.6665 21.333C19.8438 26.0423 18.1865 29.333 15.9998 29.333C13.0532 29.333 10.6665 23.3637 10.6665 15.9997C10.6665 14.3583 10.7852 12.7863 11.0025 11.333M2.6665 15.9997H15.9998"
      stroke="#96999E"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M9.54915 3.19719C9.31426 3.31778 9.13658 3.52638 9.05491 3.77747C8.97323 4.02857 8.99417 4.30177 9.11317 4.53748C9.23217 4.77319 9.43956 4.95227 9.6901 5.03564C9.94064 5.11902 10.214 5.09992 10.4505 4.98252L9.54915 3.19719ZM27.0158 21.5492C26.8963 21.7861 26.8758 22.0608 26.9588 22.3129C27.0418 22.5649 27.2216 22.7737 27.4585 22.8932C27.6954 23.0127 27.9701 23.0332 28.2222 22.9502C28.4742 22.8672 28.683 22.6874 28.8025 22.4505L27.0158 21.5492ZM21.3332 15.9999H20.3332C20.3332 16.5519 20.7812 16.9999 21.3332 16.9999V15.9999ZM11.0705 6.30119C10.9797 6.54672 10.9887 6.81808 11.0958 7.05699C11.2028 7.29589 11.3993 7.4833 11.643 7.57896C11.8867 7.67462 12.1581 7.6709 12.3991 7.56861C12.6401 7.46632 12.8314 7.2736 12.9318 7.03185L11.0705 6.30119ZM15.9998 3.66652C19.2708 3.66652 22.4079 4.96592 24.7208 7.27887C27.0338 9.59182 28.3332 12.7289 28.3332 15.9999H30.3332C30.3332 8.08385 23.9158 1.66652 15.9998 1.66652V3.66652ZM10.4505 4.98252C12.1712 4.11396 14.0723 3.66311 15.9998 3.66652V1.66652C13.7592 1.66312 11.5493 2.1875 9.54915 3.19719L10.4505 4.98252ZM28.3332 15.9999C28.3366 17.9273 27.8857 19.8285 27.0172 21.5492L28.8025 22.4505C29.8122 20.4503 30.3365 18.2404 30.3332 15.9999H28.3332ZM15.9998 3.66652C16.3732 3.66652 16.8292 3.85185 17.3492 4.40119C17.8718 4.95319 18.3892 5.80652 18.8425 6.94252C19.7492 9.21052 20.3332 12.4132 20.3332 15.9999H22.3332C22.3332 12.2239 21.7225 8.75852 20.6998 6.19985C20.1892 4.92385 19.5558 3.82385 18.8025 3.02652C18.0465 2.22785 17.0998 1.66652 15.9998 1.66652V3.66652ZM12.9318 7.03185C13.3785 5.89185 13.9398 5.01852 14.5105 4.44785C15.0878 3.87185 15.6065 3.66652 15.9998 3.66652V1.66652C14.9012 1.66652 13.9038 2.22652 13.0972 3.03319C12.2838 3.84652 11.5905 4.97319 11.0705 6.30119L12.9318 7.03185ZM21.3332 16.9999H29.3332V14.9999H21.3332V16.9999Z"
      fill="#96999E"
    />
    <Path
      d="M2.6665 2.6665L29.3332 29.3332"
      stroke="#96999E"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(NoInternetSvg);
