import * as React from 'react';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';
const ThunderSvg = props => (
  <Svg
    width={24}
    height={27}
    viewBox="0 0 24 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M23.454 0.729425C23.2217 0.168797 22.7701 -0.0760684 22.17 0.0205914C21.4216 0.143027 20.8151 0.561885 20.2345 1.01296C19.8022 1.3416 19.4087 1.71536 18.9957 2.06333C18.9312 2.12133 18.8538 2.19221 18.7763 2.2051C18.2925 2.30176 17.8086 2.38552 17.3117 2.44997C17.9634 1.81846 18.6666 1.27071 19.5248 0.948518C19.5183 0.916296 19.5183 0.884081 19.5118 0.85186C19.1957 0.87119 18.8731 0.845421 18.5699 0.916303C17.5183 1.14184 16.6408 1.72824 15.8278 2.39841C15.3375 2.79794 0.70459 14.3069 0.70459 14.3069H8.95014L0.723908 26.9886L22.2152 11.7358H16.1891C17.9892 9.05502 21.4926 3.82898 22.183 3.08792C22.3056 2.95904 22.4475 2.83661 22.5894 2.7335C23.0733 2.39197 23.3249 2.43064 23.6927 2.91394C23.7379 2.1471 23.7443 1.42537 23.454 0.729425Z"
      fill="url(#paint0_linear_3728_6756)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_3728_6756"
        x1={7.77722}
        y1={27.2622}
        x2={23.2311}
        y2={14.7121}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#FF7F02" />
        <Stop offset={1} stopColor="#FFC400" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default React.memo(ThunderSvg);
