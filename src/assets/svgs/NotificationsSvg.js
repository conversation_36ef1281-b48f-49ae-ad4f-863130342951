import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const NotificationsSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 14 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M13.2777 13.0501L11.7814 10.5976V7.12135C11.8346 6.19705 11.6252 5.27657 11.1772 4.46631C10.7293 3.65606 10.0612 2.98912 9.25017 2.5426C9.26102 2.40031 9.26102 2.25739 9.25017 2.1151C9.24144 1.82845 9.17631 1.54632 9.05848 1.28486C8.94066 1.0234 8.77246 0.787716 8.5635 0.591294C8.35454 0.394871 8.10892 0.241558 7.84067 0.140117C7.57243 0.0386757 7.28682 -0.00890343 7.00017 9.90801e-05C6.71353 -0.00890343 6.42792 0.0386757 6.15968 0.140117C5.89143 0.241558 5.64581 0.394871 5.43685 0.591294C5.22789 0.787716 5.05969 1.0234 4.94186 1.28486C4.82404 1.54632 4.7589 1.82845 4.75017 2.1151C4.73319 2.25709 4.73319 2.4006 4.75017 2.5426C3.93914 2.98912 3.27104 3.65606 2.82311 4.46631C2.37518 5.27657 2.16571 6.19705 2.21892 7.12135V10.5976L0.722673 13.0501C0.593246 13.2627 0.522559 13.5058 0.517832 13.7547C0.513105 14.0035 0.574506 14.2492 0.695766 14.4665C0.817026 14.6839 0.993799 14.8651 1.20803 14.9918C1.42227 15.1185 1.66629 15.1861 1.91517 15.1876H3.90642C3.95847 15.9654 4.30976 16.6928 4.88659 17.2172C5.46341 17.7416 6.22093 18.0222 7.00017 18.0001C7.77942 18.0222 8.53693 17.7416 9.11376 17.2172C9.69058 16.6928 10.0419 15.9654 10.0939 15.1876H12.0852C12.3341 15.1861 12.5781 15.1185 12.7923 14.9918C13.0065 14.8651 13.1833 14.6839 13.3046 14.4665C13.4258 14.2492 13.4872 14.0035 13.4825 13.7547C13.4778 13.5058 13.4071 13.2627 13.2777 13.0501ZM6.15642 2.1151C6.17642 1.91008 6.27596 1.72106 6.43369 1.58857C6.59142 1.45608 6.79478 1.39066 7.00017 1.40635C7.20557 1.39066 7.40893 1.45608 7.56666 1.58857C7.72439 1.72106 7.82392 1.91008 7.84392 2.1151C7.56295 2.0975 7.28115 2.0975 7.00017 2.1151C6.7192 2.0975 6.4374 2.0975 6.15642 2.1151ZM7.00017 16.5938C6.59367 16.6143 6.19436 16.481 5.88168 16.2204C5.569 15.9598 5.36586 15.5911 5.31267 15.1876H8.68767C8.63448 15.5911 8.43134 15.9598 8.11866 16.2204C7.80598 16.481 7.40668 16.6143 7.00017 16.5938ZM1.91517 13.7813L3.42267 11.3401L3.62517 10.9914V7.12135C3.62247 6.27884 3.85634 5.45248 4.30017 4.73635C4.60642 4.31664 5.01593 3.98318 5.48899 3.76833C5.96206 3.55349 6.48261 3.46453 7.00017 3.5101C7.51774 3.46453 8.03829 3.55349 8.51135 3.76833C8.98441 3.98318 9.39393 4.31664 9.70017 4.73635C10.144 5.45248 10.3779 6.27884 10.3752 7.12135V10.9914L10.5777 11.3401L12.0852 13.7813H1.91517Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(NotificationsSvg);
