import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const TrackSvg = props => (
  <Svg
    width={16}
    height={15}
    viewBox="0 0 16 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M0 1.25C0 1.05109 0.0790175 0.860322 0.21967 0.71967C0.360322 0.579018 0.551088 0.5 0.75 0.5H1.75C1.94891 0.5 2.13968 0.579018 2.28033 0.71967C2.42098 0.860322 2.5 1.05109 2.5 1.25C2.5 1.44891 2.42098 1.63968 2.28033 1.78033C2.13968 1.92098 1.94891 2 1.75 2H0.75C0.551088 2 0.360322 1.92098 0.21967 1.78033C0.0790175 1.63968 0 1.44891 0 1.25ZM3.5 1.25C3.5 1.05109 3.57902 0.860322 3.71967 0.71967C3.86032 0.579018 4.05109 0.5 4.25 0.5H5.25C5.44891 0.5 5.63968 0.579018 5.78033 0.71967C5.92098 0.860322 6 1.05109 6 1.25C6 1.44891 5.92098 1.63968 5.78033 1.78033C5.63968 1.92098 5.44891 2 5.25 2H4.25C4.05109 2 3.86032 1.92098 3.71967 1.78033C3.57902 1.63968 3.5 1.44891 3.5 1.25ZM12.878 8.282L13.226 9.353C13.334 9.67886 13.5168 9.97493 13.7597 10.2175C14.0027 10.4601 14.299 10.6424 14.625 10.75L15.718 11.104C15.8006 11.1331 15.8721 11.1871 15.9228 11.2585C15.9734 11.33 16.0005 11.4154 16.0005 11.503C16.0005 11.5906 15.9734 11.676 15.9228 11.7475C15.8721 11.8189 15.8006 11.8729 15.718 11.902L14.646 12.25C14.32 12.3576 14.0237 12.5399 13.7807 12.7825C13.5378 13.0251 13.355 13.3211 13.247 13.647L12.899 14.717C12.8699 14.7996 12.8159 14.8711 12.7445 14.9218C12.673 14.9724 12.5876 14.9995 12.5 14.9995C12.4124 14.9995 12.327 14.9724 12.2555 14.9218C12.1841 14.8711 12.1301 14.7996 12.101 14.717L11.752 13.647C11.6435 13.3209 11.4607 13.0244 11.2181 12.781C10.9755 12.5375 10.6797 12.3537 10.354 12.244L9.283 11.896C9.2004 11.8669 9.12885 11.8129 9.07825 11.7415C9.02764 11.67 9.00046 11.5846 9.00046 11.497C9.00046 11.4094 9.02764 11.324 9.07825 11.2525C9.12885 11.1811 9.2004 11.1271 9.283 11.098L10.354 10.75C10.6759 10.6395 10.9679 10.4559 11.2068 10.2135C11.4458 9.97105 11.6252 9.67651 11.731 9.353L12.079 8.283C12.1079 8.20005 12.1618 8.12814 12.2334 8.07725C12.305 8.02637 12.3907 7.99903 12.4785 7.99903C12.5663 7.99903 12.652 8.02637 12.7236 8.07725C12.7952 8.12814 12.8491 8.20005 12.878 8.283M10.75 2C11.2141 2 11.6592 2.18437 11.9874 2.51256C12.3156 2.84075 12.5 3.28587 12.5 3.75C12.5 4.21413 12.3156 4.65925 11.9874 4.98744C11.6592 5.31563 11.2141 5.5 10.75 5.5H3.25C2.38805 5.5 1.5614 5.84241 0.951903 6.4519C0.34241 7.0614 0 7.88805 0 8.75C0 9.61195 0.34241 10.4386 0.951903 11.0481C1.5614 11.6576 2.38805 12 3.25 12H8.09C8.02928 11.839 7.99877 11.6681 8 11.496C8 11.056 8.204 10.714 8.406 10.5H3.25C2.78587 10.5 2.34075 10.3156 2.01256 9.98744C1.68437 9.65925 1.5 9.21413 1.5 8.75C1.5 8.28587 1.68437 7.84075 2.01256 7.51256C2.34075 7.18437 2.78587 7 3.25 7H10.75C11.612 7 12.4386 6.65759 13.0481 6.0481C13.6576 5.4386 14 4.61195 14 3.75C14 2.88805 13.6576 2.0614 13.0481 1.4519C12.4386 0.84241 11.612 0.5 10.75 0.5H7.75C7.55109 0.5 7.36032 0.579018 7.21967 0.71967C7.07902 0.860322 7 1.05109 7 1.25C7 1.44891 7.07902 1.63968 7.21967 1.78033C7.36032 1.92098 7.55109 2 7.75 2H10.75Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(TrackSvg);
