import * as React from 'react';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';
const VerticalSplitter = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={2}
    height={48}
    viewBox="0 0 2 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M1 48L0.999998 -8.9407e-07"
      stroke="url(#paint0_linear_2275_3873)"
      strokeOpacity={0.4}
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_2275_3873"
        x1={1}
        y1={48}
        x2={1}
        y2={-0.618555}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor={color} stopOpacity={0} />
        <Stop offset={0.5} stopColor={color} />
        <Stop offset={1} stopColor={color} stopOpacity={0} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default React.memo(VerticalSplitter);
