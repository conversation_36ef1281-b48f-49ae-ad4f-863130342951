import * as React from 'react';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';
const Splitter = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={316}
    height={2}
    viewBox="0 0 316 2"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M0 1L316 1"
      stroke="url(#paint0_linear_2211_1667)"
      strokeOpacity={0.4}
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_2211_1667"
        x1={0}
        y1={1}
        x2={320.072}
        y2={1.00028}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor={color} stopOpacity={0} />
        <Stop offset={0.5} stopColor={color} />
        <Stop offset={1} stopColor={color} stopOpacity={0} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default React.memo(Splitter);
