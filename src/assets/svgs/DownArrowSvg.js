import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const DownArrowSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={17}
    height={10}
    viewBox="0 0 17 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M15.8495 0.448722C15.6291 0.228372 15.3301 0.104585 15.0185 0.104585C14.7068 0.104585 14.4079 0.228371 14.1875 0.448722L8.36932 6.26688L2.55116 0.448721C2.32948 0.234615 2.03258 0.116143 1.7244 0.118821C1.41621 0.121499 1.12141 0.245112 0.903487 0.463038C0.685561 0.680964 0.561949 0.975766 0.559271 1.28395C0.556593 1.59213 0.675065 1.88903 0.88917 2.11071L7.53832 8.75987C7.75874 8.98022 8.05765 9.104 8.36932 9.104C8.68099 9.104 8.9799 8.98022 9.20032 8.75987L15.8495 2.11072C16.0698 1.8903 16.1936 1.59139 16.1936 1.27972C16.1936 0.968049 16.0698 0.669139 15.8495 0.448722Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(DownArrowSvg);
