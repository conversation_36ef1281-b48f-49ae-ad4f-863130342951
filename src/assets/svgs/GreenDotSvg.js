import * as React from 'react';
import Svg, {G, Circle, Defs} from 'react-native-svg';
/* SVGR has dropped some elements not supported by react-native-svg: filter */
const GreenDotSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={26}
    height={27}
    viewBox="0 0 26 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <G filter="url(#filter0_d_2312_1454)">
      <Circle
        cx={13}
        cy={13.7998}
        r={4}
        transform="rotate(-180 13 13.7998)"
        fill={color}
      />
    </G>
    <Defs></Defs>
  </Svg>
);
export default GreenDotSvg;
