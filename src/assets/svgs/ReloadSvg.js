import {color} from 'echarts';
import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const ReloadSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M17.0585 2.9405C18.1154 3.98929 18.9235 5.2624 19.4232 6.6658C19.9228 8.06921 20.1012 9.56707 19.9453 11.0488C19.4829 15.641 15.7588 19.4198 11.1723 19.9329C5.11129 20.6211 1.77959e-07 15.9288 4.36667e-07 10.0102C-1.38809e-06 8.12066 0.535473 6.26988 1.54422 4.6729C2.55296 3.07593 3.99356 1.79832 5.69865 0.98851C6.53595 0.588102 7.49822 1.18871 7.49822 2.11466C7.49822 2.57763 7.24828 3.01558 6.83588 3.21578C5.27497 3.94227 4.01157 5.18484 3.25821 6.73448C2.50485 8.28411 2.3075 10.0463 2.69936 11.7245C3.31171 14.5023 5.57368 16.7421 8.34802 17.3302C9.44493 17.5774 10.5833 17.5745 11.679 17.3218C12.7746 17.0691 13.7995 16.573 14.6779 15.8701C15.5562 15.1673 16.2655 14.2758 16.7533 13.2615C17.241 12.2471 17.4948 11.136 17.4958 10.0102C17.4958 7.9331 16.6336 6.08121 15.2714 4.72983L13.3843 6.61926C12.597 7.40756 11.2473 6.857 11.2473 5.74336L11.2473 1.25128C11.2473 0.563076 11.8097 -3.58009e-07 12.497 -3.27965e-07L16.9835 -1.31857e-07C18.0957 -8.32393e-08 18.6581 1.35138 17.8708 2.13968L17.0585 2.9405Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(ReloadSvg);
