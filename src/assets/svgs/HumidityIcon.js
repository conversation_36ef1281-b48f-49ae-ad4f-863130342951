import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const HumidityIcon = props => (
  <Svg
    width={12}
    height={17}
    viewBox="0 0 12 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M10.6725 7.74532L6.52938 1.14782C6.46926 1.06345 6.38986 0.994675 6.29777 0.947218C6.20569 0.89976 6.10359 0.875 6 0.875C5.89641 0.875 5.79431 0.89976 5.70223 0.947218C5.61015 0.994675 5.53074 1.06345 5.47062 1.14782L1.30875 7.7772C0.731037 8.70985 0.408998 9.77814 0.375 10.8747C0.375 12.3665 0.967632 13.7973 2.02252 14.8522C3.07742 15.9071 4.50816 16.4997 6 16.4997C7.49184 16.4997 8.92258 15.9071 9.97748 14.8522C11.0324 13.7973 11.625 12.3665 11.625 10.8747C11.5892 9.76581 11.2605 8.68614 10.6725 7.74532ZM6 15.2497C4.84019 15.248 3.72835 14.7866 2.90823 13.9665C2.08812 13.1464 1.62665 12.0345 1.625 10.8747C1.65886 10.0008 1.92134 9.15107 2.38625 8.41032L2.97062 7.47907L9.26687 13.7753C8.85787 14.2385 8.35512 14.6096 7.79192 14.8638C7.22873 15.1181 6.61794 15.2492 6 15.2497Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(HumidityIcon);
