import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const AnalyzeSvg = props => (
  <Svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M14.6668 7.16679C14.463 5.70041 13.7827 4.34173 12.7307 3.3C11.6787 2.25828 10.3135 1.5913 8.84516 1.40179C7.53395 1.23171 6.20165 1.45046 5.01367 2.0309C3.82568 2.61133 2.83433 3.52789 2.16266 4.66679M1.3335 8.83345C1.52628 10.221 2.14603 11.5143 3.10681 12.5338C4.06758 13.5534 5.32178 14.2487 6.69549 14.5235C8.06921 14.7982 9.49437 14.6387 10.7734 14.0671C12.0524 13.4955 13.1218 12.5402 13.8335 11.3335"
      stroke="#96999E"
      strokeWidth={1.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M13.0002 11.3335C13.0002 11.5545 13.088 11.7665 13.2442 11.9228C13.4005 12.079 13.6125 12.1668 13.8335 12.1668C14.0545 12.1668 14.2665 12.079 14.4228 11.9228C14.579 11.7665 14.6668 11.5545 14.6668 11.3335C14.6668 11.1125 14.579 10.9005 14.4228 10.7442C14.2665 10.588 14.0545 10.5002 13.8335 10.5002C13.6125 10.5002 13.4005 10.588 13.2442 10.7442C13.088 10.9005 13.0002 11.1125 13.0002 11.3335ZM1.3335 4.66683C1.3335 4.88784 1.42129 5.0998 1.57757 5.25608C1.73385 5.41237 1.94582 5.50016 2.16683 5.50016C2.38784 5.50016 2.5998 5.41237 2.75608 5.25608C2.91237 5.0998 3.00016 4.88784 3.00016 4.66683C3.00016 4.44582 2.91237 4.23385 2.75608 4.07757C2.5998 3.92129 2.38784 3.8335 2.16683 3.8335C1.94582 3.8335 1.73385 3.92129 1.57757 4.07757C1.42129 4.23385 1.3335 4.44582 1.3335 4.66683ZM5.50016 8.00016C5.50016 8.6632 5.76355 9.29909 6.2324 9.76793C6.70124 10.2368 7.33712 10.5002 8.00016 10.5002C8.6632 10.5002 9.29909 10.2368 9.76793 9.76793C10.2368 9.29909 10.5002 8.6632 10.5002 8.00016C10.5002 7.33712 10.2368 6.70124 9.76793 6.2324C9.29909 5.76355 8.6632 5.50016 8.00016 5.50016C7.33712 5.50016 6.70124 5.76355 6.2324 6.2324C5.76355 6.70124 5.50016 7.33712 5.50016 8.00016Z"
      stroke="#96999E"
      strokeWidth={1.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(AnalyzeSvg);
