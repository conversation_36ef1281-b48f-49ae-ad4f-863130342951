import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const SolarSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={40}
    height={42}
    viewBox="0 0 40 42"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M13.7708 14.9625C13.7708 13.3613 14.4271 11.8256 15.5953 10.6933C16.7635 9.56109 18.3479 8.925 20 8.925C21.6521 8.925 23.2365 9.56109 24.4047 10.6933C25.5729 11.8256 26.2292 13.3613 26.2292 14.9625M20 21V41.125M36.6111 31.0625H3.38885M20 2.8875V0.875M32.4584 14.9625H34.5348M5.46524 14.9625H7.54164M28.8081 6.42346L30.2761 5.00063M9.72185 5.00063L11.1899 6.42346M26.8666 21H13.1334C10.3967 21 9.03041 21 7.96937 21.6923C6.91041 22.3846 6.4017 23.6102 5.38634 26.0594L3.71692 30.0844C1.64468 35.0875 0.60648 37.589 1.84816 39.356C3.08985 41.123 5.88052 41.125 11.4639 41.125H28.5361C34.1195 41.125 36.9101 41.125 38.1518 39.356C39.3935 37.589 38.3553 35.0875 36.2831 30.0844L34.6137 26.0594C33.5962 23.6102 33.0896 22.3846 32.0306 21.6923C30.9717 21 29.6012 21 26.8666 21Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(SolarSvg);
