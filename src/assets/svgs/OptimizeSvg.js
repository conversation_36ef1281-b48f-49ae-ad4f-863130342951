import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const OptimizeSvg = props => (
  <Svg
    width={17}
    height={17}
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M5.91683 1.3335L9.66683 4.66683L13.8468 2.21266L11.7502 6.75016L15.5002 10.0835L10.5002 9.66683L8.62516 13.8335L7.5835 9.25016L2.5835 8.8335L6.96183 6.18766L5.91683 1.3335Z"
      stroke="#96999E"
      strokeWidth={1.66667}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M1.3335 15.5083L7.5835 9.25"
      stroke="#96999E"
      strokeWidth={1.66667}
      strokeLinecap="round"
    />
  </Svg>
);
export default React.memo(OptimizeSvg);
