import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const HealthSvg = props => (
  <Svg
    width={18}
    height={16}
    viewBox="0 0 18 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M1.271 15.5C1.11822 15.5 0.979332 15.4619 0.854332 15.3858C0.729332 15.3097 0.63211 15.2089 0.562666 15.0833C0.493221 14.9578 0.455166 14.8222 0.448499 14.6767C0.441833 14.5311 0.479888 14.3889 0.562666 14.25L8.271 0.916667C8.35433 0.777778 8.46211 0.673611 8.59433 0.604167C8.72655 0.534722 8.86183 0.5 9.00017 0.5C9.1385 0.5 9.27405 0.534722 9.40683 0.604167C9.53961 0.673611 9.64711 0.777778 9.72933 0.916667L17.4377 14.25C17.521 14.3889 17.5593 14.5314 17.5527 14.6775C17.546 14.8236 17.5077 14.9589 17.4377 15.0833C17.3677 15.2078 17.2704 15.3086 17.146 15.3858C17.0216 15.4631 16.8827 15.5011 16.7293 15.5H1.271ZM2.7085 13.8333H15.2918L9.00017 3L2.7085 13.8333ZM9.00017 13C9.23628 13 9.43433 12.92 9.59433 12.76C9.75433 12.6 9.83405 12.4022 9.8335 12.1667C9.83294 11.9311 9.75294 11.7333 9.5935 11.5733C9.43405 11.4133 9.23628 11.3333 9.00017 11.3333C8.76406 11.3333 8.56628 11.4133 8.40683 11.5733C8.24739 11.7333 8.16739 11.9311 8.16683 12.1667C8.16628 12.4022 8.24628 12.6003 8.40683 12.7608C8.56739 12.9214 8.76517 13.0011 9.00017 13ZM9.00017 10.5C9.23628 10.5 9.43433 10.42 9.59433 10.26C9.75433 10.1 9.83405 9.90222 9.8335 9.66667V7.16667C9.8335 6.93056 9.7535 6.73278 9.5935 6.57333C9.4335 6.41389 9.23572 6.33389 9.00017 6.33333C8.76461 6.33278 8.56683 6.41278 8.40683 6.57333C8.24683 6.73389 8.16683 6.93167 8.16683 7.16667V9.66667C8.16683 9.90278 8.24683 10.1008 8.40683 10.2608C8.56683 10.4208 8.76461 10.5006 9.00017 10.5Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(HealthSvg);
