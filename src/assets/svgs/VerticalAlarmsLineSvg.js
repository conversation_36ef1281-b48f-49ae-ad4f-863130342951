import * as React from 'react';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';
const VerticalAlarmsLineSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={290}
    height={2}
    viewBox="0 0 193 2"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path d="M0 1L193 0.999983" stroke="url(#paint0_linear_2815_1806)" />
    <Defs>
      <LinearGradient
        id="paint0_linear_2815_1806"
        x1={0}
        y1={1}
        x2={195.487}
        y2={1.00009}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor={color} />
        <Stop offset={1} stopColor={color} stopOpacity={0} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default VerticalAlarmsLineSvg;
