import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const ControlSvg = props => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M13.125 10C13.125 9.38193 12.9417 8.77775 12.5983 8.26384C12.255 7.74994 11.7669 7.3494 11.1959 7.11288C10.6249 6.87635 9.99653 6.81447 9.39034 6.93505C8.78415 7.05562 8.22733 7.35325 7.79029 7.79029C7.35325 8.22733 7.05562 8.78415 6.93505 9.39034C6.81447 9.99653 6.87635 10.6249 7.11288 11.1959C7.3494 11.7669 7.74994 12.255 8.26384 12.5983C8.77775 12.9417 9.38193 13.125 10 13.125C10.8285 13.1241 11.6228 12.7945 12.2087 12.2087C12.7945 11.6228 13.1241 10.8285 13.125 10ZM8.125 10C8.125 9.62916 8.23497 9.26665 8.44099 8.95831C8.64702 8.64996 8.93986 8.40964 9.28247 8.26773C9.62508 8.12581 10.0021 8.08868 10.3658 8.16103C10.7295 8.23337 11.0636 8.41195 11.3258 8.67417C11.588 8.9364 11.7666 9.27049 11.839 9.63421C11.9113 9.99792 11.8742 10.3749 11.7323 10.7175C11.5904 11.0601 11.35 11.353 11.0417 11.559C10.7334 11.765 10.3708 11.875 10 11.875C9.50289 11.8744 9.02631 11.6767 8.6748 11.3252C8.3233 10.9737 8.12557 10.4971 8.125 10ZM6.25 15.5464L9.5823 19.375H10.4177L13.75 15.5464V14.375H6.25V15.5464ZM12.0244 15.625L10 17.9509L7.97559 15.625H12.0244ZM9.5823 0.625L6.25 4.45359V5.625H13.75V4.45359L10.4177 0.625H9.5823ZM7.97559 4.375L10 2.04906L12.0244 4.375H7.97559ZM0.625 9.5823V10.4177L4.45363 13.75H5.625V6.25H4.45363L0.625 9.5823ZM4.375 12.0245L2.04902 10L4.375 7.97555V12.0245ZM15.5464 6.25H14.375V13.75H15.5464L19.375 10.4177V9.5823L15.5464 6.25ZM15.625 12.0245V7.97555L17.951 10L15.625 12.0245Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(ControlSvg);
