import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const HidePassword = props => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={21}
    fill="none"
    {...props}>
    <Path
      fill="#A8B9CB"
      d="M14.894 16.691A8.98 8.98 0 0 1 10 18.136c-4.486 0-8.218-3.292-9-7.636a9.387 9.387 0 0 1 2.777-5.147L1.177 2.7l1.176-1.2 16.47 16.8-1.176 1.2-2.753-2.809ZM4.954 6.554A7.646 7.646 0 0 0 2.699 10.5a7.703 7.703 0 0 0 1.523 3.158 7.509 7.509 0 0 0 2.75 2.127 7.364 7.364 0 0 0 6.709-.332l-1.687-1.72a3.686 3.686 0 0 1-4.643-.531 3.88 3.88 0 0 1-.52-4.735L4.955 6.553Zm5.806 5.921-2.697-2.75a2.161 2.161 0 0 0-.1 1.209c.082.404.279.775.565 1.067.286.292.65.492 1.046.576.396.085.81.049 1.186-.102Zm6.565 1.922-1.19-1.214a7.626 7.626 0 0 0 1.167-2.683 7.714 7.714 0 0 0-1.25-2.8 7.548 7.548 0 0 0-2.23-2.07 7.35 7.35 0 0 0-5.859-.782L6.65 3.507A8.984 8.984 0 0 1 10 2.863c4.485 0 8.217 3.292 9 7.637a9.388 9.388 0 0 1-1.675 3.897ZM9.77 6.689a3.68 3.68 0 0 1 1.716.306c.47.207.892.51 1.243.89.35.379.62.828.793 1.318.174.49.247 1.012.215 1.533L9.77 6.689Z"
    />
  </Svg>
);
export default React.memo(HidePassword);
