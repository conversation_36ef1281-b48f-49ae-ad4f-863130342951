import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const WindSvg = props => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.20768 3.58333C4.20768 3.04768 4.36652 2.52405 4.66412 2.07867C4.96171 1.63328 5.3847 1.28615 5.87958 1.08116C6.37446 0.876173 6.91902 0.822539 7.44439 0.927041C7.96975 1.03154 8.45233 1.28949 8.8311 1.66825C9.20986 2.04702 9.46781 2.5296 9.57231 3.05496C9.67681 3.58033 9.62318 4.12489 9.41819 4.61977C9.2132 5.11465 8.86607 5.53764 8.42068 5.83523C7.9753 6.13283 7.45167 6.29167 6.91602 6.29167H1.49935C1.33359 6.29167 1.17462 6.22582 1.05741 6.10861C0.940197 5.9914 0.874349 5.83243 0.874349 5.66667C0.874349 5.50091 0.940197 5.34194 1.05741 5.22473C1.17462 5.10752 1.33359 5.04167 1.49935 5.04167H6.91602C7.20445 5.04167 7.4864 4.95614 7.72622 4.79589C7.96604 4.63565 8.15296 4.40789 8.26334 4.14141C8.37372 3.87494 8.4026 3.58172 8.34633 3.29883C8.29006 3.01594 8.15117 2.75609 7.94721 2.55214C7.74326 2.34819 7.48341 2.20929 7.20052 2.15302C6.91763 2.09675 6.62441 2.12563 6.35794 2.23601C6.09146 2.34639 5.8637 2.53331 5.70346 2.77313C5.54321 3.01295 5.45768 3.2949 5.45768 3.58333V3.88083C5.45768 4.04659 5.39183 4.20557 5.27462 4.32278C5.15741 4.43999 4.99844 4.50583 4.83268 4.50583C4.66692 4.50583 4.50795 4.43999 4.39074 4.32278C4.27353 4.20557 4.20768 4.04659 4.20768 3.88083V3.58333ZM10.8743 5.25C10.8743 4.54953 11.0821 3.86478 11.4712 3.28236C11.8604 2.69993 12.4135 2.24599 13.0607 1.97793C13.7078 1.70987 14.4199 1.63973 15.107 1.77639C15.794 1.91304 16.425 2.25035 16.9204 2.74566C17.4157 3.24098 17.753 3.87204 17.8896 4.55906C18.0263 5.24607 17.9562 5.95818 17.6881 6.60534C17.42 7.25249 16.9661 7.80563 16.3837 8.19479C15.8012 8.58395 15.1165 8.79167 14.416 8.79167H0.666016C0.500255 8.79167 0.341284 8.72582 0.224074 8.60861C0.106864 8.4914 0.0410156 8.33243 0.0410156 8.16667C0.0410156 8.00091 0.106864 7.84194 0.224074 7.72473C0.341284 7.60752 0.500255 7.54167 0.666016 7.54167H14.416C14.8693 7.54167 15.3123 7.40726 15.6892 7.15545C16.0661 6.90364 16.3598 6.54573 16.5332 6.12698C16.7067 5.70824 16.7521 5.24746 16.6636 4.80292C16.5752 4.35838 16.357 3.95004 16.0365 3.62955C15.716 3.30905 15.3076 3.09079 14.8631 3.00237C14.4186 2.91394 13.9578 2.95933 13.539 3.13278C13.1203 3.30623 12.7624 3.59996 12.5106 3.97682C12.2588 4.35368 12.1243 4.79675 12.1243 5.25V5.66667C12.1243 5.83243 12.0585 5.9914 11.9413 6.10861C11.8241 6.22582 11.6651 6.29167 11.4993 6.29167C11.3336 6.29167 11.1746 6.22582 11.0574 6.10861C10.9402 5.9914 10.8743 5.83243 10.8743 5.66667V5.25ZM1.70768 10.6667C1.70768 10.5009 1.77353 10.3419 1.89074 10.2247C2.00795 10.1075 2.16692 10.0417 2.33268 10.0417H14.416C15.1165 10.0417 15.8012 10.2494 16.3837 10.6385C16.9661 11.0277 17.42 11.5808 17.6881 12.228C17.9562 12.8752 18.0263 13.5873 17.8896 14.2743C17.753 14.9613 17.4157 15.5924 16.9204 16.0877C16.425 16.583 15.794 16.9203 15.107 17.0569C14.4199 17.1936 13.7078 17.1235 13.0607 16.8554C12.4135 16.5873 11.8604 16.1334 11.4712 15.551C11.0821 14.9686 10.8743 14.2838 10.8743 13.5833V13.1667C10.8743 13.0009 10.9402 12.8419 11.0574 12.7247C11.1746 12.6075 11.3336 12.5417 11.4993 12.5417C11.6651 12.5417 11.8241 12.6075 11.9413 12.7247C12.0585 12.8419 12.1243 13.0009 12.1243 13.1667V13.5833C12.1243 14.0366 12.2588 14.4797 12.5106 14.8565C12.7624 15.2334 13.1203 15.5271 13.539 15.7006C13.9578 15.874 14.4186 15.9194 14.8631 15.831C15.3076 15.7425 15.716 15.5243 16.0365 15.2038C16.357 14.8833 16.5752 14.475 16.6636 14.0304C16.7521 13.5859 16.7067 13.1251 16.5332 12.7064C16.3598 12.2876 16.0661 11.9297 15.6892 11.6779C15.3123 11.4261 14.8693 11.2917 14.416 11.2917H2.33268C2.16692 11.2917 2.00795 11.2258 1.89074 11.1086C1.77353 10.9914 1.70768 10.8324 1.70768 10.6667Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(WindSvg);
