import * as React from 'react';
import Svg, {
  G,
  <PERSON>,
  Defs,
  <PERSON>arGradient,
  Stop,
  ClipPath,
  Rect,
} from 'react-native-svg';
const SunsetCardSvg = props => (
  <Svg
    width={172}
    height={93}
    viewBox="0 0 152 83"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <G clipPath="url(#clip0_5756_3236)">
      <Path
        d="M18.8889 36.1111C18.8889 36.1111 18.8889 35 20 35C21.1111 35 21.1111 36.1111 21.1111 36.1111V37.2222C21.1111 37.2222 21.1111 38.3333 20 38.3333C18.8889 38.3333 18.8889 37.2222 18.8889 37.2222V36.1111ZM28.8889 43.8889C28.8889 43.8889 30 43.8889 30 45C30 46.1111 28.8889 46.1111 28.8889 46.1111H27.7778C27.7778 46.1111 26.6667 46.1111 26.6667 45C26.6667 43.8889 27.7778 43.8889 27.7778 43.8889H28.8889ZM12.2222 43.8889C12.2222 43.8889 13.3333 43.8889 13.3333 45C13.3333 46.1111 12.2222 46.1111 12.2222 46.1111H11.1111C11.1111 46.1111 10 46.1111 10 45C10 43.8889 11.1111 43.8889 11.1111 43.8889H12.2222ZM15.0672 39.0517C15.0672 39.0517 15.8528 39.8372 15.0672 40.6228C14.2817 41.4083 13.4961 40.6228 13.4961 40.6228L12.71 39.8378C12.71 39.8378 11.9244 39.0522 12.71 38.2661C13.4961 37.4806 14.2817 38.2661 14.2817 38.2661L15.0672 39.0517ZM26.7339 50.7183C26.7339 50.7183 27.5194 51.5039 26.7339 52.2894C25.9483 53.075 25.1628 52.2894 25.1628 52.2894L24.3772 51.5039C24.3772 51.5039 23.5917 50.7183 24.3772 49.9328C25.1628 49.1472 25.9483 49.9328 25.9483 49.9328L26.7339 50.7183ZM26.5044 40.6228C26.5044 40.6228 25.7189 41.4083 24.9333 40.6228C24.1478 39.8372 24.9333 39.0517 24.9333 39.0517L25.7189 38.2661C25.7189 38.2661 26.5044 37.4806 27.29 38.2661C28.0756 39.0517 27.29 39.8372 27.29 39.8372L26.5044 40.6228ZM14.8378 52.2894C14.8378 52.2894 14.0522 53.075 13.2667 52.2894C12.4811 51.5039 13.2667 50.7183 13.2667 50.7183L14.0522 49.9328C14.0522 49.9328 14.8378 49.1472 15.6233 49.9328C16.4089 50.7183 15.6233 51.5039 15.6233 51.5039L14.8378 52.2894ZM18.8889 52.7778C18.8889 52.7778 18.8889 51.6667 20 51.6667C21.1111 51.6667 21.1111 52.7778 21.1111 52.7778V53.8889C21.1111 53.8889 21.1111 55 20 55C18.8889 55 18.8889 53.8889 18.8889 53.8889V52.7778Z"
        fill="#FFAC33"
      />
      <Path
        d="M20.0009 50.5554C23.0691 50.5554 25.5564 48.0681 25.5564 44.9999C25.5564 41.9316 23.0691 39.4443 20.0009 39.4443C16.9326 39.4443 14.4453 41.9316 14.4453 44.9999C14.4453 48.0681 16.9326 50.5554 20.0009 50.5554Z"
        fill="#FFAC33"
      />
    </G>
    <G clipPath="url(#clip1_5756_3236)">
      <Path
        d="M126.889 40.1111C126.889 40.1111 126.889 39 128 39C129.111 39 129.111 40.1111 129.111 40.1111V41.2222C129.111 41.2222 129.111 42.3333 128 42.3333C126.889 42.3333 126.889 41.2222 126.889 41.2222V40.1111ZM136.889 47.8889C136.889 47.8889 138 47.8889 138 49C138 50.1111 136.889 50.1111 136.889 50.1111H135.778C135.778 50.1111 134.667 50.1111 134.667 49C134.667 47.8889 135.778 47.8889 135.778 47.8889H136.889ZM120.222 47.8889C120.222 47.8889 121.333 47.8889 121.333 49C121.333 50.1111 120.222 50.1111 120.222 50.1111H119.111C119.111 50.1111 118 50.1111 118 49C118 47.8889 119.111 47.8889 119.111 47.8889H120.222ZM123.067 43.0517C123.067 43.0517 123.853 43.8372 123.067 44.6228C122.282 45.4083 121.496 44.6228 121.496 44.6228L120.71 43.8378C120.71 43.8378 119.924 43.0522 120.71 42.2661C121.496 41.4806 122.282 42.2661 122.282 42.2661L123.067 43.0517ZM134.734 54.7183C134.734 54.7183 135.519 55.5039 134.734 56.2894C133.948 57.075 133.163 56.2894 133.163 56.2894L132.377 55.5039C132.377 55.5039 131.592 54.7183 132.377 53.9328C133.163 53.1472 133.948 53.9328 133.948 53.9328L134.734 54.7183ZM134.504 44.6228C134.504 44.6228 133.719 45.4083 132.933 44.6228C132.148 43.8372 132.933 43.0517 132.933 43.0517L133.719 42.2661C133.719 42.2661 134.504 41.4806 135.29 42.2661C136.076 43.0517 135.29 43.8372 135.29 43.8372L134.504 44.6228ZM122.838 56.2894C122.838 56.2894 122.052 57.075 121.267 56.2894C120.481 55.5039 121.267 54.7183 121.267 54.7183L122.052 53.9328C122.052 53.9328 122.838 53.1472 123.623 53.9328C124.409 54.7183 123.623 55.5039 123.623 55.5039L122.838 56.2894ZM126.889 56.7778C126.889 56.7778 126.889 55.6667 128 55.6667C129.111 55.6667 129.111 56.7778 129.111 56.7778V57.8889C129.111 57.8889 129.111 59 128 59C126.889 59 126.889 57.8889 126.889 57.8889V56.7778Z"
        fill="#FFAC33"
      />
      <Path
        d="M128.001 54.5554C131.069 54.5554 133.556 52.0681 133.556 48.9999C133.556 45.9316 131.069 43.4443 128.001 43.4443C124.933 43.4443 122.445 45.9316 122.445 48.9999C122.445 52.0681 124.933 54.5554 128.001 54.5554Z"
        fill="#FFAC33"
      />
    </G>
    <G clipPath="url(#clip2_5756_3236)">
      <Path
        d="M73.8889 3.11111C73.8889 3.11111 73.8889 2 75 2C76.1111 2 76.1111 3.11111 76.1111 3.11111V4.22222C76.1111 4.22222 76.1111 5.33333 75 5.33333C73.8889 5.33333 73.8889 4.22222 73.8889 4.22222V3.11111ZM83.8889 10.8889C83.8889 10.8889 85 10.8889 85 12C85 13.1111 83.8889 13.1111 83.8889 13.1111H82.7778C82.7778 13.1111 81.6667 13.1111 81.6667 12C81.6667 10.8889 82.7778 10.8889 82.7778 10.8889H83.8889ZM67.2222 10.8889C67.2222 10.8889 68.3333 10.8889 68.3333 12C68.3333 13.1111 67.2222 13.1111 67.2222 13.1111H66.1111C66.1111 13.1111 65 13.1111 65 12C65 10.8889 66.1111 10.8889 66.1111 10.8889H67.2222ZM70.0672 6.05167C70.0672 6.05167 70.8528 6.83722 70.0672 7.62278C69.2817 8.40833 68.4961 7.62278 68.4961 7.62278L67.71 6.83778C67.71 6.83778 66.9244 6.05222 67.71 5.26611C68.4961 4.48056 69.2817 5.26611 69.2817 5.26611L70.0672 6.05167ZM81.7339 17.7183C81.7339 17.7183 82.5194 18.5039 81.7339 19.2894C80.9483 20.075 80.1628 19.2894 80.1628 19.2894L79.3772 18.5039C79.3772 18.5039 78.5917 17.7183 79.3772 16.9328C80.1628 16.1472 80.9483 16.9328 80.9483 16.9328L81.7339 17.7183ZM81.5044 7.62278C81.5044 7.62278 80.7189 8.40833 79.9333 7.62278C79.1478 6.83722 79.9333 6.05167 79.9333 6.05167L80.7189 5.26611C80.7189 5.26611 81.5044 4.48056 82.29 5.26611C83.0756 6.05167 82.29 6.83722 82.29 6.83722L81.5044 7.62278ZM69.8378 19.2894C69.8378 19.2894 69.0522 20.075 68.2667 19.2894C67.4811 18.5039 68.2667 17.7183 68.2667 17.7183L69.0522 16.9328C69.0522 16.9328 69.8378 16.1472 70.6233 16.9328C71.4089 17.7183 70.6233 18.5039 70.6233 18.5039L69.8378 19.2894ZM73.8889 19.7778C73.8889 19.7778 73.8889 18.6667 75 18.6667C76.1111 18.6667 76.1111 19.7778 76.1111 19.7778V20.8889C76.1111 20.8889 76.1111 22 75 22C73.8889 22 73.8889 20.8889 73.8889 20.8889V19.7778Z"
        fill="#FFAC33"
      />
      <Path
        d="M75.0009 17.5554C78.0691 17.5554 80.5564 15.0681 80.5564 11.9999C80.5564 8.93164 78.0691 6.44434 75.0009 6.44434C71.9326 6.44434 69.4453 8.93164 69.4453 11.9999C69.4453 15.0681 71.9326 17.5554 75.0009 17.5554Z"
        fill="#FFAC33"
      />
    </G>
    <Path
      d="M60 11L54.8877 8.31709L55.1204 14.0859L60 11ZM20 32L20.4417 32.2343C23.8391 25.8294 35.4248 13.3228 55.543 11.6797L55.5023 11.1814L55.4616 10.6831C34.9537 12.358 23.0906 25.1065 19.5583 31.7657L20 32Z"
      fill="url(#paint0_linear_5756_3236)"
    />
    <Path
      d="M127.102 34.208L126.835 28.4407L121.973 31.5556L127.102 34.208ZM87 11.75L87.0428 12.2482C94.9511 11.5679 112.755 14.4855 124.264 30.706L124.672 30.4167L125.08 30.1273C113.312 13.5426 95.1161 10.55 86.9571 11.2518L87 11.75Z"
      fill="url(#paint1_linear_5756_3236)"
    />
    <Path
      d="M11.4295 67.1473C7.28781 69.2435 2.41746 69.4987 0.5 69.3644V74H151.5V48C142.296 48 141.721 49.6124 133.955 51.6279C126.19 53.6434 128.49 48.4031 112.384 49.6124C96.2771 50.8217 101.454 54.8528 88.799 54.8528C76.1438 54.8528 83.6219 56.6667 69.241 60.4962C54.86 64.3256 53.7095 60.0931 43.3552 63.1163C33.001 66.1396 34.7267 62.5117 26.961 65.3334C19.1952 68.1551 16.6067 64.5272 11.4295 67.1473Z"
      fill="url(#paint2_linear_5756_3236)"
    />
    <Path
      d="M140.07 68.1939C144.212 71.4993 149.083 71.9019 151 71.69V83H0V38C9.20381 38 9.77904 40.5427 17.5448 43.721C25.3105 46.8993 23.0095 38.6357 39.1162 40.5427C55.2229 42.4497 53.0457 45.8063 65.701 45.8063C78.3562 45.8063 70.8781 48.6667 85.259 54.7055C99.64 60.7442 100.79 54.0698 111.145 58.8373C121.499 63.6047 116.773 60.8838 124.539 65.3334C132.305 69.783 134.893 64.0621 140.07 68.1939Z"
      fill="url(#paint3_linear_5756_3236)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_5756_3236"
        x1={60}
        y1={11}
        x2={22.1652}
        y2={35.6531}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#7D5228" />
        <Stop offset={1} stopColor="#1A1E27" />
      </LinearGradient>
      <LinearGradient
        id="paint1_linear_5756_3236"
        x1={127.102}
        y1={34.208}
        x2={87.183}
        y2={11.067}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#7D5228" />
        <Stop offset={1} stopColor="#1A1E27" />
      </LinearGradient>
      <LinearGradient
        id="paint2_linear_5756_3236"
        x1={151.5}
        y1={48}
        x2={151.494}
        y2={70.5789}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#1A366E" />
        <Stop offset={1} stopColor="#1A1E27" />
      </LinearGradient>
      <LinearGradient
        id="paint3_linear_5756_3236"
        x1={0}
        y1={38}
        x2={0.0726831}
        y2={84.4999}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#554A3F" />
        <Stop offset={1} stopColor="#1A1E27" />
      </LinearGradient>
      <ClipPath id="clip0_5756_3236">
        <Rect
          width={20}
          height={20}
          fill="white"
          transform="translate(10 35)"
        />
      </ClipPath>
      <ClipPath id="clip1_5756_3236">
        <Rect
          width={20}
          height={20}
          fill="white"
          transform="translate(118 39)"
        />
      </ClipPath>
      <ClipPath id="clip2_5756_3236">
        <Rect width={20} height={20} fill="white" transform="translate(65 2)" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default React.memo(SunsetCardSvg);
