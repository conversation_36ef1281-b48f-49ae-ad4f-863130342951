import * as React from 'react';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';
const FillingContainer = ({width = 170, height = 0, ...props}) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 151 96" // Keep original dimensions
    preserveAspectRatio="none" // This makes it stretch to fill container
    {...props}>
    <Path
      d="M151 5.00204V88C151 92.4183 147.418 96 143 96H8C3.58172 96 0 92.4183 0 88V9.63853C0 7.07787 2.07583 5.00204 4.6365 5.00204V5.00204C6.0251 5.00204 7.32897 4.3519 8.44322 3.52325C10.6515 1.88098 14.6125 0 20.1518 0C27.5295 0 32.1075 3.33673 33.4735 5.00204C35.0672 6.77755 39.3498 9.26939 43.7186 5.00204C45.0846 3.33673 49.6626 0 57.0403 0C64.4181 0 68.996 3.33061 70.3621 4.99592C71.9552 6.77086 76.2357 9.26168 80.603 4.99999C80.6056 4.99744 80.6092 4.99592 80.6129 4.99592V4.99592C80.617 4.99592 80.6208 4.99427 80.6235 4.99103C81.9944 3.32411 86.5707 0 93.9412 0C101.319 0 105.897 3.33061 107.263 4.99592C108.856 6.77077 113.13 9.25529 117.497 5.01291C117.5 5.00995 117.502 5.00577 117.502 5.00152V5.00152C117.502 4.9979 117.503 4.99458 117.505 4.99177C118.863 3.33119 123.44 0 130.811 0C138.189 0 142.767 3.33061 144.133 4.99592L144.136 4.99899C144.137 5.00012 144.139 4.99932 144.139 4.99772V4.99772C144.139 4.99672 144.14 4.99592 144.141 4.99592H150.994C150.997 4.99592 151 4.99866 151 5.00204V5.00204Z"
      fill="url(#paint0_linear_5739_3149)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_5739_3149"
        x1={75.5}
        y1={0.00000832406}
        x2={75.5}
        y2={96}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#FF7F02" />
        <Stop offset={1} stopColor="#FF7F02" stopOpacity={0} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default React.memo(FillingContainer);
