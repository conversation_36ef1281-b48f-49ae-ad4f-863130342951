import * as React from 'react';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';
const AddSiteSplitterSvg = props => (
  <Svg
    width={342}
    height={2}
    viewBox="0 0 342 2"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M1 1L341 1.00003"
      stroke="url(#paint0_linear_3767_16447)"
      strokeLinecap="round"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_3767_16447"
        x1={1}
        y1={1.5}
        x2={341}
        y2={1.50003}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="white" stopOpacity={0} />
        <Stop offset={0.5} stopColor="white" />
        <Stop offset={1} stopColor="white" stopOpacity={0} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default React.memo(AddSiteSplitterSvg);
