import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const CrticalSvg = ({width = 19, height = 17, ...props}) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 19 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M0.963529 16.375C0.803112 16.375 0.657279 16.335 0.526029 16.2551C0.394779 16.1752 0.292695 16.0693 0.219779 15.9375C0.146862 15.8057 0.106904 15.6633 0.0999037 15.5105C0.0929037 15.3577 0.132862 15.2083 0.219779 15.0625L8.31353 1.0625C8.40103 0.916667 8.5142 0.807292 8.65303 0.734375C8.79186 0.661458 8.9339 0.625 9.07915 0.625C9.2244 0.625 9.36674 0.661458 9.50615 0.734375C9.64557 0.807292 9.75845 0.916667 9.84478 1.0625L17.9385 15.0625C18.026 15.2083 18.0663 15.358 18.0593 15.5114C18.0523 15.6648 18.012 15.8068 17.9385 15.9375C17.865 16.0682 17.7629 16.174 17.6323 16.2551C17.5016 16.3362 17.3558 16.3762 17.1948 16.375H0.963529ZM2.4729 14.625H15.6854L9.07915 3.25L2.4729 14.625ZM9.07915 13.75C9.32707 13.75 9.53503 13.666 9.70303 13.498C9.87103 13.33 9.95474 13.1223 9.95415 12.875C9.95357 12.6277 9.86957 12.42 9.70215 12.252C9.53474 12.084 9.32707 12 9.07915 12C8.83124 12 8.62357 12.084 8.45615 12.252C8.28874 12.42 8.20474 12.6277 8.20415 12.875C8.20357 13.1223 8.28757 13.3303 8.45615 13.4989C8.62474 13.6675 8.8324 13.7512 9.07915 13.75ZM9.07915 11.125C9.32707 11.125 9.53503 11.041 9.70303 10.873C9.87103 10.705 9.95474 10.4973 9.95415 10.25V7.625C9.95415 7.37708 9.87015 7.16942 9.70215 7.002C9.53415 6.83458 9.32649 6.75058 9.07915 6.75C8.83182 6.74942 8.62415 6.83342 8.45615 7.002C8.28815 7.17058 8.20415 7.37825 8.20415 7.625V10.25C8.20415 10.4979 8.28815 10.7059 8.45615 10.8739C8.62415 11.0419 8.83182 11.1256 9.07915 11.125Z"
      fill="#FF5555"
    />
  </Svg>
);
export default React.memo(CrticalSvg);
