import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const InventorySvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M3.75 3.75H5.25V6H12.75V3.75H14.25V7.5H15.75V3.75C15.75 2.925 15.075 2.25 14.25 2.25H11.115C10.8 1.38 9.975 0.75 9 0.75C8.025 0.75 7.2 1.38 6.885 2.25H3.75C2.925 2.25 2.25 2.925 2.25 3.75V14.25C2.25 15.075 2.925 15.75 3.75 15.75H8.25V14.25H3.75V3.75ZM9 2.25C9.4125 2.25 9.75 2.5875 9.75 3C9.75 3.4125 9.4125 3.75 9 3.75C8.5875 3.75 8.25 3.4125 8.25 3C8.25 2.5875 8.5875 2.25 9 2.25Z"
      fill={color}
    />
    <Path
      d="M15.75 8.625L11.6325 12.75L9.375 10.5L8.25 11.625L11.6325 15L16.875 9.75L15.75 8.625Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(InventorySvg);
