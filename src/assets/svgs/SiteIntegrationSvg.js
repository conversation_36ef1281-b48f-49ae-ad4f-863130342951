import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const SiteIntegrationSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M12.6562 16.875C11.5706 16.875 10.6875 15.9919 10.6875 14.9063C10.6875 14.3803 10.8923 13.8864 11.2635 13.5146L12.0594 14.31C11.9152 14.471 11.8382 14.6811 11.8442 14.8971C11.8502 15.1132 11.9387 15.3187 12.0916 15.4714C12.2445 15.6242 12.4501 15.7125 12.6661 15.7183C12.8822 15.7241 13.0922 15.6469 13.2531 15.5025L16.0656 12.69C16.2109 12.5292 16.2888 12.3186 16.2833 12.1019C16.2777 11.8852 16.1891 11.6789 16.0357 11.5258C15.8823 11.3726 15.6759 11.2842 15.4592 11.279C15.2425 11.2737 15.032 11.352 14.8714 11.4975L14.0754 10.7027C14.2579 10.519 14.4751 10.3734 14.7142 10.2743C14.9534 10.1751 15.2098 10.1244 15.4688 10.125C16.5544 10.125 17.4375 11.0081 17.4375 12.0938C17.4375 12.6197 17.2328 13.1141 16.8609 13.4859L14.0484 16.2984C13.866 16.4818 13.649 16.6271 13.41 16.7261C13.1711 16.825 12.9149 16.8756 12.6562 16.875Z"
      fill={color}
    />
    <Path
      d="M9.28125 17.4375C8.19562 17.4375 7.3125 16.5544 7.3125 15.4688C7.3125 14.9428 7.51725 14.4484 7.88906 14.0766L10.7016 11.2641C10.884 11.0808 11.101 10.9354 11.34 10.8365C11.5789 10.7375 11.8351 10.6869 12.0938 10.6875C13.1794 10.6875 14.0625 11.5706 14.0625 12.6562C14.0625 13.1822 13.8577 13.6761 13.4865 14.0479L12.69 13.2525C12.8342 13.0915 12.9112 12.8814 12.9052 12.6654C12.8993 12.4494 12.8107 12.2438 12.6578 12.0911C12.5049 11.9383 12.2993 11.85 12.0833 11.8442C11.8673 11.8384 11.6572 11.9157 11.4964 12.06L8.68388 14.8725C8.53855 15.0333 8.46061 15.2439 8.46617 15.4606C8.47174 15.6773 8.56039 15.8836 8.71378 16.0367C8.86717 16.1899 9.07356 16.2783 9.29027 16.2835C9.50698 16.2888 9.71742 16.2106 9.87806 16.065L10.674 16.8598C10.4916 17.0434 10.2745 17.189 10.0355 17.2881C9.79642 17.3873 9.54006 17.4381 9.28125 17.4375ZM9 1.125C6.01988 1.125 2.8125 1.82925 2.8125 3.375V13.5C2.8125 14.445 4.01288 15.0739 5.625 15.4209V14.2701C4.50338 14.004 3.96844 13.6271 3.9375 13.5V11.4907C4.77787 11.9121 6.00413 12.1742 7.3125 12.2951V11.1713C5.13112 10.9592 4.00669 10.3714 3.9375 10.125V8.11575C5.1345 8.71594 7.11169 9 9 9C11.9801 9 15.1875 8.29575 15.1875 6.75V3.375C15.1875 1.82925 11.9801 1.125 9 1.125ZM3.93694 3.38344C4.02188 3.07125 5.70881 2.25 9 2.25C12.2614 2.25 13.9472 3.05663 14.0608 3.375C13.9472 3.69338 12.2608 4.5 9 4.5C5.70938 4.5 4.02188 3.67875 3.93694 3.38344ZM14.0625 6.74269C13.9725 7.056 12.2856 7.875 9 7.875C5.70938 7.875 4.02188 7.05375 3.9375 6.75V4.74075C5.1345 5.34094 7.11169 5.625 9 5.625C10.8883 5.625 12.8655 5.34094 14.0625 4.74075V6.74269Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(SiteIntegrationSvg);
