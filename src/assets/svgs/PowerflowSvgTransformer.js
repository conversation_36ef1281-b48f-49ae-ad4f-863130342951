import * as React from 'react';
import {useRef, useEffect} from 'react';
import {Animated, Dimensions, Easing} from 'react-native';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';

const AnimatedPath = Animated.createAnimatedComponent(Path);

const PowerflowSvgTransformer = props => {
  // Animation value for the dash offset
  const {width: screenWidth, height: screenHeight} = Dimensions.get('window');
  const dashOffset = useRef(new Animated.Value(0)).current;

  // Start the animation when component mounts
  useEffect(() => {
    // Create an infinite animation
    const animation = Animated.loop(
      Animated.timing(dashOffset, {
        toValue: props.direction,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
        iterations: -1, // Run forever
      }),
    );

    // Start the animation
    animation.start();

    // Clean up on unmount
    return () => {
      animation.stop();
    };
  }, [props.direction]);

  // Get color from props or use default
  const pathColor = props.pathColor || '#FFFFFF'; // Default color is white

  // Use the pathData from props
  const pathData = props.pathData;

  return (
    <Svg
      width={screenWidth * 0.54}
      height={screenHeight * 0.3}
      viewBox="0 15 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      {/* Background path (static) */}
      <Path d={pathData} stroke="#969696" strokeWidth={2} strokeDasharray="0" />

      {/* Animated dashed path */}
      <AnimatedPath
        d={pathData}
        stroke={pathColor}
        strokeWidth={3}
        strokeDasharray="10, 25"
        strokeDashoffset={dashOffset}
        strokeLinecap="round"
      />

      <Defs>
        <LinearGradient id="powerGradient" x1="0" y1="0" x2="1" y2="0">
          <Stop offset="0" stopColor={pathColor} stopOpacity="1" />
          <Stop offset="1" stopColor={pathColor} stopOpacity="0.6" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
};

export default PowerflowSvgTransformer;
