import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const LightingSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={46}
    height={46}
    viewBox="0 0 46 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M18.598 7.00187H31.3595C31.5133 7.00134 31.6648 7.0385 31.8003 7.10999C31.9358 7.18147 32.051 7.28502 32.1355 7.41126C32.2199 7.5375 32.271 7.68243 32.284 7.83294C32.2971 7.98345 32.2717 8.13478 32.2102 8.27323L27.2172 19.7189H35.6339C35.7044 19.7196 35.7732 19.7401 35.8322 19.7779C35.8913 19.8157 35.9381 19.8693 35.9671 19.9324C35.9962 19.9955 36.0063 20.0654 35.9962 20.1339C35.9862 20.2024 35.9564 20.2667 35.9104 20.3191L18.7459 38.8464C18.6991 38.9152 18.6287 38.9652 18.5475 38.9874C18.4663 39.0095 18.3798 39.0024 18.3035 38.9672C18.2272 38.9321 18.1663 38.8712 18.1318 38.7958C18.0973 38.7203 18.0915 38.6351 18.1154 38.5558L19.8185 25.1676H11.9389C11.7859 25.1696 11.6347 25.1344 11.4989 25.0651C11.3632 24.9957 11.247 24.8944 11.1608 24.7702C11.0746 24.646 11.0211 24.5027 11.0051 24.3532C10.9891 24.2037 11.011 24.0527 11.0689 23.9135L17.7262 7.56403C17.7968 7.39474 17.9177 7.25032 18.0733 7.1497C18.2289 7.04908 18.4117 6.99868 18.598 7.00187Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(LightingSvg);
