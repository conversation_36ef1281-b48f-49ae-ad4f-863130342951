import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const RotateSvg = props => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M11.2616 5.88755C11.5663 5.85005 11.8686 5.8313 12.171 5.82896L12.171 7.32661C12.171 7.47896 12.3468 7.56333 12.4663 7.46958L15.4686 5.10005C15.5624 5.02505 15.5624 4.88443 15.4686 4.81177L12.4686 2.44458C12.3491 2.35083 12.1733 2.43521 12.1733 2.58755L12.1686 4.08755C9.38895 4.09927 6.64208 5.33911 4.7952 7.70161C3.16395 9.79224 2.55223 12.3563 2.89442 14.7891H4.64989C4.6288 14.6649 4.61005 14.5383 4.59364 14.4118C4.47411 13.4251 4.54442 12.4407 4.80223 11.4868C5.06942 10.4977 5.5288 9.58599 6.16395 8.77271C6.80145 7.95943 7.57723 7.29146 8.4702 6.79693C9.33739 6.31411 10.2725 6.00943 11.2616 5.88755Z"
      fill="#96999E"
    />
    <Path
      d="M20.625 9.79688H8.25C7.83516 9.79688 7.5 10.132 7.5 10.5469L7.5 20.25C7.5 20.6648 7.83516 21 8.25 21H20.625C21.0398 21 21.375 20.6648 21.375 20.25L21.375 10.5469C21.375 10.132 21.0398 9.79688 20.625 9.79688ZM19.5937 19.2188H9.28125L9.28125 11.5781H19.5937L19.5937 19.2188Z"
      fill="#96999E"
    />
  </Svg>
);
export default React.memo(RotateSvg);
