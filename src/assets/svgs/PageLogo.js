import * as React from 'react';
import Svg, {
  G,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Rect,
} from 'react-native-svg';
const PageLogo = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={117}
    height={58}
    viewBox="0 0 117 58"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <G clipPath="url(#clip0_4516_1621)">
      <Path
        d="M16.3614 26.1809H10.9763V36.5694H5.50166V26.1809H0.135742V21.7305H16.3678V26.1809H16.3614Z"
        fill={color}
      />
      <Path
        d="M51.3945 21.7305H57.4514L61.6875 29.0999H62.1767V21.7305H67.3419V36.5694H61.4088L57.0303 29.1999H56.5596V36.5694H51.3945V21.7305Z"
        fill={color}
      />
      <Path
        d="M84.7127 29.15C84.7127 33.4254 82.6066 36.5694 76.4568 36.5694H68.7656V21.7305H76.4568C82.6066 21.7305 84.7127 24.8558 84.7127 29.15ZM79.3285 29.15C79.3285 26.9248 78.0371 26.1996 76.4568 26.1996H74.1563L74.138 32.1002H76.4568C78.0371 32.1002 79.3285 31.3564 79.3285 29.15Z"
        fill={color}
      />
      <Path
        d="M85.5684 21.7305H99.2369V25.9871H90.9109V27.4373H98.9181V30.8627H90.9109V32.294H99.2369V36.5694H85.5684V21.7305Z"
        fill={color}
      />
      <Path
        d="M111.028 36.5694L109.137 32.9316H105.752V36.5694H100.376V21.7305H109.175C113.379 21.7305 115.768 23.4619 115.768 27.5248C115.768 29.4688 114.911 30.8813 113.72 31.7627L116.323 36.5694H111.028ZM105.752 28.9562H108.778C109.849 28.9562 110.366 28.4249 110.366 27.5623C110.366 26.6997 109.849 26.1684 108.778 26.1684H105.752V28.9562Z"
        fill={color}
      />
      <Path
        d="M34.3086 21.7305V29.8374C34.3086 29.8437 34.3086 29.8436 34.3086 29.8499L39.7193 21.7305H34.3086Z"
        fill={color}
      />
      <Path
        d="M35.1631 33.4958C36.3003 35.5949 38.6182 36.5679 42.154 36.5679C47.4049 36.5679 49.9713 34.426 49.9713 29.6159V23.1289L35.1631 33.4958Z"
        fill={color}
      />
      <Path
        d="M56.268 7.03577C56.0438 6.49488 55.6079 6.25864 55.0286 6.3519C54.3062 6.47002 53.7208 6.87413 53.1603 7.30932C52.743 7.62639 52.3632 7.98698 51.9646 8.3227C51.9022 8.37866 51.8275 8.44704 51.7528 8.45947C51.2857 8.55274 50.8186 8.63354 50.3391 8.69572C50.9681 8.08645 51.6469 7.558 52.4753 7.24715C52.469 7.21606 52.469 7.18498 52.4628 7.15389C52.1576 7.17254 51.8462 7.14768 51.5535 7.21607C50.5384 7.43366 49.6914 7.99941 48.9067 8.64598C48.4333 9.03144 34.3086 20.1351 34.3086 20.1351H42.2678L34.3272 32.3702L55.0722 17.6545H49.2554C50.993 15.0682 54.3748 10.0262 55.0411 9.31121C55.1595 9.18687 55.2964 9.06874 55.4335 8.96927C55.9006 8.63976 56.1434 8.67707 56.4984 9.14335C56.542 8.40352 56.5482 7.70721 56.268 7.03577Z"
        fill="url(#paint0_linear_4516_1621)"
      />
      <Path
        d="M33.1699 21.7305V36.5694H27.8082V31.394H22.5655V36.5694H17.2227V21.7305H22.5655V26.9059H27.8082V21.7305H33.1699Z"
        fill={color}
      />
      <Path
        d="M80.7392 48.8385C79.7555 48.8385 79.3888 48.2613 79.3888 47.4848V42.5065H78.3914C78.2988 42.5065 78.2988 42.3485 78.2988 42.2179C78.2988 42.0873 78.2988 41.9156 78.3914 41.9156H79.3888V40.1943C79.3888 40.1152 79.6116 40.1152 79.7418 40.1152C79.872 40.1152 80.0948 40.1152 80.0948 40.1943V41.9156H81.6406C81.7195 41.9156 81.7195 42.0599 81.7195 42.2179C81.7195 42.3759 81.7195 42.5065 81.6406 42.5065H80.0948V47.4574C80.0948 48.0105 80.3313 48.2201 80.9072 48.2201H81.6269C81.7195 48.2201 81.7435 48.2476 81.7435 48.5087C81.7435 48.7973 81.716 48.8385 81.6269 48.8385H80.7392Z"
        fill="#1AA3C9"
        stroke="#1AA3C9"
        strokeWidth={0.5}
      />
      <Path
        d="M83.4258 45.6323V46.316C83.4258 47.5769 84.0942 48.3534 85.4446 48.3534C86.466 48.3534 87.0829 48.0785 87.5936 47.8002C87.6999 47.7487 88.0255 48.3122 87.8952 48.3912C87.2269 48.7726 86.4009 48.9959 85.472 48.9959C83.7411 48.9959 82.7197 48.0236 82.7197 46.3675V44.1996C82.7197 42.5986 83.8474 41.5713 85.328 41.5713C86.9253 41.5713 88.0152 42.5951 88.0152 44.3439V45.6323H83.4258ZM87.3023 44.2821C87.3023 42.8769 86.3974 42.1932 85.3246 42.1932C84.2895 42.1932 83.4258 42.8769 83.4258 44.2408V45.0551H87.3057V44.2821H87.3023Z"
        fill="#1AA3C9"
        stroke="#1AA3C9"
        strokeWidth={0.5}
      />
      <Path
        d="M90.9452 48.8399C89.9752 48.8399 89.5947 48.2627 89.5947 47.4862V39.079C89.5947 39 89.8175 39 89.9478 39C90.078 39 90.3008 39 90.3008 39.079V47.4587C90.3008 48.0119 90.551 48.2352 91.1268 48.2352H91.4798C91.5724 48.2352 91.5964 48.2627 91.5964 48.5238C91.5964 48.8124 91.569 48.8399 91.4798 48.8399H90.9452Z"
        fill="#1AA3C9"
        stroke="#1AA3C9"
        strokeWidth={0.5}
      />
      <Path
        d="M93.1494 45.6323V46.316C93.1494 47.5769 93.8178 48.3534 95.1682 48.3534C96.1896 48.3534 96.8066 48.0785 97.3173 47.8002C97.4235 47.7487 97.7491 48.3122 97.6189 48.3912C96.9505 48.7726 96.1245 48.9959 95.1956 48.9959C93.4648 48.9959 92.4434 48.0236 92.4434 46.3675V44.1996C92.4434 42.5986 93.571 41.5713 95.0517 41.5713C96.6489 41.5713 97.7388 42.5951 97.7388 44.3439V45.6323H93.1494ZM97.0259 44.2821C97.0259 42.8769 96.1211 42.1932 95.0483 42.1932C94.0132 42.1932 93.1494 42.8769 93.1494 44.2408V45.0551H97.0293V44.2821H97.0259Z"
        fill="#1AA3C9"
        stroke="#1AA3C9"
        strokeWidth={0.5}
      />
      <Path
        d="M104.026 48.8923C103.896 48.8923 103.673 48.8923 103.673 48.8133V43.9895C103.673 42.8214 103.162 42.282 102.309 42.282C101.404 42.282 100.564 43.0584 100.054 43.8074V48.8133C100.054 48.8923 99.8309 48.8923 99.7007 48.8923C99.5704 48.8923 99.3477 48.8923 99.3477 48.8133V41.746C99.3477 41.667 99.5704 41.667 99.7007 41.667C99.8309 41.667 100.04 41.667 100.04 41.746V43.0207C100.602 42.2201 101.469 41.5742 102.518 41.5742C103.659 41.5742 104.379 42.4297 104.379 43.6769V48.8133C104.379 48.8923 104.156 48.8923 104.026 48.8923Z"
        fill="#1AA3C9"
        stroke="#1AA3C9"
        strokeWidth={0.5}
      />
      <Path
        d="M105.887 46.4358V44.1373C105.887 42.4812 106.884 41.5742 108.612 41.5742C110.356 41.5742 111.364 42.4812 111.364 44.1373V46.4358C111.364 48.078 110.356 48.9988 108.612 48.9988C106.884 48.9954 105.887 48.078 105.887 46.4358ZM108.612 42.2167C107.354 42.2167 106.593 42.7561 106.593 44.2541V46.3155C106.593 47.7997 107.354 48.3632 108.612 48.3632C109.883 48.3632 110.654 47.8238 110.654 46.3155V44.2541C110.658 42.7698 109.883 42.2167 108.612 42.2167Z"
        fill="#1AA3C9"
        stroke="#1AA3C9"
        strokeWidth={0.5}
      />
      <Path
        d="M115.453 42.4263C114.877 42.4263 114.391 42.952 113.671 44.0548V48.8133C113.671 48.8923 113.448 48.8923 113.318 48.8923C113.188 48.8923 112.979 48.8923 112.979 48.8133V41.746C112.979 41.667 113.188 41.667 113.318 41.667C113.448 41.667 113.647 41.667 113.647 41.746V43.3368C114.432 42.1411 114.853 41.6945 115.638 41.6945H115.899C115.978 41.6945 115.978 41.9453 115.978 42.0758C115.978 42.2064 115.95 42.4297 115.871 42.4297H115.453V42.4263Z"
        fill="#1AA3C9"
        stroke="#1AA3C9"
        strokeWidth={0.5}
      />
      <Path
        d="M71.0543 42.0562C71.1973 42.0806 71.2262 42.0481 71.2454 41.9009C71.2923 41.5578 71.3863 41.2239 71.524 40.9109C71.7118 40.4936 72.0086 40.0345 72.427 39.7262C72.8486 39.4306 73.3129 39.2138 73.7997 39.0851C74.1284 39.0063 74.4657 38.9819 74.8009 39.0132C75.4029 39.0724 75.736 39.259 75.9035 39.5001C75.959 39.5801 75.9921 39.6763 75.9996 39.7772C76.0049 39.8977 75.9569 40.0542 75.7968 40.2072C75.6378 40.3614 75.3122 40.5434 74.8628 40.7069C74.3964 40.875 73.7591 41.0535 73.1229 41.2123C72.7547 41.3027 72.3907 41.4163 72.0342 41.552C71.6136 41.7073 71.4866 42.1617 71.7481 42.3008C72.0427 42.4573 72.317 42.6532 72.5668 42.8828C72.9671 43.2282 73.3332 43.6189 73.6577 44.0478C74.0537 44.5729 74.7016 45.5745 74.9333 46.5471C75.1916 47.6148 75.0304 48.6268 74.4743 48.9096C73.9288 49.1878 73.2019 48.7867 72.6917 48.2106C72.2071 47.6669 71.8677 47.0235 71.5474 46.0359C71.271 45.1815 71.16 43.9493 71.16 43.3024C71.16 43.0868 71.16 43.0416 71.1653 42.8468C71.1835 42.6776 70.7629 42.5361 70.3114 42.8526C69.7969 43.212 69.2941 43.8623 68.9963 44.2414C68.8672 44.4072 68.6911 44.6494 68.5053 44.9033C68.2598 45.2372 67.9898 45.5849 67.7432 45.7797C67.3739 46.066 66.7772 46.1901 66.3577 45.8678C66.125 45.6881 66.0001 45.3473 66.0001 45.0007C65.9969 44.7619 66.0535 44.5266 66.1645 44.3191C66.3075 44.0524 66.5338 43.7661 66.9 43.438C67.3824 43.0311 67.9161 42.7008 68.484 42.4573C69.4052 42.0551 70.3968 41.9287 71.0543 42.0562Z"
        fill="#1AA3C9"
      />
    </G>
    <Defs>
      <LinearGradient
        id="paint0_linear_4516_1621"
        x1={41.1356}
        y1={32.6342}
        x2={56.0468}
        y2={20.5186}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#FF7F02" />
        <Stop offset={1} stopColor="#FFC400" />
      </LinearGradient>
      <ClipPath id="clip0_4516_1621">
        <Rect width={117} height={58} fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default PageLogo;
