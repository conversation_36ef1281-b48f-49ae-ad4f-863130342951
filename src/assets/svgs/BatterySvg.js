import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const BatterySvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={44}
    height={44}
    viewBox="0 0 44 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M26.1817 18.091H34.5454M9.45446 18.091H13.6363M13.6363 18.091H17.8181M13.6363 18.091V13.9092M13.6363 18.091V22.2729M9.45446 5.54559H2.34537C2.01264 5.54559 1.69354 5.67776 1.45827 5.91304C1.223 6.14831 1.09082 6.46741 1.09082 6.80013V29.382C1.09082 29.7147 1.223 30.0338 1.45827 30.2691C1.69354 30.5043 2.01264 30.6365 2.34537 30.6365H41.6545C41.9872 30.6365 42.3063 30.5043 42.5416 30.2691C42.7768 30.0338 42.909 29.7147 42.909 29.382V6.80013C42.909 6.46741 42.7768 6.14831 42.5416 5.91304C42.3063 5.67776 41.9872 5.54559 41.6545 5.54559H34.5454M9.45446 5.54559V1.36377H13.6363V5.54559M9.45446 5.54559H13.6363M34.5454 5.54559V1.36377H30.3635V5.54559M34.5454 5.54559H30.3635M13.6363 5.54559H30.3635"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(BatterySvg);
// export default BatterySvg;
