import * as React from 'react';
import Svg, {G, Path, Defs} from 'react-native-svg';
/* SVGR has dropped some elements not supported by react-native-svg: filter */
const MapPin = props => (
  <Svg
    width={21}
    height={22}
    viewBox="0 0 21 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <G filter="url(#filter0_d_671_2046)">
      <Path
        d="M10.5 1.79492C12.4138 1.79718 14.2486 2.55845 15.6019 3.91174C16.9552 5.26503 17.7165 7.09983 17.7187 9.01367C17.7205 10.5776 17.2097 12.099 16.2645 13.3449C16.2645 13.3449 16.0676 13.6041 16.0355 13.6415L10.5 20.1699L4.9619 13.6383C4.93303 13.6035 4.73549 13.3449 4.73549 13.3449L4.73484 13.343C3.79029 12.0974 3.27973 10.5768 3.28124 9.01367C3.2835 7.09983 4.04477 5.26503 5.39806 3.91174C6.75135 2.55845 8.58616 1.79718 10.5 1.79492ZM10.5 11.6387C11.0192 11.6387 11.5267 11.4847 11.9584 11.1963C12.39 10.9078 12.7265 10.4979 12.9252 10.0182C13.1239 9.53856 13.1758 9.01076 13.0746 8.50156C12.9733 7.99236 12.7233 7.52463 12.3562 7.15752C11.989 6.7904 11.5213 6.5404 11.0121 6.43911C10.5029 6.33782 9.97511 6.38981 9.49545 6.58849C9.01579 6.78717 8.60583 7.12362 8.31739 7.5553C8.02895 7.98698 7.87499 8.4945 7.87499 9.01367C7.87586 9.7096 8.1527 10.3768 8.6448 10.8689C9.13689 11.361 9.80407 11.6378 10.5 11.6387Z"
        fill="#00EE5D"
      />
    </G>
    <Defs></Defs>
  </Svg>
);
export default React.memo(MapPin);
