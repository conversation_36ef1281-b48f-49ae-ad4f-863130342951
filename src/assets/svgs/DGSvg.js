import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const DGSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={38}
    height={39}
    viewBox="0 0 38 39"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M19.7059 11.4175V5.31068L17.5882 3.87379H10.8824H10.1765L11.9412 7.82524H15.8235V11.4175M19.7059 11.4175H37V33.6893H35.2353M19.7059 11.4175H15.8235M15.8235 11.4175H2.41176V33.6893H3.47059M35.2353 33.6893V38H7.35294L3.47059 33.6893M35.2353 33.6893H3.47059M7 5.6699H2.76471M4.52941 1H1M6.64706 15.3689H11.9412V29.3786H6.64706V15.3689ZM15.8235 17.165H33.4706V26.5049H15.8235V17.165ZM19.3529 20.0388H22.8824V23.6311H19.3529V20.0388ZM26.0588 20.0388H29.5882V23.6311H26.0588V20.0388Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(DGSvg);
