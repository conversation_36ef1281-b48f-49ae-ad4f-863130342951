import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const SiteViewSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M5.9251 10.575L5.9926 10.5075C6.1951 10.305 6.2326 9.97504 6.0526 9.75004C5.5201 9.06754 5.2501 8.25754 5.2501 7.50004C5.2501 6.69004 5.5126 5.88004 6.0301 5.24254C6.2176 5.01754 6.1876 4.68004 5.9776 4.47754L5.9251 4.42504C5.6701 4.17004 5.2501 4.20004 5.0251 4.48504C4.3426 5.37004 3.9751 6.43504 3.9751 7.50004C3.9751 8.56504 4.3426 9.63004 5.0251 10.515C5.2501 10.8 5.6701 10.83 5.9251 10.575ZM13.8826 2.61754L13.8226 2.67754C13.5976 2.90254 13.6051 3.24754 13.8001 3.48754C14.7451 4.63504 15.2251 6.09754 15.2251 7.50004C15.2251 8.90254 14.7526 10.3575 13.8001 11.5125C13.5901 11.76 13.6276 12.135 13.8601 12.3675C14.1226 12.63 14.5576 12.6 14.7901 12.315C15.9676 10.905 16.5001 9.23254 16.5001 7.50004C16.5001 5.76004 15.9076 4.08754 14.7676 2.67754C14.5426 2.40004 14.1301 2.37004 13.8826 2.61754Z"
      fill={color}
    />
    <Path
      d="M4.1775 2.67754L4.1175 2.61754C3.87 2.37004 3.4575 2.40004 3.2325 2.67754C2.0925 4.08754 1.5 5.76004 1.5 7.50004C1.5 9.24004 2.0925 10.9125 3.2325 12.3225C3.4575 12.6 3.87 12.6375 4.1175 12.3825L4.1775 12.3225C4.4025 12.0975 4.395 11.7525 4.2 11.5125C3.2475 10.3575 2.775 8.90254 2.775 7.50004C2.775 6.09754 3.2475 4.64254 4.2 3.48754C4.395 3.24754 4.4025 2.90254 4.1775 2.67754ZM12.0525 10.5525C12.3225 10.8225 12.765 10.7925 12.9975 10.485C13.6725 9.60004 14.025 8.55004 14.025 7.50004C13.965 6.44254 13.6425 5.37754 12.975 4.49254C12.9239 4.42393 12.8586 4.36709 12.7836 4.32588C12.7087 4.28466 12.6257 4.26002 12.5404 4.25362C12.4551 4.24722 12.3694 4.25921 12.2891 4.28878C12.2088 4.31836 12.1358 4.36482 12.075 4.42504L12.015 4.48504C11.8125 4.68754 11.775 5.01754 11.955 5.24254C12.48 5.93254 12.75 6.74254 12.75 7.50004C12.75 8.30254 12.495 9.09754 11.9925 9.73504C11.7975 9.97504 11.8275 10.3275 12.0525 10.5525ZM10.875 7.50004C10.875 6.30004 9.7425 5.36254 8.49 5.69254C7.89 5.85004 7.395 6.33004 7.215 6.93004C6.975 7.72504 7.26 8.46004 7.785 8.91004L5.565 15.5625C5.4075 16.0275 5.7525 16.5 6.24 16.5C6.5475 16.5 6.8175 16.305 6.915 16.0125L7.2525 15H10.755L11.0925 16.0125C11.1202 16.1034 11.1658 16.1878 11.2267 16.2608C11.2875 16.3338 11.3624 16.3938 11.4468 16.4374C11.5312 16.481 11.6235 16.5073 11.7183 16.5146C11.813 16.5219 11.9082 16.5103 11.9984 16.4802C12.0885 16.4502 12.1717 16.4024 12.2431 16.3397C12.3145 16.2769 12.3726 16.2006 12.414 16.115C12.4554 16.0295 12.4792 15.9366 12.4841 15.8417C12.489 15.7468 12.4749 15.6519 12.4425 15.5625L10.2225 8.91004C10.62 8.56504 10.875 8.07004 10.875 7.50004ZM7.7475 13.5L9 9.75004L10.2525 13.5H7.7475Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(SiteViewSvg);
