import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const AlarmsSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={16}
    height={14}
    viewBox="0 0 16 14"
    fill={color}
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M1.04366 13.75C0.906155 13.75 0.781155 13.7158 0.668655 13.6473C0.556155 13.5788 0.468655 13.488 0.406155 13.375C0.343655 13.262 0.309405 13.14 0.303405 13.009C0.297405 12.878 0.331655 12.75 0.406155 12.625L7.34366 0.625C7.41866 0.5 7.51566 0.40625 7.63465 0.34375C7.75365 0.28125 7.87541 0.25 7.99991 0.25C8.12441 0.25 8.24641 0.28125 8.36591 0.34375C8.4854 0.40625 8.58216 0.5 8.65616 0.625L15.5937 12.625C15.6687 12.75 15.7032 12.8783 15.6972 13.0098C15.6912 13.1413 15.6567 13.263 15.5937 13.375C15.5307 13.487 15.4432 13.5778 15.3312 13.6473C15.2192 13.7168 15.0942 13.751 14.9562 13.75H1.04366ZM2.3374 12.25H13.6624L7.99991 2.5L2.3374 12.25ZM7.99991 11.5C8.21241 11.5 8.39066 11.428 8.53466 11.284C8.67866 11.14 8.75041 10.962 8.74991 10.75C8.7494 10.538 8.67741 10.36 8.53391 10.216C8.39041 10.072 8.21241 10 7.99991 10C7.78741 10 7.60941 10.072 7.46591 10.216C7.32241 10.36 7.25041 10.538 7.24991 10.75C7.24941 10.962 7.32141 11.1402 7.46591 11.2847C7.61041 11.4292 7.78841 11.501 7.99991 11.5ZM7.99991 9.25C8.21241 9.25 8.39066 9.178 8.53466 9.034C8.67866 8.89 8.75041 8.712 8.74991 8.5V6.25C8.74991 6.0375 8.67791 5.8595 8.53391 5.716C8.3899 5.5725 8.21191 5.5005 7.99991 5.5C7.78791 5.4995 7.60991 5.5715 7.46591 5.716C7.32191 5.8605 7.24991 6.0385 7.24991 6.25V8.5C7.24991 8.7125 7.32191 8.89075 7.46591 9.03475C7.60991 9.17875 7.78791 9.2505 7.99991 9.25Z"
      fill={color}
    />
  </Svg>
);
export default React.memo(AlarmsSvg);
