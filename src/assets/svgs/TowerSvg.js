import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const TowerSvg = ({color = '#FFFFFF', ...props}) => (
  <Svg
    width={46}
    height={46}
    viewBox="0 0 46 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M17.2502 11.5L18.0168 15.7435C18.1433 16.5753 18.0667 17.4206 17.7887 18.2275L9.5835 42.1667M28.7502 11.5L27.9835 15.7435C27.857 16.5753 27.9337 17.4206 28.2116 18.2275L36.4168 42.1667"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M5.75 21.0833L6.371 19.0133C6.53775 18.4575 6.62017 18.1796 6.80992 17.9457C6.99775 17.7138 7.27758 17.5433 7.83533 17.2059L16.2418 12.1133C16.7402 11.8105 16.9912 11.6591 17.2807 11.5805C17.5682 11.5 17.8787 11.5 18.4958 11.5H27.5042C28.1213 11.5 28.4299 11.5 28.7193 11.5767C29.0068 11.6572 29.2579 11.8067 29.7582 12.1114L38.1647 17.2059C38.7224 17.5433 39.0023 17.7138 39.1901 17.9457C39.3779 18.1796 39.4622 18.4575 39.6309 19.0152L40.25 21.0833M5.75 42.1667H40.25M7.66667 17.25H37.375"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M18.2085 19.167L30.6668 27.792L9.5835 42.167"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M27.7918 19.1668L15.3335 27.7918L36.4168 42.1668M17.2502 11.5002L19.9335 7.20683C21.3384 4.95858 22.0418 3.8335 23.0002 3.8335C23.9585 3.8335 24.6619 4.95858 26.0668 7.20683L28.7502 11.5002"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default React.memo(TowerSvg);
// export default TowerSvg;
