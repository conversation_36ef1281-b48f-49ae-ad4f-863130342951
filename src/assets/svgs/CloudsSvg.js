import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const CloudsSvg = props => (
  <Svg
    width={54}
    height={54}
    viewBox="0 0 54 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M45.4017 19.5281C45.4017 19.5281 46.1273 5.89312 35.3231 3.19312C22.8314 0.0712464 20.3718 12.0187 20.3718 12.0187C20.3718 12.0187 15.4992 11.0948 12.4659 14.3897C9.92199 17.153 10.1625 19.8572 10.1625 19.8572C10.1625 19.8572 6.80433 19.1316 4.16761 21.4392C1.53089 23.7469 1.92746 25.9153 1.92746 25.9153L9.76589 28.9444C9.76589 28.9444 49.3504 29.1426 49.5487 29.0119C49.747 28.8811 52.1854 25.0589 52.1854 25.0589C52.1854 25.0589 51.7889 21.7641 49.4854 20.4478C47.1778 19.1316 45.4017 19.5281 45.4017 19.5281Z"
      fill="#92999C"
    />
    <Path
      d="M6.01141 30.9865C7.98578 31.054 20.8994 31.3155 28.7336 31.248C36.5678 31.1805 48.4816 31.3071 50.1353 30.5266C51.4305 29.9149 52.2025 28.9024 52.3248 27.4849C52.443 26.1307 52.1814 25.0591 52.1814 25.0591C52.1814 25.0591 50.3378 26.3923 46.8447 26.4429C44.672 26.4724 40.2592 26.1138 41.1156 25.3249C41.3561 25.1013 44.7691 22.5152 44.6045 20.1232C44.5666 19.5579 41.508 22.3633 38.6097 23.5488C35.7114 24.7343 32.9608 25.6498 29.1302 25.4051C25.9028 25.1941 24.5022 23.8821 24.5022 23.8821C24.5022 23.8821 22.9455 25.7088 18.2669 26.1012C13.5883 26.4977 12.58 24.7385 12.58 24.7385C12.58 24.7385 10.3145 26.4513 6.50922 26.4007C2.71234 26.3416 1.96563 24.9748 1.96563 24.9748C1.96563 24.9748 1.60703 27.409 2.40016 28.8307C3.21016 30.2777 4.6825 30.9443 6.01141 30.9865Z"
      fill="#B9CED2"
    />
    <Path
      d="M29.5263 34.4125C28.8049 34.1594 26.2314 36.1971 25.0713 38.2094C23.9069 40.2302 24.6663 41.7067 25.9108 42.3142C27.3114 42.9977 29.328 42.5758 29.9018 40.5846C30.4671 38.6144 30.3025 34.6867 29.5263 34.4125ZM15.8871 33.4085C15.2332 33.1764 13.1111 35.1466 12.1577 36.2097C10.8794 37.6399 9.89223 40.3061 12.318 41.3861C14.7607 42.4703 16.3469 40.6942 16.4693 38.2389C16.5874 35.9524 16.4271 33.6025 15.8871 33.4085ZM21.0382 42.8838C20.3421 42.5083 17.4185 45.1028 16.4861 46.6511C15.558 48.1994 15.6171 50.1864 17.5155 51.0007C19.4139 51.8149 21.0888 50.7728 21.4389 48.0602C21.6836 46.1364 21.7089 43.2466 21.0382 42.8838ZM35.1752 43.1074C34.6732 42.8247 31.1969 45.5078 30.4164 47.4611C28.8682 51.3382 34.1585 53.0678 35.3608 49.4228C36.0611 47.2966 35.7953 43.4575 35.1752 43.1074ZM43.8194 34.75C43.1318 34.3282 40.2419 36.7075 39.28 38.4963C38.065 40.7575 39.01 42.4619 40.3347 43.0019C41.71 43.5588 43.4777 43.023 44.0768 40.9853C44.9416 38.0491 44.486 35.1635 43.8194 34.75Z"
      fill="#00B1FF"
    />
    <Path
      d="M40.6678 38.4288C40.2122 38.4836 39.6469 39.1333 39.5583 40.2428C39.4697 41.3524 40.17 41.9135 40.8787 41.6308C41.5453 41.365 41.655 40.4158 41.655 39.8167C41.655 39.2177 41.3723 38.3402 40.6678 38.4288ZM26.5772 38.0786C26.0962 37.9436 25.2019 38.6861 25.0458 39.8885C24.9234 40.8166 25.282 41.3777 26.0034 41.4325C27.0075 41.5085 27.3239 40.2724 27.2986 39.5172C27.2817 39.0869 27.2058 38.2558 26.5772 38.0786ZM17.9076 46.3178C17.4605 46.2841 16.7095 47.035 16.7095 48.2036C16.7095 49.3722 17.317 49.8785 18.0342 49.7224C18.7514 49.562 18.8948 48.191 18.8484 47.6467C18.802 47.1025 18.57 46.3685 17.9076 46.3178ZM13.0392 37.3108C12.4359 37.1378 11.6639 37.9014 11.5711 38.762C11.474 39.6227 11.8748 40.247 12.5119 40.3441C13.1489 40.4411 13.6931 39.6733 13.7564 38.7789C13.8239 37.8845 13.6003 37.4669 13.0392 37.3108ZM32.2303 46.8156C31.6734 46.7692 30.8761 47.5919 30.8972 48.8364C30.9183 50.081 31.5637 50.2919 31.9898 50.3172C32.6015 50.3552 33.0276 49.6338 33.0825 48.5031C33.1289 47.5539 32.9137 46.8705 32.2303 46.8156Z"
      fill="#FEFEFE"
    />
  </Svg>
);
export default CloudsSvg;
