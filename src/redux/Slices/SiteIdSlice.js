import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  siteId: null,
  lastUpdated: null,
  isInitialized: false,
};

const SiteIdSlice = createSlice({
  name: 'siteId',
  initialState,
  reducers: {
    GetsiteID: (state, action) => {
      state.siteId = action.payload;
      state.lastUpdated = Date.now();
      state.isInitialized = true;
    },
    GetsiteIDAlarms: (state, action) => {
      state.siteId = action.payload;
      state.lastUpdated = Date.now();
      state.isInitialized = true;
    },
  },
});

export const {GetsiteID, GetsiteIDAlarms} = SiteIdSlice.actions;
export default SiteIdSlice.reducer;
