import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useRef} from 'react';
import LinearGradient from 'react-native-linear-gradient';

import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import SplashScreen from './Screens/Splash/SplashScreen';
import OnBoardingScreen from './Screens/onBoarding/OnBoardingScreen';
import Toast, {BaseToast} from 'react-native-toast-message';
import BottomSheetCustom from './Screens/onBoarding/BottomSheetCustom';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import LandingScreen from './Screens/LandingScreen/LandingScreen';
import {Provider} from 'react-redux';
import {store} from './redux/Store/store';
import {AuthProvider} from './Context/AuthContext';
import {AutocompleteDropdownContextProvider} from 'react-native-autocomplete-dropdown';

import UserDefinedParams from './Screens/LandingScreen/Components/ParametersComponent.js/UserDefinedParams/UserDefinedParams';
import {Fonts} from './styles/fonts';
import ConfigLogs from './Screens/LandingScreen/Components/ConfigurationComponents/ConfigLogs/ConfigLogs';
import EditDeviceScreen from './Screens/LandingScreen/Components/IntegrationComponents/EditDeviceScreen/EditDeviceScreen';
import AddDeviceScreen from './Screens/LandingScreen/Components/IntegrationComponents/AddDeviceScreen/AddDeviceScreen';
import TourComponent from './Screens/onBoarding/TourComponent/TourComponent';
import {DeviceEventEmitter} from 'react-native';
import {TOKEN_EXPIRED_EVENT} from './middleWare/ApiCaller';
import AskedQueryScreen from './Screens/LandingScreen/Components/ChatBotComponents/AskedQueriesComponent/AskedQueryScreen/AskedQueryScreen';

const Stack = createNativeStackNavigator();

function RootStack() {
  const navigationRef = useRef(null);

  useEffect(() => {
    // Listen for token expiration events
    const subscription = DeviceEventEmitter.addListener(
      TOKEN_EXPIRED_EVENT,
      () => {
        // Add a small delay to ensure navigation is safe
        setTimeout(() => {
          if (navigationRef.current) {
            try {
              // Use a simpler navigation approach
              navigationRef.current.navigate('onBoarding');
            } catch (error) {
              console.error('Navigation error:', error);
            }
          }
        }, 500);
      },
    );

    return () => subscription.remove();
  }, []);

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}>
        <Stack.Screen name="Home" component={SplashScreen} />
        <Stack.Screen name="onBoarding" component={OnBoardingScreen} />
        <Stack.Screen name="LandingScreen" component={LandingScreen} />

        <Stack.Screen name="ConfigLogs" component={ConfigLogs} />
        <Stack.Screen name="UserDefinedParams" component={UserDefinedParams} />
        <Stack.Screen name="EditDeviceScreen" component={EditDeviceScreen} />
        <Stack.Screen name="AddDeviceScreen" component={AddDeviceScreen} />
        <Stack.Screen name="TourComponent" component={TourComponent} />
        <Stack.Screen name="AskedQueries" component={AskedQueryScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
const App = () => {
  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <Provider store={store}>
        <AuthProvider>
          <AutocompleteDropdownContextProvider>
            <RootStack />
          </AutocompleteDropdownContextProvider>
        </AuthProvider>
      </Provider>
    </GestureHandlerRootView>
  );
};

export default App;
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
