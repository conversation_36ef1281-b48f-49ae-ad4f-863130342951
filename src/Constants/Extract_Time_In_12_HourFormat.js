export function Extract_Time_In_12_HourFormat(datetimeString) {
  const date = new Date(datetimeString);

  if (isNaN(date.getTime())) {
    return {error: 'Invalid date string'};
  }

  const hours = date.getHours();
  const minutes = date.getMinutes();

  // 24-hour format
  const time24 = `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}`;

  // 12-hour format
  const hours12 = hours % 12 || 12;
  const ampm = hours >= 12 ? 'PM' : 'AM';
  const time12 = `${hours12}:${minutes.toString().padStart(2, '0')} ${ampm}`;

  return {time24, time12};
}
