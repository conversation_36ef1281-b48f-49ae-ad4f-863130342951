export const DayFilter = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate());
  const formattedDate = yesterday.toISOString().split('T')[0];
  return formattedDate;
};

export const DayFilter_7 = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 7);
  const formattedDate = yesterday.toISOString().split('T')[0];
  return formattedDate;
};

export const MonthFilter = () => {
  const today = new Date();
  today.setMonth(today.getMonth() - 1); // Set to last month

  const formattedDate = today.toISOString().split('T')[0];
  return formattedDate;
};

export const YearFilter = () => {
  const today = new Date();
  today.setFullYear(today.getFullYear() - 1); // Set to last month

  const formattedDate = today.toISOString().split('T')[0];
  return formattedDate;
};

export const YesterdayFilter = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const formattedDate = yesterday.toISOString().split('T')[0];
  return formattedDate;
};
export const ThisWeekFilter = () => {
  const today = new Date();
  const dayOfWeek = today.getDay();

  const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
  const startOfWeek = new Date(today.setDate(diff));
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);

  const formatDate = date => {
    return date.toISOString().split('T')[0];
  };

  return formatDate(startOfWeek);
};
export const LastWeekFilter = () => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 (Sunday) to 6 (Saturday)

  // Calculate last week's Monday (7 days before this week's Monday)
  const lastWeekMonday = new Date(today);
  lastWeekMonday.setDate(today.getDate() - dayOfWeek - 6);

  // Calculate last week's Sunday (6 days after last week's Monday)
  const lastWeekSunday = new Date(lastWeekMonday);
  lastWeekSunday.setDate(lastWeekMonday.getDate() + 6);

  const formatDate = date => date.toISOString().split('T')[0];

  return {
    startOfLastWeek: formatDate(lastWeekMonday),
    endOfLastWeek: formatDate(lastWeekSunday),
  };
};

export const ThisMonthFilter = () => {
  const today = new Date();
  const firstDayThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const currentDate = today;

  const formatDate = date => date.toISOString().split('T')[0];

  return {
    startOfThisMonth: formatDate(firstDayThisMonth),
    currentDate: formatDate(currentDate),
  };
};
export const LastMonthFilter = () => {
  const today = new Date();
  const firstDayLastMonth = new Date(
    today.getFullYear(),
    today.getMonth() - 1,
    1,
  );
  const lastDayLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);

  const formatDate = date => date.toISOString().split('T')[0];

  return {
    startOfLastMonth: formatDate(firstDayLastMonth),
    endOfLastMonth: formatDate(lastDayLastMonth),
  };
};
