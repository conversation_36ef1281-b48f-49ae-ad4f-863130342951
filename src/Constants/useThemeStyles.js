import AsyncStorage from '@react-native-async-storage/async-storage';
import {darkColors} from '@rneui/base';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

export const useThemeStyles = () => {
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState();

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');
        if (storedTheme !== null) {
          const parsedTheme = JSON.parse(storedTheme);
          setIsDark(parsedTheme);
        } else {
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching/parsing theme:', error);
        setIsDark(istheme === 'dark');
      }
    };
    fetchTheme();
  }, [istheme]);

  const themeStyles = {
    ssvBack: isDark ? '#F1F1F1' : '#0C121D',
    background: isDark ? '#FFFFFF' : '#0C121D',
    textColor: !isDark ? '#FFFFFF' : '#0E121A',
    textRow: !isDark ? '#FFFFFF' : '#96999E',
    splitterColor: isDark ? '#96999E00' : '#FFFFFF',
    cardBackground: !isDark ? '#252A3480' : '#F6F6F6',
    iconColor: isDark ? '#96999E' : '#FFFFFF',
    barBackground: !isDark ? '#FFFFFF' : '#0E121A',
    borderColor: isDark ? '#BEC4CE' : '#404346',
    inactiveColorPagination: isDark ? '#17172A1A' : '#FFFFFF1A',
    greyTextColor: !isDark ? '#96999E' : '#737476',
    alarmsTableBottomColor: !isDark ? '#96999E' : '#eee',
    ssvBackground: isDark ? '#FFFFFFE5' : '#252A34B2',
    siteInfoButton: isDark ? '#FFFFFF' : '#432A1180',
    siteInfoButtonColor: isDark ? '#CFCFCF' : '#473524',
    dropDownTextColor: !isDark ? '#96999E' : '#0E121A',
    powerSourceChart: isDark ? '#FFFFFF4D' : '#171E2C',
    iconBackgrounColor: isDark ? '' : '#2B303E',
    topContainerBackground: isDark ? '#FFFFFF0D' : '#252A341A',
    svgsTheme: isDark ? true : false,
    alarmsBackground: !isDark ? '#252A3480' : '#FFFFFF',
    alarmsCategory: isDark ? '#FFFFFF' : '#252A34B2',
    arrowIconColor: isDark ? '#0E121A' : '#FFFFFF',
    timeFilterColor: !isDark ? '#FFFFFF' : '#96999E',
    textFieldColor: !isDark ? '#FFFFFF1A' : '#F4F7FB',
    tooltipColor: !isDark ? '#0C121DE5' : '#FAF7ED',
    newSSvCardsBackground: isDark ? '#FFFFFF99' : '#0E121AE9',
    newSSVBorderColor: isDark ? '#CFCFCF' : '#272727',
    backgroundColorPages: isDark ? '#FFFFFF1A' : '#0C121D',
  };

  return themeStyles;
};
