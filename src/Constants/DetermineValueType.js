export const determineValueType = value => {
  // Handle null, undefined, and empty strings
  if (value === null || value === undefined || value === '') {
    return {value: null, type: 'NULL'};
  }

  // Handle boolean values
  if (value === 'true' || value === 'false') {
    return {value: value === 'true', type: 'BOOLEAN'};
  }

  // Handle numbers
  if (typeof value === 'string') {
    // Check for floating point numbers
    if (/^\-?\d*\.\d+$/.test(value)) {
      return {value: parseFloat(value), type: 'FLOAT'};
    }

    // Check for integers
    if (/^\-?\d+$/.test(value)) {
      return {value: parseInt(value, 10), type: 'INTEGER'};
    }

    // If it contains letters or special characters, keep as string
    if (/[a-zA-Z]/.test(value) || /[^0-9.\-]/.test(value)) {
      return {value: value, type: 'STRING'};
    }
  }

  // If the value is already a number
  if (typeof value === 'number') {
    return {
      value: value,
      type: Number.isInteger(value) ? 'INTEGER' : 'FLOAT',
    };
  }

  // Default case: return as string
  return {value: String(value), type: 'STRING'};
};
