export function formatDate(dateString) {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  if (!dateString) return '---';

  let day, month, year;

  if (/^\d{2}-\d{2}-\d{4}$/.test(dateString)) {
    // Format: dd-mm-yyyy
    [day, month, year] = dateString.split('-').map(Number);
  } else if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    // Format: yyyy-mm-dd (ISO)
    [year, month, day] = dateString.split('-').map(Number);
  } else {
    return '---'; // Invalid format
  }

  // Check for valid numbers
  if (
    isNaN(day) ||
    isNaN(month) ||
    isNaN(year) ||
    day < 1 ||
    day > 31 ||
    month < 1 ||
    month > 12 ||
    year < 1000
  ) {
    return '---';
  }

  const monthName = months[month - 1];
  return `${String(day).padStart(2, '0')} ${monthName} ${year}`;
}
