import {
  BATTERY_COLOR,
  CP,
  DG_BATTERY_COLOR,
  DG_COLOR,
  DG_SOLAR_BATTERY_COLOR,
  DG_SOLAR_COLOR,
  FUEL_COLOR,
  LLVD_Outage,
  LOAD_SHEDDING_COLOR,
  MAIN_BATTERY_COLOR,
  MAIN_COLOR,
  MAIN_SOLAR_BATTERY_COLOR,
  MAIN_SOLAR_COLOR,
  NAR,
  NO_PACKET,
  NO_UTILIZATION,
  SOLAR_BATTERY_COLOR,
  SOLAR_COLOR,
  TCH_COLOR,
  UNACCOUNTED_COLOR,
  Und_NP,
  UNDETERMINED_COLOR,
} from './SourceColors';

export const SourceColorMaping = {
  Main: MAIN_COLOR,
  Mains: MAIN_COLOR,
  'Main Total': MAIN_COLOR,
  'Main-Solar': MAIN_SOLAR_COLOR,
  'Main+Solar': MAIN_SOLAR_COLOR,
  'Main-Solar-Battery': MAIN_SOLAR_BATTERY_COLOR,
  'Main+Solar+Battery': MAIN_SOLAR_BATTERY_COLOR,
  'Solar-Battery': SOLAR_BATTERY_COLOR,
  'Solar+Battery': SOLAR_BATTERY_COLOR,
  Battery: BATTERY_COLOR,
  'Main-Battery': MAIN_BATTERY_COLOR,
  'Main+Battery': MAIN_BATTERY_COLOR,
  Undetermined: UNDETERMINED_COLOR,
  'No Packet': NO_PACKET,
  'No Utilization': NO_UTILIZATION,
  Solar: SOLAR_COLOR,
  DG: DG_COLOR,
  'DG-Solar': DG_SOLAR_COLOR,
  'DG+Solar': DG_SOLAR_COLOR,
  'DG-Battery': DG_BATTERY_COLOR,
  'DG+Battery': DG_BATTERY_COLOR,
  'DG-Solar-Battery': DG_SOLAR_BATTERY_COLOR,
  'DG+Solar+Battery': DG_SOLAR_BATTERY_COLOR,
  'Unaccounted Hours': UNACCOUNTED_COLOR,

  'LLVD Outage': LLVD_Outage,
  'BLVD Outage': '#890000',
  'DG+Solar+BLVD Outage': '#86A182',
  'DG+Solar+LLVD Outage': '#999191',
  'Main+Solar+BLVD Outage': '#4A6796',
  'Main+Solar+LLVD Outage': '#4A576C',
  'Solar+BLVD Outage': '#6B710D',
  'Solar+LLVD Outage': '#A0A81C',
};
