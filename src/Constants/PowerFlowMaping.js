export const processAndGroupData = (dataObject, itemsPerLine) => {
  if (!dataObject && dataObject < 0) {
    return '---';
  }

  const values = Object.values(dataObject);
  const groupedData = [];

  for (let i = 0; i < values.length; i += itemsPerLine) {
    const group = values
      .slice(i, i + itemsPerLine)
      .map(value => {
        if (typeof value === 'number') {
          return value.toFixed(1); // Format to one decimal place
        }
        return value; // Keep non-numeric values as they are
      })
      .join(' | ');
    groupedData.push(group);
  }

  return groupedData;
};

export const processAndGroupDataForPu = (dataObject, itemsPerLine) => {
  if (!dataObject && dataObject < 0) {
    return '---';
  }

  const values = Object.values(dataObject);

  if (values.length === 0 || !Array.isArray(values[0])) {
    // Handle cases where the data is not in the expected format
    return '---';
  }

  const innerValues = values[0]; // Access the inner array
  const groupedData = [];

  for (let i = 0; i < innerValues.length; i += itemsPerLine) {
    const group = innerValues
      .slice(i, i + itemsPerLine)
      .map(value => {
        if (typeof value === 'number') {
          return value.toFixed(1); // Format to one decimal place
        }
        return value; // Keep non-numeric values as they are
      })
      .join(' | ');
    groupedData.push(group);
  }

  return groupedData;
};
