import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import TowerSvg from '../assets/svgs/TowerSvg';
import SolarSvg from '../assets/svgs/SolarSvg';
import LightingSvg from '../assets/svgs/LightingSvg';
import BatterySvg from '../assets/svgs/BatterySvg';
import DGSvg from '../assets/svgs/DGSvg';
import {useThemeStyles} from './useThemeStyles';

export const PowerSourceSvgsMaping = powersource => {
  const istheme = useSelector(state => state.theme.theme);

  useEffect(() => {
    setIsDark(istheme === 'dark');
  }, [istheme]);
  const [isDark, setIsDark] = useState(false);

  const siteIdColor = !isDark ? '#FFFFFF' : '#0E121A';
  const themeStyles = useThemeStyles();

  switch (powersource) {
    case 'Mains':
    case 'Main':
      return (
        <TowerSvg color={themeStyles.textColor} style={{marginTop: '1%'}} />
      );
    case 'Solar':
      return (
        <SolarSvg color={themeStyles.textColor} style={{marginTop: '1%'}} />
      );
    case 'Solar+Battery':
    case 'Solar+BLVD Outage':
    case 'DG+Battery':
    case 'Main+Battery':
    case 'Main-Solar':
    case 'Main+Solar':
    case 'Main-Solar-Battery':
    case 'Main+Solar+Battery':
    case 'Solar-Battery':
    case 'Solar+Battery':
    case 'DG-Solar':
    case 'DG+Solar':
    case 'DG-Battery':
    case 'DG-Solar-Battery':
    case 'DG+Solar+Battery':
    case 'LLVD Outage':
    case 'BLVD Outage':
    case 'DG+Solar+BLVD Outage':
    case 'DG+Solar+LLVD Outage':
    case 'Main+Solar+BLVD Outage':
    case 'Main+Solar+LLVD Outage':
    case 'Solar+LLVD Outage':
      return <LightingSvg color={siteIdColor} style={{marginTop: '1%'}} />;
    case 'Battery':
      return <BatterySvg color={siteIdColor} style={{marginTop: '1%'}} />;
    case 'DG':
      return <DGSvg color={siteIdColor} style={{marginTop: '1%'}} />;
    default:
      return null;
  }
};
