import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  GET_ENERGY_CONSUMPTIONS,
  GET_INFRA_DETAILS,
  GET_SITE_ALARMS,
  GET_SITE_LOAD_TREND,
  GET_SOURCE_UTILIZATION,
  Request_Types,
} from '../api/uri';
import {ApiCaller} from './ApiCaller';
import {DayFilter} from '../Constants/DateFilter';
import moment from 'moment';

// ✅ Fetch function that takes `siteId` as an argument
export const fetchSiteLoadTrend = async siteId => {
  try {
    if (!siteId) return [];

    const token = await AsyncStorage.getItem('Authorization');
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayFormatted = yesterday.toISOString().split('T')[0];

    const response = await ApiCaller({
      method: Request_Types.GET,
      url: `${GET_SITE_LOAD_TREND(siteId, yesterdayFormatted, DayFilter())}`,
      headers: {Authorization: token},
    });

    return response.data?.data || [];
  } catch (error) {
    console.error('Site Load Chart Fetch Error:', error);
    return [];
  }
};

export const fetchPowerDuty = async ({
  siteId,
  startDate,
  endDate,
  utilization,
}) => {
  if (!startDate || !endDate) return;

  try {
    if (siteId) {
      const token = await AsyncStorage.getItem('Authorization');
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_SOURCE_UTILIZATION(
          siteId,
          endDate,
          startDate,
          utilization,
        )}`,
        headers: {Authorization: token},
      });

      return response.data?.data || [];
    }
  } catch (error) {
    console.error('Power Source Fetch Error:', error);
  }
};

export const fetchCurrentSource = async siteId => {
  try {
    const yesterday = moment().subtract(0, 'days').format('YYYY-MM-DD');

    const token = await AsyncStorage.getItem('Authorization');

    if (!token) {
      console.error('Authorization token missing');
      return [];
    }

    const response = await ApiCaller({
      method: 'GET',
      url: `${GET_ENERGY_CONSUMPTIONS(siteId, yesterday, yesterday)}`,
      headers: {Authorization: token},
    });

    if (response.data?.data?.data) {
      return response.data.data.data.map(item => ({
        source: item.name,
        startTime: moment(item.dt).valueOf(),
        endTime: moment(item.dt).add(5, 'minutes').valueOf(),
      }));
    } else {
      console.warn('No data received for energy consumption');
      return [];
    }
  } catch (error) {
    console.error('Power Source Fetch Error:', error);
    return []; // Ensure function always returns an array
  }
};

export const fetchInfraDetails = async id => {
  if (!id) return;
  try {
    const token = await AsyncStorage.getItem('Authorization');
    const response = await ApiCaller({
      method: Request_Types.GET,
      url: `${GET_INFRA_DETAILS(id)}`,
      headers: {Authorization: token},
    });
    return response.data?.data || [];
  } catch (error) {
    console.error('Infra Details Fetch Error:', error);
  }
};
export const fetchSiteAlarms = async id => {
  if (!id) return;

  try {
    const token = await AsyncStorage.getItem('Authorization');
    const response = await ApiCaller({
      method: Request_Types.GET,
      url: `${GET_SITE_ALARMS(id)}`,
      headers: {Authorization: token},
    });
    return response.data?.data || [];
  } catch (error) {
    console.error('Power Source Fetch Error:', error);
  }
};
