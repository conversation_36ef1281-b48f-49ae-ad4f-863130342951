import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {LoginApi} from '../api/uri';
import {DeviceEventEmitter} from 'react-native';

import {AuthContext} from '../Context/AuthContext';

// Create axios instance
const axiosInstance = axios.create();

// Event name for token expiration
export const TOKEN_EXPIRED_EVENT = 'TOKEN_EXPIRED';

axiosInstance.interceptors.request.use(
  async config => {
    console.log('🚀 API Request:', config.method?.toUpperCase(), config.url);
    const token = await AsyncStorage.getItem('Authorization');
    if (token) {
      config.headers.Authorization = token;
    }
    return config;
  },
  error => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  },
);

axiosInstance.interceptors.response.use(
  response => {
    console.log('✅ API Response:', response.status, response.config.url);
    return response;
  },
  async error => {
    console.error('❌ API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    });
    
    // Safety check - if error doesn't have config, we can't proceed
    if (!error.config) {
      return Promise.reject(error);
    }

    // Safety check - if error doesn't have config, we can't proceed
    if (!error.config) {
      return Promise.reject(error);
    }

    const originalRequest = error.config;

    // Check if error is due to token expiration (401 status)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Clear the expired token
        await AsyncStorage.removeItem('Authorization');

        // Emit an event that can be listened to by components
        DeviceEventEmitter.emit(TOKEN_EXPIRED_EVENT);
      } catch (e) {
        console.error('Error during auth handling:', e);
      }
    }

    return Promise.reject(error);
  },
);

export async function ApiCaller({
  url = '',
  method,
  params = {},
  data = {},
  headers = {},
}) {
  try {
    const resp = await axiosInstance({
      method,
      url,
      responseType: 'json',
      data,
      params,
      headers,
    });
    return resp;
  } catch (error) {
    console.error('API Error:', error.response?.data || error.message);
    throw error;
  }
}
export async function testInterceptors() {
  // Test request interceptor with better error handling
  try {
    await axiosInstance({
      method: 'GET',
      url: 'https://httpstat.us/401', // This will return a 401 status
      responseType: 'json',
      // Add timeout to prevent hanging
      timeout: 10000,
    });
  } catch (error) {
    // Don't rethrow the error
  }
}
