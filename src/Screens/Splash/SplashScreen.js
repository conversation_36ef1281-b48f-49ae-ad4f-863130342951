import {StyleSheet, View, Animated, Appearance} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import Logo from '../../assets/svgs/Logo.js';

import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from '../../Context/AuthContext.js';

const SplashScreen = () => {
  const navigation = useNavigation();
  const {setToken} = useAuth();

  // Animation reference
  const scaleAnim = useRef(new Animated.Value(0.02)).current;
  const [isDark, setIsDark] = useState('dark');

  useEffect(() => {
    // Theme detection
    const subscription = Appearance.addChangeListener(({colorScheme}) => {
      setIsDark(colorScheme === 'dark');
    });

    // Start animation
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Navigate after animation
    const timeout = setTimeout(async () => {
      try {
        // Check if user is authenticated
        const user = await AsyncStorage.getItem('Authorization');

        // Check if user has completed the tour
        const hasCompletedTour = await AsyncStorage.getItem('hasCompletedTour');

        // Check if this is first app launch
        const isFirstLaunch = await AsyncStorage.getItem('isFirstLaunch');

        if (user == null) {
          // User is not authenticated
          if (isFirstLaunch === null) {
            // First time launching the app
            await AsyncStorage.setItem('isFirstLaunch', 'false');
            navigation.replace('TourComponent');
          } else {
            // Not first launch, go to onboarding
            navigation.replace('onBoarding');
          }
        } else {
          // User is authenticated
          setToken(user);

          if (hasCompletedTour === null) {
            // User hasn't seen the tour yet
            navigation.replace('TourComponent');
          } else {
            // User has seen the tour, go to main screen
            navigation.replace('LandingScreen');
          }
        }
      } catch (error) {
        console.error('Error during navigation decision:', error);
        // Default to onBoarding in case of error
        navigation.replace('onBoarding');
      }
    }, 1500); // Adjusted to 1.5 seconds for smoother transition

    return () => {
      subscription.remove();
      clearTimeout(timeout);
    };
  }, [navigation, scaleAnim, setToken]);

  return (
    <View
      style={[
        styles.container,
        {backgroundColor: isDark ? '#0C121D' : '#F1F1F1'},
      ]}>
      <Animated.View
        style={{
          transform: [{scale: scaleAnim}],
        }}>
        <Logo color={'#FFFFFF'} />
      </Animated.View>
    </View>
  );
};

export default SplashScreen;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
});
