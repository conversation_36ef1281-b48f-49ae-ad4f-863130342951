import {
  Dimensions,
  StyleSheet,
  Text,
  View,
  TouchableWithoutFeedback,
  Modal,
  Pressable,
} from 'react-native';
import React, {useRef, useEffect, useState, memo} from 'react';
import {Svg<PERSON><PERSON>, SVGRenderer} from '@wuba/react-native-echarts';
import * as echarts from 'echarts/core';
import {Gauge<PERSON>hart} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
} from 'echarts/components';
import {ApiCaller} from '../../../middleWare/ApiCaller';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GET_SITE_LOAD_TREND, Request_Types} from '../../../api/uri';
import {DayFilter} from '../../../Constants/DateFilter';
import {color} from 'echarts';
import {Fonts} from '../../../styles/fonts';
import SiteLoadArrowSvg from '../../../assets/svgs/SiteLoadArrowSvg';
import {useThemeStyles} from '../../../Constants/useThemeStyles';
import {fetchSiteLoadTrend} from '../../../middleWare/ApiCallerFunctions';
import GreenDotSvg from '../../../assets/svgs/GreenDotSvg';

// Register ECharts components
echarts.use([
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  GridComponent,
  SVGRenderer,
  GaugeChart,
]);
const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

const E_HEIGHT = screenHeight * 0.2;
const E_WIDTH = screenWidth * 0.26;

const ChartComponent = ({
  option,
  onPress,
  showTooltips,
  percentage,
  themeStyles,
}) => {
  const chartRef = useRef(null);

  useEffect(() => {
    let chart;
    if (chartRef.current) {
      chart = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        width: E_WIDTH,
        height: E_HEIGHT,
      });
      chart.setOption(option);
    }
    return () => chart?.dispose();
  }, [option]);

  return (
    <View style={styles.chartWrapper}>
      <SvgChart
        ref={chartRef}
        style={{
          width: E_WIDTH,
          height: E_HEIGHT,
        }}
      />
      <Pressable style={styles.touchableOverlay} onPress={onPress} />
      {showTooltips && (
        <View style={styles.tooltipWrapper}>
          <View style={styles.tooltip}>
            <View style={styles.textContainer}>
              <View style={styles.leftContent}>
                <GreenDotSvg style={styles.greenDot} color={'#126CFF'} />
                <Text style={[styles.tooltipText]}>Demand Load </Text>
              </View>
              <View style={styles.valueContainer}>
                <Text
                  style={[
                    styles.tooltipText,
                    {color: themeStyles.textColor, fontSize: 16},
                  ]}>
                  {`${option.series[0].data[1].value?.toFixed(1) || 0}`}
                </Text>
                <Text
                  style={[
                    styles.tooltipText,
                    {color: themeStyles.textColor, fontSize: 10},
                  ]}>
                  {' A'}
                </Text>
              </View>
            </View>
            <View style={styles.textContainer}>
              <View style={styles.leftContent}>
                <GreenDotSvg style={styles.greenDot} color={'#00BE4A'} />
                <Text style={[styles.tooltipText]}>Actual Load</Text>
              </View>
              <View style={styles.valueContainer}>
                <Text
                  style={[
                    styles.tooltipText,
                    {color: themeStyles.textColor, fontSize: 16},
                  ]}>
                  {`${option.series[0].data[0].value?.toFixed(1) || 0}`}
                </Text>
                <Text
                  style={[
                    styles.tooltipText,
                    {color: themeStyles.textColor, fontSize: 10},
                  ]}>
                  {' A'}
                </Text>
              </View>
            </View>
            <View style={styles.textContainer}>
              <View style={styles.leftContent}>
                <GreenDotSvg style={styles.greenDot} color={'#FFFFFF'} />
                <Text style={[styles.tooltipText]}>Difference</Text>
              </View>

              <View style={styles.valueContainer}>
                <SiteLoadArrowSvg
                  style={{position: 'absolute', top: 11, right: 55}}
                />
                <Text
                  style={[
                    styles.tooltipText,
                    {color: themeStyles.textColor, fontSize: 16},
                  ]}>
                  {`${Math.abs(percentage).toFixed(1)}`}
                </Text>
                <Text
                  style={[
                    styles.tooltipText,
                    {color: themeStyles.textColor, fontSize: 10},
                  ]}>
                  {'%'}
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};
const CustomTooltip = ({values}) => (
  <View style={styles.tooltip}>
    {values.map((item, index) => (
      <Text
        key={index}
        style={[
          styles.tooltipText,
          {color: '#fff', marginBottom: index === 0 ? 4 : 0},
        ]}>
        {`${item.name}: ${item.value.toFixed(2)} kW`}
      </Text>
    ))}
  </View>
);
const SiteLoadChart = memo(({setLoader, siteValue, siteLoad}) => {
  const themeStyles = useThemeStyles();
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(istheme === 'dark');
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [percentage, setPercentage] = useState(0);
  const [CurrentLoad, setCurrentLoad] = useState();
  const [DemandLoad, setDemandLoad] = useState();
  const [showTooltips, setShowTooltips] = useState(false);

  const handlePress = () => {
    setShowTooltips(prev => !prev);
  };

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchSiteLoadTrend(isSiteID); // ✅ Pass siteId here

      setSiteLoad(data);
    };

    fetchData();
    const interval = setInterval(() => fetchData(isSiteID), 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [isSiteID]);
  const latestData = siteLoad.length > 0 ? siteLoad[siteLoad.length - 1] : null;
  const loadCurr = latestData ? parseFloat(latestData.dcanaloadcurr) : 0;
  const demandLoad = latestData ? parseFloat(latestData.demandload) : 0;

  useEffect(() => {
    if (siteLoad.length > 0) {
      const latestData = siteLoad[siteLoad.length - 1];
      const loadCurr = parseFloat(latestData?.dcanaloadcurr || 0);
      const demandLoad = parseFloat(latestData?.demandload || 0);

      if (demandLoad !== 0) {
        const calculation = (loadCurr * 100) / demandLoad;
        setCurrentLoad(loadCurr);
        setDemandLoad(demandLoad);
        setPercentage(100 - calculation);
      } else {
        setPercentage(0);
      }
    } else {
      setPercentage(0);
    }
  }, [siteLoad]);
  // Gauge chart configuration
  const demandLoadMax = Math.ceil(demandLoad / 100) * 100 || 100; // Ensures nearest hundredth
  const loadCurrMax = demandLoad; // Current Load max value must be Demand Load
  const option = {
    tooltip: {
      show: true,
      trigger: 'item',
      triggerOn: 'none',
      backgroundColor: themeStyles.tooltipColor || 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      borderWidth: 0,
      padding: [8, 12],
      formatter: function (params) {
        return `${params.name}: ${params.value.toFixed(2)} kW`;
      },
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontFamily: Fonts.BaiJamjuree_Medium,
      },
      position: 'inside',
      extraCssText: 'z-index: 9999; pointer-events: none;',
    },
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        pointer: {show: false},
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          itemStyle: {
            borderWidth: 2,
            borderColor: '#E9EEF40D',
          },
        },
        axisLine: {
          lineStyle: {
            width: 10,
            color: [[1, '#E9EEF40D']],
          },
        },
        splitLine: {show: false},
        axisTick: {show: false},
        axisLabel: {show: false},
        data: [
          {
            value: loadCurr,
            name: 'Load Current',
            itemStyle: {color: '#00BE4A'},
            max: loadCurrMax,
          },
          {
            value: demandLoad,
            name: 'Demand Load',
            itemStyle: {color: '#126CFF'},
            max: demandLoadMax,
          },
        ],
        title: {show: false},
        detail: {
          show: true,
          formatter: value => {
            return ['{value|' + percentage.toFixed(0) + '}', '{unit|%}'].join(
              '',
            );
          },
          rich: {
            value: {
              fontSize: 16,
              fontFamily: Fonts.BaiJamjuree_Medium,
              color: themeStyles.textColor,
            },
            unit: {
              fontSize: 12,
              fontFamily: Fonts.BaiJamjuree_Medium,
              color: themeStyles.textColor,
            },
          },
          offsetCenter: ['18%', '10%'],
        },
      },
    ],
  };
  const arrowColor = percentage < 0 ? 'red' : '#00BE4A';

  return (
    <View style={styles.container}>
      <View style={styles.chartContainer}>
        <ChartComponent
          option={option}
          onPress={handlePress}
          showTooltips={showTooltips}
          percentage={percentage}
          themeStyles={themeStyles}
        />
      </View>
      <View style={styles.arrowContainer}>
        <SiteLoadArrowSvg color={arrowColor} />
      </View>
    </View>
  );
});

export default SiteLoadChart;

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: '-20%',
    position: 'relative',
  },
  chartContainer: {
    position: 'relative',
    width: E_WIDTH,
    height: E_HEIGHT,
    zIndex: 2,
  },
  pressableWrapper: {
    width: '100%',
    height: '100%',
  },
  chartWrapper: {
    position: 'relative',
    width: E_WIDTH,
    height: E_HEIGHT,
  },
  touchableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
  },
  tooltipWrapper: {
    position: 'absolute',
    top: '50%',
    right: '2%',
    transform: [{translateX: -75}, {translateY: -30}], // Adjust these values as needed
    zIndex: 1000,
  },
  tooltip: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 12,
    borderRadius: 8,
    minWidth: 230,
    height: '85%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    justifyContent: 'space-between',
  },
  tooltipText: {
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
    paddingVertical: 2,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline', // This will align the text at their baselines
  },
  arrowContainer: {
    marginLeft: '7%',
    position: 'absolute',
    top: '46%',
    left: '15%',
    zIndex: 1,
  },
  textContainer: {
    height: '30%',
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftContent: {
    flexDirection: 'row',

    alignItems: 'center',
  },
});
