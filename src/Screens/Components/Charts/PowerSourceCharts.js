import React, {useRef, useEffect, useState, useCallback, useMemo} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {Svg<PERSON><PERSON>, <PERSON><PERSON>enderer} from '@wuba/react-native-echarts';
import moment from 'moment';
import * as echarts from 'echarts/core';
import {CustomChart} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
} from 'echarts/components';
import {ApiCaller} from '../../../middleWare/ApiCaller';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GET_ENERGY_CONSUMPTIONS} from '../../../api/uri';
import {
  BATTERY_COLOR,
  CP,
  DG_BATTERY_COLOR,
  DG_COLOR,
  DG_SOLAR_BATTERY_COLOR,
  DG_SOLAR_COLOR,
  FUEL_COLOR,
  LLVD_Outage,
  LOAD_SHEDDING_COLOR,
  MAIN_BATTERY_COLOR,
  MAIN_COLOR,
  MAIN_SOLAR_BATTERY_COLOR,
  MAIN_SOLAR_COLOR,
  NAR,
  NO_PACKET,
  NO_UTILIZATION,
  SOLAR_BATTERY_COLOR,
  SOLAR_COLOR,
  TCH_COLOR,
  UNACCOUNTED_COLOR,
  Und_NP,
  UNDETERMINED_COLOR,
} from '../../../Constants/SourceColors';
import {DayFilter_7} from '../../../Constants/DateFilter';
import {useThemeStyles} from '../../../Constants/useThemeStyles';
import {colors} from '../../../Constants/ColorsSource';
import {fetchCurrentSource} from '../../../middleWare/ApiCallerFunctions';
import {Fonts} from '../../../styles/fonts';

echarts.use([
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  GridComponent,
  SVGRenderer,
  CustomChart,
]);

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

const E_HEIGHT = screenHeight * 0.123;
const E_WIDTH = screenWidth * 0.5;

const ChartComponent = ({option, onPress}) => {
  const chartRef = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      const chart = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        width: E_WIDTH,
        height: E_HEIGHT,
      });
      chart.setOption(option);

      return () => chart.dispose();
    }
  }, [option]);

  return (
    <View
      style={{
        position: 'relative',
        borderRadius: 10,
        overflow: 'hidden',
      }}>
      <SvgChart ref={chartRef} style={{width: E_WIDTH, height: E_HEIGHT}} />

      <TouchableOpacity
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: E_WIDTH,
          height: E_HEIGHT,
          backgroundColor: 'transparent',
        }}
        onPress={onPress}
        activeOpacity={1}
      />
    </View>
  );
};

const PowerSourceCharts = ({setPsuModal, isExpanded}) => {
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(istheme === 'dark');
  const [siteId, setSiteId] = useState();
  const [chartData, setChartData] = useState([]);
  const [siteIdVersion, setSiteIdVersion] = useState(0);
  const [chartRefresh, setChartRefresh] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const isSiteID = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (site) setSiteId(site);
        setSiteIdVersion(prevVersion => prevVersion + 1);
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    })();
  }, [siteId]);

  const fetchCurrentSource = useCallback(async () => {
    try {
      setLoading(true);

      // Get today's date at midnight (00:00:00)
      const startDate = moment().startOf('day').toISOString();

      // Get current time
      const endDate = moment().toISOString();

      const token = await AsyncStorage.getItem('Authorization');
      if (!token) return console.error('Authorization token missing');

      const response = await ApiCaller({
        method: 'GET',
        url: `${GET_ENERGY_CONSUMPTIONS(siteId, startDate, endDate)}`,
        headers: {Authorization: token},
      });

      if (response.data?.data?.data) {
        // Process the data with Pakistan timezone
        const processedData = response.data.data.data.map(item => {
          // Convert to Pakistan timezone
          const dt = moment(item.dt).utcOffset('+05:00');
          const startOfDay = moment(dt).startOf('day');
          const millisSinceStartOfDay = dt.diff(startOfDay);

          return {
            source: item.name,
            startTime: millisSinceStartOfDay, // Store as ms since start of day
            endTime: millisSinceStartOfDay + 5 * 60 * 1000, // 5 minutes later
            actualTime: dt.format('HH:mm'), // Store formatted time for display
            actualDate: dt.format('YYYY-MM-DD'), // Store date for tooltip
          };
        });

        // Log processed data for debugging

        setChartData(processedData);
      }
    } catch (error) {
      console.error('Power Source Fetch Error:', error);
    } finally {
      setLoading(false);
    }
  }, [siteId]);

  useEffect(() => {
    fetchCurrentSource();
  }, [siteId, fetchCurrentSource, chartRefresh]);

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        if (isSiteID) {
          setSiteId(isSiteID);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };

    fetchSiteId();
  }, [isSiteID]);

  useEffect(() => {
    const refreshChart = async () => {
      await AsyncStorage.getItem('SelectedSiteId');
      setChartRefresh(prev => !prev);
    };
    refreshChart();
  }, [siteId]);

  const option = useMemo(
    () => ({
      grid: {
        left: '5%',
        right: '8%', // Increased right margin to accommodate the 24:00 label
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'value', // Value type for better control
        min: 0, // Start of day (00:00)
        max: 24 * 60 * 60 * 1000, // End of day (24:00)
        interval: 8 * 60 * 60 * 1000, // 12-hour intervals (in milliseconds)
        splitNumber: 3, // 2 splits for 24 hours (00:00, 12:00, 24:00)
        splitLine: {show: false},
        axisLabel: {
          show: true,
          formatter: function (value) {
            // Convert milliseconds since start of day to time format
            const hours = Math.floor(value / (60 * 60 * 1000));
            // Special case for 24:00 to ensure it's visible

            return `${String(hours).padStart(2, '0')}:00`;
          },
          color: '#fff',
          fontFamily: Fonts.BaiJamjuree_Regular,
          padding: [0, 0, 0, 8], // Adjusted padding
          margin: 8, // Added margin
          fontSize: 9, // Smaller font size
          align: 'center', // Center alignment
        },
        axisLine: {show: false, lineStyle: {color: '#fff'}},
        axisTick: {
          show: false,
          alignWithLabel: true,
          lineStyle: {color: '#fff'},
        },
      },
      yAxis: {
        type: 'category',
        data: ['Power Utilization'],
        axisTick: {show: false},
        axisLabel: {show: false},
        splitLine: {show: false},
        axisLine: {show: false},
      },
      tooltip: {
        show: true,
        trigger: 'item',
        backgroundColor: 'rgba(50, 50, 50, 0.8)',
        borderColor: 'rgba(50, 50, 50, 0.8)',
        borderWidth: 1,
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontFamily: Fonts.BaiJamjuree_Medium,
        },
        formatter: params => {
          const source = params.value[2];
          const actualTime = params.value[3];
          const actualDate = params.value[4];

          return `Source: ${source}\nDate: ${actualDate}\nTime: ${actualTime}`;
        },
      },
      series: [
        {
          type: 'custom',
          selectedMode: null,
          encode: {x: 0, y: 1, tooltip: [0, 1, 2, 3, 4]},
          emphasis: {
            itemStyle: {
              disabled: true,
            },
          },
          renderItem: (params, api) => {
            const start = api.coord([api.value(0), 0]);
            const end = api.coord([api.value(1), 0]);
            const width = Math.max(1, end[0] - start[0]);

            // Ensure width is not too large (cap at 5 minutes worth of pixels)
            const fiveMinutesInPixels =
              api.coord([5 * 60 * 1000, 0])[0] - api.coord([0, 0])[0];
            const adjustedWidth = Math.min(width, fiveMinutesInPixels);

            return {
              type: 'rect',
              shape: {
                x: start[0],
                y: start[1] - 10,
                width: adjustedWidth,
                height: 20,
                r: [5, 5, 5, 5],
              },
              style: {
                fill: colors[api.value(2)] || themeStyles.powerSourceChart,
                stroke: 'none',
                lineWidth: 2,
              },
            };
          },
          data: chartData.map(item => [
            item.startTime,
            item.endTime,
            item.source,
            item.actualTime,
            item.actualDate,
          ]),
        },
      ],
    }),
    [chartData, themeStyles.powerSourceChart],
  );

  const handleChartPress = () => {
    setPsuModal(true);
  };

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="small" color="#FF7F02" />
        </View>
      ) : (
        <ChartComponent option={option} onPress={handleChartPress} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '20%',
    // marginTop: '-5%',
    marginLeft: '10%',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '  10%',
  },
});

export default PowerSourceCharts;
