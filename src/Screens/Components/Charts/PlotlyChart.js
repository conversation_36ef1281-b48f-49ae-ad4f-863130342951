import React from 'react';
import {Dimensions} from 'react-native';
import {WebView} from 'react-native-webview';

// Decoding function
function base64ToUint8Array(base64) {
  const binary_string = atob(base64);
  const len = binary_string.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binary_string.charCodeAt(i);
  }
  return bytes;
}

function decodeBdata(bdata, dtype) {
  const bytes = base64ToUint8Array(bdata);

  let typedArray;
  switch (dtype) {
    case 'i1': // Int8
      typedArray = new Int8Array(bytes.buffer);
      break;
    case 'i2': // Int16
      if (bytes.length % 2 !== 0) {
        console.warn('Invalid i2 data length');
        return [];
      }
      typedArray = new Int16Array(bytes.buffer);
      break;
    case 'f8': // Float64
      if (bytes.length % 8 !== 0) {
        console.warn('Invalid f8 data length');
        return [];
      }
      typedArray = new Float64Array(bytes.buffer);
      break;
    default:
      console.warn('Unknown dtype:', dtype);
      return [];
  }

  return Array.from(typedArray);
}

// Data preparation function
function preparePlotlyData(parsedJson) {
  if (!parsedJson || !Array.isArray(parsedJson.data)) {
    return {data: [], layout: parsedJson?.layout || {}};
  }

  const cleanData = parsedJson.data.map(trace => {
    const newTrace = {...trace};

    if (newTrace.x && newTrace.x.bdata) {
      newTrace.x = decodeBdata(newTrace.x.bdata, newTrace.x.dtype);
    }

    if (newTrace.y && newTrace.y.bdata) {
      newTrace.y = decodeBdata(newTrace.y.bdata, newTrace.y.dtype);
    }

    return newTrace;
  });

  return {
    data: cleanData,
    layout: parsedJson.layout,
  };
}

const PlotlyChart = ({plotlyJson}) => {
  const preparedData = preparePlotlyData(plotlyJson);

  const htmlContent = `
  <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    </head>
    <body style="margin:0; padding:0;">
     <div id="chart" style="width:380px; height:300px; margin-top:-50px;"></div>
      <script>
        const data = ${JSON.stringify(preparedData.data)};
        const layout = ${JSON.stringify(preparedData.layout)};
        layout.title = '';
        Plotly.newPlot('chart', data, layout, {responsive: true, displayModeBar: false});
      </script>
    </body>
  </html>
`;

  return (
    <WebView
      originWhitelist={['*']}
      source={{html: htmlContent}}
      style={{width: 330, height: 250}}
      javaScriptEnabled
      domStorageEnabled
    />
  );
};

export default PlotlyChart;
