import {
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useRef, useEffect, useState, useCallback} from 'react';
import {Svg<PERSON><PERSON>, SVGRenderer} from '@wuba/react-native-echarts';
import * as echarts from 'echarts/core';
import {
  BarChart as EChartsBarChart,
  LineChart as EChartsLineChart,
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
} from 'echarts/components';
import {ApiCaller} from '../../../middleWare/ApiCaller';

import {GET_SOURCE_UTILIZATION, Request_Types} from '../../../api/uri';
import {useSelector} from 'react-redux';

import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  Day<PERSON><PERSON>er,
  Last<PERSON><PERSON>hFilter,
  LastWeek<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  YesterdayFilter,
} from '../../../Constants/DateFilter';
import moment from 'moment';

import {useThemeStyles} from '../../../Constants/useThemeStyles';
import {colors} from '../../../Constants/ColorsSource';

echarts.use([
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  GridComponent,
  SVGRenderer,
  EChartsBarChart,
  EChartsLineChart,
]);

function ChartComponent({option, isLandscape}) {
  const {width: screenWidth, height: screenHeight} = Dimensions.get('window');
  const chartRef = useRef(null);
  let E_HEIGHT;
  let E_WIDTH;
  if (isLandscape) {
    E_HEIGHT = screenHeight * 0.68;
    E_WIDTH = screenWidth * 0.97;
  } else {
    E_HEIGHT = screenHeight * 0.34;
    E_WIDTH = screenWidth * 0.9;
  }

  useEffect(() => {
    let chart;
    if (chartRef.current) {
      chart = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        width: E_WIDTH,
        height: E_HEIGHT,
      });

      chart.setOption(option);
    }
    return () => chart?.dispose();
  }, [option]);

  return <SvgChart ref={chartRef} style={{width: E_WIDTH, height: E_HEIGHT}} />;
}

const PowerCycleChart = ({
  setLoader,
  siteValue,
  selectedItem,
  isLandscape,
  utilization,
}) => {
  const istheme = useSelector(state => state.theme.theme);

  const [isDark, setIsDark] = useState(false);
  const [powerDuty, setPowerDuty] = useState([]);
  const [legendData, setLegendData] = useState([]);
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [hrs, setHrs] = useState();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [isLoading, setIsloading] = useState(false);
  const [chartUpdate, setChartUpdate] = useState(0); // Add a state for re-render
  const themeStyles = useThemeStyles();
  const [siteId, setSiteId] = useState('');
  useEffect(() => {
    const dateFilters = {
      Today: [DayFilter(), DayFilter()],
      Yesterday: [YesterdayFilter(), YesterdayFilter()],
      'This Week': [ThisWeekFilter(), DayFilter()],
      'Last Week': [
        LastWeekFilter().startOfLastWeek,
        LastWeekFilter().endOfLastWeek,
      ],
      'This Month': [
        ThisMonthFilter().startOfThisMonth,
        ThisMonthFilter().currentDate,
      ],
      'Last Month': [
        LastMonthFilter().startOfLastMonth,
        LastMonthFilter().endOfLastMonth,
      ],
    };
    const [start, end] =
      dateFilters[selectedItem?.name] || dateFilters['This Week'];
    setStartDate(start);

    setEndDate(end);
    if (
      selectedItem?.name == 'Today' ||
      selectedItem?.name == 'Yesterday' ||
      moment(end).isSame(moment(), 'day')
    ) {
      setHrs(1);
    } else {
      setHrs(24);
    }
  }, [selectedItem]);

  const [layoutReady, setLayoutReady] = useState(false);

  const handleLayout = () => {
    setLayoutReady(true);
  };
  useEffect(() => {
    setLegendData(
      dataKeys.filter(key => powerDuty.every(item => item[key] >= 0)),
    );
  }, [dataKeys, powerDuty]);

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (site) {
          setSiteId(site);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };

    fetchSiteId();
  }, [isSiteID]);

  const fetchPowerDuty = useCallback(async () => {
    if (!startDate || !endDate) return;

    try {
      if (siteId) {
        setIsloading(true);
        const token = await AsyncStorage.getItem('Authorization');
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_SOURCE_UTILIZATION(
            siteId,
            endDate,
            startDate,
            utilization,
          )}`,
          headers: {Authorization: token},
        });
        setPowerDuty(response.data?.data || []);
      }
    } catch (error) {
      console.error('Power Source Fetch Error:', error);
    } finally {
      setIsloading(false);
    }
  }, [startDate, endDate, siteId, utilization]);
  useEffect(() => {
    if (startDate && endDate) {
      // <-- Check if startDate and endDate are set
      fetchPowerDuty();
    }

    const interval = setInterval(() => {
      fetchPowerDuty();
    }, 5 * 60 * 1000); // 5 minutes
    setChartUpdate(prev => prev + 1);
    return () => clearInterval(interval);
  }, [startDate, endDate, fetchPowerDuty, siteId, utilization]);

  if (!powerDuty || [powerDuty].length === 0) {
    return <Text style={{color: 'black'}}>Loading...</Text>;
  }

  const getLastWeekDates = () => {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 (Sunday) to 6 (Saturday)

    // Calculate last week's Monday (7 days before this week's Monday)
    const lastWeekMonday = new Date(today);
    lastWeekMonday.setDate(today.getDate() - dayOfWeek - 6);

    // Generate array of dates for last week (only date numbers)
    const dates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(lastWeekMonday);
      date.setDate(lastWeekMonday.getDate() + i);
      dates.push(date.getDate().toString()); // Just the date number as string
    }

    return dates;
  };

  const categories = powerDuty.map((item, index) => {
    // Ensure we have a valid date string to work with
    if (!item.date) return `Day ${index + 1}`;

    // For hourly data (Today/Yesterday), handle time format directly
    if (hrs === 1) {
      // Handle simple time strings like "11 AM"
      if (typeof item.date === 'string') {
        const timeStr = item.date.trim();

        // Check if it's a simple time format with AM/PM
        if (/^\d{1,2}\s*(AM|PM|am|pm)$/.test(timeStr)) {
          const parts = timeStr.split(/\s+/);
          let hours = parseInt(parts[0], 10);
          const isPM = parts[1].toLowerCase() === 'pm';

          // Convert to 24-hour format
          if (isPM && hours < 12) hours += 12;
          if (!isPM && hours === 12) hours = 0;

          // Format with leading zeros
          return `${hours.toString().padStart(1, '')}`;
        }

        // If it contains a colon, it might be "HH:MM AM/PM"
        if (
          timeStr.includes(':') &&
          (timeStr.toLowerCase().includes('am') ||
            timeStr.toLowerCase().includes('pm'))
        ) {
          const [timePart, ampm] = timeStr.split(/\s+/);
          const [hoursStr, minutesStr] = timePart.split(':');
          let hours = parseInt(hoursStr, 10);
          const isPM = ampm.toLowerCase() === 'pm';

          // Convert to 24-hour format
          if (isPM && hours < 12) hours += 12;
          if (!isPM && hours === 12) hours = 0;

          return `${hours.toString().padStart(2, '0')}:${minutesStr}`;
        }
      }

      // If all else fails, return the original string
      return item.date;
    }

    // For Last Week, use pre-calculated dates if needed
    if (selectedItem?.name === 'Last Week') {
      try {
        // Try to parse the date
        const dateObj = moment(item.date);

        if (dateObj.isValid()) {
          return dateObj.format('DD'); // Only show the date number
        } else {
          // If parsing fails, use pre-calculated dates
          const lastWeekDates = getLastWeekDates();
          return index < lastWeekDates.length
            ? lastWeekDates[index]
            : `${index + 1}`;
        }
      } catch (error) {
        console.error('Date parsing error:', error);
        const lastWeekDates = getLastWeekDates();
        return index < lastWeekDates.length
          ? lastWeekDates[index]
          : `${index + 1}`;
      }
    }

    // For Last Month
    if (selectedItem?.name === 'Last Month') {
      try {
        const dateObj = moment(item.date);
        return dateObj.isValid() ? dateObj.format('DD') : `${index + 1}`;
      } catch (error) {
        return `${index + 1}`;
      }
    }

    // For other periods, keep existing logic
    try {
      const dateObj = moment(item.date);

      if (dateObj.isValid()) {
        if (selectedItem?.name === 'This Week') {
          return dateObj.format('DD MMM');
        } else if (selectedItem?.name === 'This Month') {
          return dateObj.format('DD');
        } else {
          return dateObj.format('YYYY-MM-DD');
        }
      }

      // If parsing fails, return a generic label
      return `Day ${index + 1}`;
    } catch (error) {
      console.error('Date parsing error:', error);
      return `Day ${index + 1}`;
    }
  });

  // Extract unique keys dynamically (excluding "date")
  const dataKeys =
    powerDuty.length > 0
      ? Object.keys(powerDuty[0]).filter(
          key =>
            key !== 'date' &&
            key !== 'Site Condition' &&
            powerDuty.some(item => item[key] > 0),
        )
      : [];
  const hoursKeys = dataKeys.filter(
    key => !['TCH', 'NAR', 'Fuel'].includes(key),
  );
  const maxHours = Math.max(
    ...powerDuty.flatMap(item => hoursKeys.map(key => item[key] || 0)),
  );

  const series = dataKeys.map(key => {
    const seriesItem = {
      name: key,
      type: ['TCH', 'NAR', 'Fuel'].includes(key) ? 'line' : 'bar',
      data: powerDuty.map(item => item[key]),

      itemStyle: {color: colors[key] || '#888'},
    };

    if (['TCH', 'NAR', 'Fuel'].includes(key)) {
      seriesItem.yAxisIndex = 0;
    } else if (['Main', 'LS'].includes(key)) {
      seriesItem.yAxisIndex = 1;
      seriesItem.stack = 'group1';
    } else {
      seriesItem.yAxisIndex = 1;
      seriesItem.stack = 'group2';
    }

    return seriesItem;
  });

  const option = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {type: 'shadow'},
      backgroundColor: themeStyles.tooltipColor,
      textStyle: {color: themeStyles.textColor},
      confine: true,

      z: 10,
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      top: 'top',
      textStyle: {color: themeStyles.textColor},
      pageIconColor: '#FF7E00',
      pageIconInactiveColor: '#96999E',
      pageTextStyle: {color: '#FFFFFF'},
      itemHeight: 12,
      z: 20,

      selector: false,
      data: legendData,
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {color: '#96999E'},
    },

    yAxis: [
      {
        type: 'value',
        name: '%',
        min: 0,
        max: 100,
        axisLabel: {color: '#96999E'},
      },
      {
        type: 'value',
        name: utilization,
        min: 0,
        max: Math.max(maxHours, hrs),
        axisLabel: {color: '#96999E'},
      },
    ],
    series,
  };

  return isLoading ? (
    <View style={styles.loaderContainer}>
      <ActivityIndicator size="large" color="#FF7F02" />
    </View>
  ) : (
    <View
      onLayout={handleLayout}
      style={[styles.container, isLandscape && styles.landscapeContainer]}>
      {layoutReady && (
        <ChartComponent
          option={option}
          isLandscape={isLandscape}
          key={chartUpdate}
        />
      )}
    </View>
  );
};

export default PowerCycleChart;

const styles = StyleSheet.create({
  container: {
    height: '60%',
    width: '100%',

    alignItems: 'center',
  },
  loaderContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  landscapeContainer: {
    marginLeft: '0%',
  },
});
