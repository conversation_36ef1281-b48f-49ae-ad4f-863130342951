import React, {useRef, useEffect, useState, useCallback, useMemo} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {Svg<PERSON><PERSON>, <PERSON><PERSON>enderer} from '@wuba/react-native-echarts';
import moment from 'moment';
import * as echarts from 'echarts/core';
import {CustomChart} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomInsideComponent,
} from 'echarts/components';
import {ApiCaller} from '../../../middleWare/ApiCaller';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GET_ENERGY_CONSUMPTIONS} from '../../../api/uri';
import {
  BATTERY_COLOR,
  CP,
  DG_BATTERY_COLOR,
  DG_COLOR,
  DG_<PERSON>OLAR_BATTERY_COLOR,
  DG_SOLAR_COLOR,
  FUEL_COLOR,
  LLVD_Outage,
  LOAD_SHEDDING_COLOR,
  MAIN_BATTERY_COLOR,
  MAIN_COLOR,
  MAIN_SOLAR_BATTERY_COLOR,
  MAIN_SOLAR_COLOR,
  NAR,
  NO_PACKET,
  NO_UTILIZATION,
  SOLAR_BATTERY_COLOR,
  SOLAR_COLOR,
  TCH_COLOR,
  UNACCOUNTED_COLOR,
  Und_NP,
  UNDETERMINED_COLOR,
} from '../../../Constants/SourceColors';
import {
  DayFilter,
  DayFilter_7,
  LastMonthFilter,
  LastWeekFilter,
  ThisMonthFilter,
  ThisWeekFilter,
  YesterdayFilter,
} from '../../../Constants/DateFilter';
import {Fonts} from '../../../styles/fonts';

echarts.use([
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  GridComponent,
  SVGRenderer,
  CustomChart,
  DataZoomInsideComponent,
]);

const PowerSourceUtilizationChart = ({
  setPsuModal,
  isExpanded,
  toggleAvailability = 'Utilization',
  selectedItem,
}) => {
  const [dimensions, setDimensions] = useState(Dimensions.get('window'));
  const chartRef = useRef(null);

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({window}) => {
      setDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  const chartDimensions = useMemo(() => {
    const isLandscape = dimensions.width > dimensions.height;

    if (isLandscape) {
      return {
        width: dimensions.width * 1.02,
        height: dimensions.height * 0.7,
      };
    }

    return {
      width: dimensions.width * 1.05,
      height: dimensions.height * 0.5,
    };
  }, [dimensions]);

  const ChartComponent = ({option}) => {
    const svgRef = useRef(null);

    useEffect(() => {
      if (svgRef.current) {
        const chart = echarts.init(svgRef.current, 'light', {
          renderer: 'svg',
          width: chartDimensions.width,
          height: chartDimensions.height,
        });

        chart.setOption(option);

        return () => {
          chart.dispose();
        };
      }
    }, [option, chartDimensions]);

    return (
      <View
        style={[
          styles.chartContainer,
          {
            width: chartDimensions.width,
            height: chartDimensions.height,
          },
        ]}>
        <SvgChart
          ref={svgRef}
          style={{
            width: chartDimensions.width,
            height: chartDimensions.height,
          }}
        />
      </View>
    );
  };

  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(istheme === 'dark');
  const [siteId, setSiteId] = useState();
  const [chartData, setChartData] = useState([]);
  const [siteIdVersion, setSiteIdVersion] = useState(0);
  const [chartRefresh, setChartRefresh] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [hrs, setHrs] = useState();
  useEffect(() => {
    const dateFilters = {
      Today: [moment().startOf('day'), moment()],
      Yesterday: [
        moment().subtract(1, 'day').startOf('day'),
        moment().subtract(1, 'day').endOf('day'),
      ],
      'This Week': [moment().startOf('week'), moment()],
      'Last Week': [
        moment().subtract(1, 'week').startOf('week'),
        moment().subtract(1, 'week').endOf('week'),
      ],
      'This Month': [moment().startOf('month'), moment()],
      'Last Month': [
        moment().subtract(1, 'month').startOf('month'),
        moment().subtract(1, 'month').endOf('month'),
      ],
    };

    const [start, end] =
      dateFilters[selectedItem?.name] || dateFilters['This Week'];
    setStartDate(start.toISOString());
    setEndDate(end.toISOString());
  }, [selectedItem]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (site) setSiteId(site);
        setSiteIdVersion(prevVersion => prevVersion + 1);
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    })();
  }, [siteId]);

  // Process the data based on the toggle
  const processMultiSourceData = useCallback(
    data => {
      if (toggleAvailability !== 'Utilization') {
        return data;
      } else {
        return data.map(item => {
          let {name, phasecurr, puoutcurr} = item;
          if (/(DG|dg|main|Main|solar|Solar)/.test(name)) {
            const allNonPositivePhaseCurr =
              phasecurr && Object.values(phasecurr).every(value => value <= 0);

            if (allNonPositivePhaseCurr) {
              name = name.replace(/DG|dg|main|Main/g, '');
            }

            if (/(solar|Solar)/.test(name) && (!puoutcurr || puoutcurr <= 0)) {
              name = name.replace(/solar|Solar/g, '');
            }

            name = name.replace(/^[+-]+|[+-]+$/g, '');
            name = name === '' ? 'No Utilization' : name;

            return {...item, name};
          }
          return item;
        });
      }
    },
    [toggleAvailability],
  );

  const fetchCurrentSource = useCallback(async () => {
    try {
      setLoading(true);

      const token = await AsyncStorage.getItem('Authorization');
      if (!token) return console.error('Authorization token missing');

      const response = await ApiCaller({
        method: 'GET',
        url: `${GET_ENERGY_CONSUMPTIONS(siteId, startDate, endDate)}`,
        headers: {Authorization: token},
      });

      if (response.data?.data?.data) {
        // Log raw data sample

        // Process the data based on toggle
        const rawData = response.data.data.data;
        const processedData = processMultiSourceData(rawData);

        // Group data by day
        const groupedData = processedData.reduce((acc, item) => {
          // Use moment.tz to explicitly set Pakistan timezone
          const dt = moment(item.dt).utcOffset('+05:00');

          const date = dt.format('YYYY-MM-DD');

          if (!acc[date]) acc[date] = [];

          acc[date].push({
            source: item.name,
            startTime: dt.valueOf(),
            endTime: dt.clone().add(5, 'minutes').valueOf(),
            phasecurr: item.phasecurr,
            puoutcurr: item.puoutcurr,
          });

          return acc;
        }, {});

        // Get the date range based on the selected filter
        let days = [];

        // Determine the number of days to display based on the selected filter
        if (
          selectedItem?.name === 'Today' ||
          selectedItem?.name === 'Yesterday'
        ) {
          days = [moment(startDate).format('YYYY-MM-DD')];
        } else if (
          selectedItem?.name === 'This Week' ||
          selectedItem?.name === 'Last Week'
        ) {
          days = Array.from({length: 7}, (_, i) =>
            moment(startDate).add(i, 'days').format('YYYY-MM-DD'),
          );
        } else if (
          selectedItem?.name === 'This Month' ||
          selectedItem?.name === 'Last Month'
        ) {
          const daysInMonth =
            moment(endDate).diff(moment(startDate), 'days') + 1;
          days = Array.from({length: daysInMonth}, (_, i) =>
            moment(startDate).add(i, 'days').format('YYYY-MM-DD'),
          );
        } else {
          days = Array.from({length: 7}, (_, i) =>
            moment(endDate)
              .subtract(6 - i, 'days')
              .format('YYYY-MM-DD'),
          );
        }

        // Transform the data for the chart
        const transformedData = days.flatMap((day, yIndex) => {
          const dayStart = moment
            .utc(day)
            .utcOffset('+05:00')
            .startOf('day')
            .valueOf();

          const dayData =
            groupedData[day]?.map(item => {
              const localStartTime = moment(item.startTime).utcOffset('+05:00');
              const localEndTime = moment(item.endTime).utcOffset('+05:00');

              const startOfDay = moment(localStartTime).startOf('day');
              const millisSinceStartOfDay = localStartTime.diff(startOfDay);

              return {
                ...item,
                startTime: millisSinceStartOfDay,
                endTime: millisSinceStartOfDay + 5 * 60 * 1000,
                yIndex,
                actualTime: localStartTime.format('HH:mm'),
              };
            }) || [];

          if (dayData.length > 0) {
          }

          return dayData;
        });

        setChartData(transformedData);
      }
    } catch (error) {
      console.error('Power Source Fetch Error:', error);
    } finally {
      setLoading(false);
    }
  }, [siteId, processMultiSourceData, startDate, endDate, selectedItem]);

  useEffect(() => {
    if (!startDate || !endDate) return;
    const delay = setTimeout(() => {
      fetchCurrentSource();
    }, 50);
    return () => clearTimeout(delay);
  }, [startDate, endDate]);

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');
        if (storedTheme !== null) {
          const parsedTheme = JSON.parse(storedTheme);
          setIsDark(parsedTheme);
        } else {
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching/parsing theme:', error);
        setIsDark(istheme === 'dark');
      }
    };

    fetchTheme();
  }, [istheme]);

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        if (isSiteID) {
          setSiteId(isSiteID);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };

    fetchSiteId();
  }, [isSiteID]);

  useEffect(() => {
    const refreshChart = async () => {
      await AsyncStorage.getItem('SelectedSiteId');
      setChartRefresh(prev => !prev);
    };
    refreshChart();
  }, [siteId]);

  const background = isDark ? '#FFFFFF4D' : '#171E2C';
  const colorMap = {
    Main: MAIN_COLOR,
    Mains: MAIN_COLOR,
    'Main-Solar': MAIN_SOLAR_COLOR,
    'Main+Solar': MAIN_SOLAR_COLOR,
    'Main-Solar-Battery': MAIN_SOLAR_BATTERY_COLOR,
    'Main+Solar+Battery': MAIN_SOLAR_BATTERY_COLOR,
    'Solar-Battery': SOLAR_BATTERY_COLOR,
    'Solar+Battery': SOLAR_BATTERY_COLOR,
    Battery: BATTERY_COLOR,
    'Main-Battery': MAIN_BATTERY_COLOR,
    'Main+Battery': MAIN_BATTERY_COLOR,
    Undetermined: UNDETERMINED_COLOR,
    'No Utilization': NO_UTILIZATION,
    Solar: SOLAR_COLOR,
    DG: DG_COLOR,
    'DG-Solar': DG_SOLAR_COLOR,
    'DG+Solar': DG_SOLAR_COLOR,
    'DG-Battery': DG_BATTERY_COLOR,
    'DG+Battery': DG_BATTERY_COLOR,
    'DG-Solar-Battery': DG_SOLAR_BATTERY_COLOR,
    'DG+Solar+Battery': DG_SOLAR_BATTERY_COLOR,
    'Unaccounted Hours': UNACCOUNTED_COLOR,
    LS: LOAD_SHEDDING_COLOR,
    TCH: TCH_COLOR,
    Fuel: FUEL_COLOR,
    'LLVD Outage': LLVD_Outage,
    'BLVD Outage': '#890000',
    'DG+Solar+BLVD Outage': '#86A182',
    'DG+Solar+LLVD Outage': '#999191',
    'Main+Solar+BLVD Outage': '#4A6796',
    'Main+Solar+LLVD Outage': '#4A576C',
    'Solar+BLVD Outage': '#6B710D',
    'Solar+LLVD Outage': '#A0A81C',
    'Und/NP': Und_NP,
    CP: CP,
    NAR: NAR,
  };

  const option = useMemo(() => {
    // Create a proper time scale for the x-axis
    const minTime = 0; // Start of day (00:00)
    const maxTime = 24 * 60 * 60 * 1000; // End of day (24:00)

    return {
      grid: {left: '5%', right: '5%', bottom: '10%', containLabel: true},
      xAxis: {
        type: 'value', // Change to value type for better control
        min: minTime,
        max: maxTime,
        splitNumber: 8,
        splitLine: {show: false},
        axisLabel: {
          show: true,
          formatter: function (value) {
            // Convert milliseconds since start of day to time format
            const hours = Math.floor(value / (60 * 60 * 1000));
            const minutes = Math.floor(
              (value % (60 * 60 * 1000)) / (60 * 1000),
            );
            return `${String(hours).padStart(2, '0')}:${String(
              minutes,
            ).padStart(2, '0')}`;
          },
          color: '#fff',
          fontFamily: Fonts.BaiJamjuree_Regular,
          padding: [10, 0, 0, 10],
        },
        axisLine: {show: true, lineStyle: {color: '#fff'}},
      },
      yAxis: {
        type: 'category',
        data:
          selectedItem?.name === 'Today' || selectedItem?.name === 'Yesterday'
            ? [moment(startDate).format('YYYY-MM-DD')]
            : selectedItem?.name === 'This Week' ||
              selectedItem?.name === 'Last Week'
            ? Array.from({length: 7}, (_, i) =>
                moment(startDate).add(i, 'days').format('YYYY-MM-DD'),
              )
            : selectedItem?.name === 'This Month' ||
              selectedItem?.name === 'Last Month'
            ? Array.from(
                {length: moment(endDate).diff(moment(startDate), 'days') + 1},
                (_, i) => moment(startDate).add(i, 'days').format('YYYY-MM-DD'),
              )
            : Array.from({length: 7}, (_, i) =>
                moment(endDate)
                  .subtract(6 - i, 'days')
                  .format('YYYY-MM-DD'),
              ),
        axisTick: {show: false},
        axisLabel: {
          show: true,
          align: 'right',
          verticalAlign: 'middle',
          margin: 15,
          padding: [0, 0, 0, 0],
          fontSize: 12,
          fontFamily: Fonts.BaiJamjuree_Medium,
          color: '#96999E',
          formatter: function (value) {
            return moment(value).format('DD MMM YY');
          },
        },
        splitLine: {show: false},
        axisLine: {show: false},
      },
      tooltip: {
        show: true,
        trigger: 'item',
        backgroundColor: 'rgba(50, 50, 50, 0.8)', // Dark background
        borderColor: 'rgba(50, 50, 50, 0.8)', // Dark border
        borderWidth: 1,
        padding: 10,
        textStyle: {
          color: '#fff', // White text
          fontSize: 15,
          fontFamily: Fonts.BaiJamjuree_Medium,
        },
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 5,
        formatter: params => {
          const source = params.value[3];
          const actualTime = params.value[4]; // Use the stored actual time
          const date = params.value[5];

          return `Source: ${source}\nDate: ${date}\nTime: ${actualTime}`;
        },
      },
      dataZoom: [
        {
          type: 'inside', // Or 'inside'
          xAxisIndex: 0,
          filterMode: 'filter',
          start: 0, // Initial start percentage
          end: 100, // Initial end percentage
        },
      ],
      series: [
        {
          type: 'custom',
          selectedMode: null,
          encode: {x: 0, y: 1, tooltip: [0, 1, 2, 3, 4, 5]}, // Include all required values for tooltip
          renderItem: (params, api) => {
            const start = api.coord([api.value(0), api.value(1)]);
            const end = api.coord([api.value(2), api.value(1)]);
            const source = api.value(3);
            const width = Math.max(1, end[0] - start[0]);

            return {
              type: 'rect',
              shape: {
                x: start[0],
                y: start[1] - 10,
                width: width,
                height: 35,
                r: [5, 5, 5, 5],
              },
              style: {
                fill: colorMap[source] || background,
                stroke: 'none',
                lineWidth: 2,
              },
            };
          },
          data: chartData.map(item => [
            item.startTime,
            item.yIndex,
            item.endTime,
            item.source,
            item.actualTime,
            moment(startDate).add(item.yIndex, 'days').format('YYYY-MM-DD'),
          ]),
        },
      ],
    };
  }, [chartData, chartDimensions, startDate, endDate, selectedItem]);

  const handleChartPress = () => {
    // setPsuModal(true);
  };

  return (
    <View style={styles.container}>
      {isLoading || chartData.length === 0 ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#FF7F02" />
        </View>
      ) : (
        <ChartComponent option={option} onPress={handleChartPress} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PowerSourceUtilizationChart;
