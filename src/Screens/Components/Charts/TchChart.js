import {
  ActivityIndicator,
  StyleSheet,
  Text,
  View,
  Dimensions,
} from 'react-native';
import React, {useRef, useEffect, useState} from 'react';
import {Svg<PERSON>hart, SVGRenderer} from '@wuba/react-native-echarts';
import * as echarts from 'echarts/core';
import {<PERSON>au<PERSON><PERSON><PERSON>} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
} from 'echarts/components';
import {ApiCaller} from '../../../middleWare/ApiCaller';
import {useAuth} from '../../../Context/AuthContext';
import {
  RegionWiseLastYear,
  RegionWiseToday,
  Request_Types,
} from '../../../api/uri';
import {useSelector} from 'react-redux';
import {Fonts} from '../../../styles/fonts';

echarts.use([
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  LegendComponent,
  <PERSON><PERSON><PERSON><PERSON>ponent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
]);
const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

const E_HEIGHT = screenHeight * 0.13;
const E_WIDTH = screenWidth * 0.4;

function ChartComponent({option}) {
  const chartRef = useRef(null);

  useEffect(() => {
    let chart;
    if (chartRef.current) {
      chart = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        width: E_WIDTH,
        height: E_HEIGHT,
      });
      chart.setOption(option);
    }
    return () => chart?.dispose();
  }, [option]);

  return <SvgChart ref={chartRef} style={{width: E_WIDTH, height: E_HEIGHT}} />;
}

const TchChart = ({Tch, setLoader, siteValue}) => {
  //   const {token} = useAuth();
  //   const [chartData, setChartData] = useState([]);
  //   const [chartData_2023, setChartData_2023] = useState([]);
  //   const istheme = useSelector(state => state.theme.theme);
  //   const [isDark, setIsDark] = useState(false);

  //   const DATE = new Date().toLocaleString();

  //   const yesterday = new Date();
  //   yesterday.setDate(yesterday.getDate() - 1);
  //   const formattedDate = yesterday.toISOString().split('T')[0];

  //   const weekLater = new Date();
  //   weekLater.setDate(weekLater.getDate() - 7);
  //   const formattedDate2 = weekLater.toISOString().split('T')[0];

  //   var date = new Date();
  //   date.setFullYear(date.getFullYear() - 1);
  //   const formattedDate3 = date.toISOString().split('T')[0];

  //   var date = new Date(formattedDate3);
  //   date.setDate(date.getDate() - 7);
  //   const formattedDate4 = date.toISOString().split('T')[0];

  //   useEffect(() => {
  //     if (istheme === 'light') {
  //       setIsDark(false);
  //     } else {
  //       setIsDark(true);
  //     }
  //   }, [istheme]);

  //   const text = isDark ? '#28283D' : '#FFFFFF';

  //   useEffect(() => {
  //     const fetchData = async () => {
  //       setLoader(true); // Start loader before API call
  //       try {
  //         const header = {
  //           Authorization: token,
  //         };

  //         const queryParams = 'NONPRIME';

  //         const response = await ApiCaller({
  //           method: Request_Types.GET,
  //           url: RegionWiseToday(siteValue, formattedDate, formattedDate2),
  //           headers: header,
  //         });

  //         const Chartdata = response.data.data;
  //         setChartData(Chartdata);

  //         const response_2023 = await ApiCaller({
  //           method: Request_Types.GET,
  //           url: RegionWiseLastYear(siteValue, formattedDate3, formattedDate4),
  //           headers: header,
  //         });

  //         const Chartdata_2023 = response_2023.data.data; // Corrected to use data from 2023 response
  //         setChartData_2023(Chartdata_2023);
  //       } catch (error) {
  //         console.error('API Call Error:', error.response?.data || error.message);
  //       } finally {
  //         setLoader(false);
  //       }
  //     };

  //     fetchData();
  //   }, [siteValue]);

  //   if (chartData.length === 0 || chartData_2023.length === 0) {
  //     return <Text style={{color: text}}>Loading...</Text>;
  //   }

  const option = {
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        pointer: {
          show: false,
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: false,
          clip: false,
          itemStyle: {
            borderWidth: 1,
            borderColor: '#00E5D2',
            color: '#00E5D2',
            backgroundColor: '#FFFFFF1A',
          },
        },
        axisLine: {
          lineStyle: {
            width: 10,
            color: [[1, '#FFFFFF1A']],
          },
        },
        splitLine: {
          show: false,
          distance: 0,
          length: 10,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          distance: 40,
        },
        data: [Tch],
        title: {
          fontSize: 20,
        },
        detail: {
          width: 50,
          height: 14,
          fontSize: 16,
          color: '#96999E',
          fontFamily: Fonts.BaiJamjuree_Bold,
          offsetCenter: [0, '0%'],

          formatter: 'TCH',
        },
      },
    ],
  };
  return <ChartComponent option={option} />;
};

export default TchChart;

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
  },
});
