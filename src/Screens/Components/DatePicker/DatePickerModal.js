import {Modal, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import DatePicker from './DatePicker';
import {useThemeStyles} from '../../../Constants/useThemeStyles';
import {Fonts} from '../../../styles/fonts';

const DatePickerModal = ({setCloseModal, setStartDate, setEndDate}) => {
  const themeStyles = useThemeStyles();
  const [initialDate, setInitialDate] = useState(null);
  const [lastDate, setLastDate] = useState(null);

  const handleDateChange = ({startDate, endDate}) => {
    if (startDate) {
      const formattedStartDate = new Date(startDate)
        .toISOString()
        .split('T')[0];
      setInitialDate(formattedStartDate);
    }

    if (endDate) {
      const formattedEndDate = new Date(endDate).toISOString().split('T')[0];
      setLastDate(formattedEndDate);
    }
  };
  const handleDateChangeForConfigLog = () => {
    setStartDate(initialDate);
    setEndDate(lastDate);
    setCloseModal(false);
  };
  return (
    <Modal animationType="fade" transparent={true} visible={true}>
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.modalContainer,
            {backgroundColor: themeStyles.background},
          ]}>
          <View style={styles.header}>
            <Text
              style={{
                color: '#96999E',
                fontSize: 16,
                fontFamily: Fonts.BaiJamjuree_SemiBold,
              }}>
              Select Day/Duration
            </Text>
          </View>
          <DatePicker onDateChange={handleDateChange} />
          <View style={styles.footer}>
            <TouchableOpacity
              onPress={() => setCloseModal(false)}
              style={[
                styles.backButton,
                {borderWidth: 1, borderColor: '#404346'},
              ]}>
              <Text style={styles.clearButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.backButton, {backgroundColor: '#FF7F02'}]}
              onPress={() => handleDateChangeForConfigLog()}>
              <Text style={styles.changeParamsText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default DatePickerModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '96%',
    height: '60%',

    borderRadius: 25,
    padding: 16,
  },
  header: {
    height: '10%',
    width: '100%',

    alignItems: 'center',
    justifyContent: 'center',
  },
  footer: {
    width: '100%',
    height: '13%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    height: '90%',
    width: '48%',

    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#96999E',
  },
  changeParamsText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#0E121A',
  },
});
