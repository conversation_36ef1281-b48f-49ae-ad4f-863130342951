import {StyleSheet, View, ActivityIndicator, Text} from 'react-native';
import React, {useState, useEffect, useCallback} from 'react';
import DateTimePicker from 'react-native-ui-datepicker';
import {useThemeStyles} from '../../../Constants/useThemeStyles';
import dayjs from 'dayjs';
import {Colors} from '../../../styles/colors';
import {DayFilter, DayFilter_7} from '../../../Constants/DateFilter';

const DatePicker = ({
  onDateChange,
  initialStartDate,
  initialEndDate,
  minDate,
  maxDate,
  isLoading = false,
}) => {
  const themeStyles = useThemeStyles();
  const [error, setError] = useState(null);

  // Set default dates using DayFilter and DayFilter_7
  const getDefaultDates = () => {
    return {
      startDate: dayjs(DayFilter_7()),
      endDate: dayjs(DayFilter()),
    };
  };

  const [dateRange, setDateRange] = useState(() => {
    if (initialStartDate && initialEndDate) {
      return {
        startDate: dayjs(initialStartDate),
        endDate: dayjs(initialEndDate),
      };
    }
    return getDefaultDates();
  });

  useEffect(() => {
    if (initialStartDate && initialEndDate) {
      const newStartDate = dayjs(initialStartDate);
      const newEndDate = dayjs(initialEndDate);

      setDateRange({
        startDate: newStartDate,
        endDate: newEndDate,
      });
    }
  }, [initialStartDate, initialEndDate]);

  const validateDateRange = useCallback(
    (start, end) => {
      if (!start || !end) {
        const defaultDates = getDefaultDates();
        return {
          isValid: true,
          dates: defaultDates,
        };
      }

      // Ensure end date is not before start date
      if (dayjs(end).isBefore(dayjs(start))) {
        setError('End date cannot be before start date');
        return {isValid: false};
      }

      // Check against min/max dates if provided
      if (minDate && dayjs(start).isBefore(dayjs(minDate))) {
        setError(
          `Date cannot be before ${dayjs(minDate).format('YYYY-MM-DD')}`,
        );
        return {isValid: false};
      }

      if (maxDate && dayjs(end).isAfter(dayjs(maxDate))) {
        setError(`Date cannot be after ${dayjs(maxDate).format('YYYY-MM-DD')}`);
        return {isValid: false};
      }

      setError(null);
      return {
        isValid: true,
        dates: {startDate: start, endDate: end},
      };
    },
    [minDate, maxDate],
  );

  const handleDateChange = useCallback(
    ({startDate, endDate}) => {
      const adjustedStartDate = startDate ? dayjs(startDate) : undefined;
      const adjustedEndDate = endDate ? dayjs(endDate) : undefined;

      const validation = validateDateRange(adjustedStartDate, adjustedEndDate);

      if (!validation.isValid) {
        return;
      }

      const finalDates = validation.dates;
      setDateRange(finalDates);

      if (onDateChange) {
        onDateChange({
          startDate: finalDates.startDate.toDate(),
          endDate: finalDates.endDate.toDate(),
        });
      }
    },
    [onDateChange, validateDateRange],
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.buttonColor} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <DateTimePicker
        mode="range"
        startDate={dateRange.startDate.toDate()}
        endDate={dateRange.endDate.toDate()}
        onChange={handleDateChange}
        minDate={minDate}
        maxDate={maxDate}
        accessible={true}
        accessibilityLabel="Date Range Picker"
        accessibilityHint="Select start and end dates"
        styles={{
          button_next: {
            backgroundColor: 'transparent',
            padding: 8,
          },
          button_prev: {
            backgroundColor: 'transparent',
            padding: 8,
          },
          button_next_image: {
            tintColor: themeStyles.textColor,
          },
          button_prev_image: {
            tintColor: themeStyles.textColor,
          },
          selected: {
            backgroundColor: Colors.buttonColor,
            borderRadius: 10,
          },
          selected_label: {
            color: '#FFFFFF',
            fontWeight: '600',
          },
          day_label: {
            color: themeStyles.textColor,
          },
          month_label: {
            color: themeStyles.textColor,
          },
          year_label: {
            color: themeStyles.textColor,
          },
          weekday_label: {
            color: themeStyles.textColor,
            fontWeight: '500',
          },
          month_selector_label: {
            color: themeStyles.textColor,
            fontSize: 16,
            fontWeight: '600',
          },
          range_fill: {
            backgroundColor: `${Colors.buttonColor}20`,
          },
          header: {
            backgroundColor: themeStyles.background,
            paddingVertical: 12,
          },
          day_disabled: {
            opacity: 0.4,
          },
          day_today: {
            borderColor: Colors.buttonColor,
            borderWidth: 1,
          },
        }}
      />
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

export default DatePicker;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingHorizontal: 16,
    marginTop: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
});
