import {StyleSheet, View, ActivityIndicator, BackHandler} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useThemeStyles} from '../../Constants/useThemeStyles';
import IntegrationScreenHeader from './Components/IntegrationComponents/IntegrationScreenHeader/IntegrationScreenHeader';
import {GetsiteID} from '../../redux/Slices/SiteIdSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useDispatch, useSelector} from 'react-redux';
import {GET_SITE_DEVICE_INFORMATION, Request_Types} from '../../api/uri';
import {ApiCaller} from '../../middleWare/ApiCaller';
import IntegrationAccordion from './Components/IntegrationComponents/IntegrationAccordion/IntegrationAccordion';
import {useFocusEffect} from '@react-navigation/native';
import {useNavigation} from '@react-navigation/native';

const SiteIntegration = ({route}) => {
  const themeStyles = useThemeStyles();
  const dispatch = useDispatch();
  const [siteId, setSiteId] = useState('');
  const [siteLoad, setSiteLoad] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [siteIdChanging, setSiteIdChanging] = useState(false);
  const navigation = useNavigation();
  const fetchSiteDevices = useCallback(async id => {
    if (!id) return;

    try {
      setLoading(true);
      setSiteIdChanging(true);

      const token = await AsyncStorage.getItem('Authorization');
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_SITE_DEVICE_INFORMATION(id)}`,
        headers: {Authorization: token},
      });

      setSiteLoad(response.data.data);
    } catch (error) {
      console.error('Site Device info Fetch Error:', error);
    } finally {
      setLoading(false);
      setSiteIdChanging(false);
    }
  }, []);

  const handleSiteIdChange = useCallback(
    async newSiteId => {
      setSiteId(newSiteId);
      dispatch(GetsiteID(newSiteId));
      try {
        await AsyncStorage.setItem('SelectedSiteId', newSiteId);
        fetchSiteDevices(newSiteId);
      } catch (error) {
        console.error('Error saving site ID:', error);
      }
    },
    [dispatch, fetchSiteDevices],
  );
  useEffect(() => {
    const handleBackButtonPress = () => {
      // Navigate to LandingScreen and open the Site Profile (Home) screen
      navigation.navigate('LandingScreen', {screen: 'Site Profile'});
      return true; // Prevents default back button behavior
    };

    // Add event listener for hardware back button
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    // Add listener for navigation events
    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        // If we're navigating away from this screen
        if (event.data.action.type === 'GO_BACK') {
          // Prevent default navigation behavior
          event.preventDefault();

          // Navigate to LandingScreen and open the Site Profile (Home) screen
          navigation.navigate('LandingScreen', {screen: 'Site Profile'});
        }
      },
    );

    // Clean up event listeners when component unmounts
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation]);
  // Refresh when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      if (isSiteID) {
        fetchSiteDevices(isSiteID);
      }
    }, [isSiteID, fetchSiteDevices]),
  );

  // Initial load and handle site ID changes
  useEffect(() => {
    if (isSiteID) {
      setSiteId(isSiteID);
      fetchSiteDevices(isSiteID);
    }
  }, [isSiteID, fetchSiteDevices]);

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <IntegrationScreenHeader
        siteId={siteId}
        onSiteIdChange={handleSiteIdChange}
        showSearch={true}
        toggleSearch={true}
        parameterCount={siteIdChanging ? 0 : siteLoad?.length}
      />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF7F02" />
        </View>
      ) : (
        <IntegrationAccordion
          siteLoad={siteLoad}
          alarmsType={''}
          timestamp={''}
          onRefresh={() => fetchSiteDevices(siteId)}
        />
      )}
    </View>
  );
};

export default SiteIntegration;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
