import React, {useState, useEffect, useCallback} from 'react';
import {
  Dimensions,
  Image,
  ImageBackground,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
  StatusBar,
} from 'react-native';
import {useSelector} from 'react-redux';
import SiteInformationComponent from './Components/HomeComponents/SiteInformationComponent';
import PowerComponent from './Components/HomeComponents/PowerComponent';
import CommunicationComponent from './Components/HomeComponents/CommunicationComponent';
import SiteAvailability from './Components/HomeComponents/SiteAvailability';
import PowerSourceDuty from './Components/HomeComponents/PowerSourceDuty';
import SiteInformationBottomSheet from './Components/HomeComponents/SiteInformationBottomSheet';
import NightSouce from '../../assets/Images/NewDarkBackground.png';
import DaySouce from '../../assets/Images/NewLightBackground.png';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ModalComponent from './Components/HomeComponents/ModalComponent';
import PsdcLandScapeModal from './Components/HomeComponents/PsdcLandScapeModal';
import FastImage from 'react-native-fast-image';
import {Fonts} from '../../styles/fonts';
import PowerSourceModal from './Components/HomeComponents/PowerSourceModal';
import AssetsComponent from './Components/HomeComponents/AssetsComponent';
import SiteInfraBottomSheet from './Components/HomeComponents/SiteInfraBottomSheet';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import NetInfo from '@react-native-community/netinfo';
import NoInternetSvg from '../../assets/svgs/NoInternetSvg';
import LinearGradient from 'react-native-linear-gradient';
import {Colors} from '../../styles/colors';
import WeatherInformationCard from './Components/HomeComponents/WeatherInformationCard/WeatherInformationCard';
import {useThemeStyles} from '../../Constants/useThemeStyles';
const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

const Home = ({scrollY}) => {
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(false);
  const [isVisible, setVisible] = useState(false);
  const [modal, setModal] = useState(false);
  const [psdcModal, setPsdcModal] = useState(false);
  const [PsuModal, setPsuModal] = useState(false);
  const [InfraVisible, setInfraVisible] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState(null);
  const [storedTheme, setStoredTheme] = useState(null);
  const [layoutKey, setLayoutKey] = useState(0); // new state to force re-render
  const [isweatherVisible, setWeatherVisible] = useState(false);
  const [checkInternetConnectivity, setCheckInternetConnectivity] =
    useState(false);
  const [isInternetReachable, setIsInternetReachable] = useState(true);
  const isSiteID = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setCheckInternetConnectivity(state.isInternetReachable);
      setIsInternetReachable(state.isInternetReachable);
    });

    return () => unsubscribe();
  }, []);

  const checkInternetConnection = useCallback(() => {
    NetInfo.fetch().then(state => {
      setIsInternetReachable(state.isInternetReachable);
    });
  }, []);

  const background = isDark ? '#F1F1F1' : '#0C121D';

  const backgroundImage = isDark ? DaySouce : NightSouce;

  const handleSetVisible = useCallback(visible => {
    setVisible(visible);
  }, []);

  const handleModalVisible = useCallback(ismodal => {
    setModal(ismodal);
  }, []);
  const handlePsdcModal = useCallback(ismodal => {
    setPsdcModal(ismodal);

    if (!ismodal) {
      // Delay re-render until orientation has reset
      setTimeout(() => {
        setLayoutKey(prevKey => prevKey + 1);
      }, 500); // adjust delay if needed
    }
  }, []);

  const handlePsuModal = useCallback(value => {
    if (value === true) {
      StatusBar.setHidden(true);
      setPsuModal(true);
    } else {
      StatusBar.setHidden(false);
      setPsuModal(false);
      setTimeout(() => {
        setLayoutKey(prevKey => prevKey + 1);
      }, 500);
    }
  }, []);
  const handleInfaSheet = useCallback(ismodal => {
    setInfraVisible(ismodal);
  }, []);

  const handleWeatherSheet = useCallback(ismodal => {
    setWeatherVisible(ismodal);
  }, []);
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', () => {
      setLayoutKey(prev => prev + 1); // force re-render on any orientation change
    });

    return () => {
      subscription?.remove(); // clean up
    };
  }, []);
  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <View style={[styles.container, {backgroundColor: themeStyles.ssvBack}]}>
        <Animated.ScrollView
          onScroll={Animated.event(
            [{nativeEvent: {contentOffset: {y: scrollY}}}],
            {useNativeDriver: false},
          )}
          scrollEventThrottle={16}
          style={{flex: 3}}
          showsVerticalScrollIndicator={false}
          alwaysBounceVertical={true}
          scrollEnabled={isInternetReachable}>
          <View style={{alignItems: 'center', flex: 1}}>
            <ImageBackground
              source={themeStyles.svgsTheme ? DaySouce : NightSouce}
              style={styles.imageBackground}>
              <SiteInformationComponent
                setVisible={handleSetVisible}
                setWeatherVisible={handleWeatherSheet}
              />
              <PowerComponent setPsuModal={handlePsuModal} />
              <CommunicationComponent setModal={handleModalVisible} />
              <AssetsComponent
                layoutTrigger={layoutKey}
                setInfraVisible={handleInfaSheet}
                setSelectedAsset={setSelectedAsset}
              />
            </ImageBackground>
            {!isVisible && (
              <>
                {psdcModal ? (
                  <PsdcLandScapeModal setPsdcModal={handlePsdcModal} />
                ) : (
                  <>
                    <SiteAvailability />
                    <PowerSourceDuty setPsdcModal={handlePsdcModal} />
                  </>
                )}
              </>
            )}
          </View>
        </Animated.ScrollView>
        {InfraVisible && (
          <SiteInfraBottomSheet
            setInfraVisible={handleInfaSheet}
            selectedAsset={selectedAsset}
          />
        )}
        {isVisible && (
          <SiteInformationBottomSheet
            setVisible={handleSetVisible}
            isVisible={isVisible}
          />
        )}
        {isweatherVisible && (
          <WeatherInformationCard setWeatherVisible={handleWeatherSheet} />
        )}
        {modal && <ModalComponent setModal={handleModalVisible} />}
        {PsuModal && <PowerSourceModal setPsuModal={handlePsuModal} />}
        {!isInternetReachable && (
          <View style={styles.noInternetOverlay}>
            <View style={styles.noInternetContainer}>
              <View style={styles.contentContainer}>
                <NoInternetSvg />
                <Text style={styles.noInternetTitle}>Connection error!</Text>
              </View>
              <Text style={styles.noInternetMessage}>
                Please check your internet connection and try again.
              </Text>
              <LinearGradient
                colors={Colors.buttonGradient}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 1}}
                style={{
                  height: '23%',
                  width: '90%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 6,
                  marginBottom: '1%',
                  marginTop: '5%',
                }}>
                <TouchableOpacity
                  style={styles.button}
                  onPress={checkInternetConnection}>
                  <Text style={[styles.buttonText, {color: '#0C121D'}]}>
                    Try Again
                  </Text>
                </TouchableOpacity>
              </LinearGradient>
            </View>
          </View>
        )}
      </View>
    </GestureHandlerRootView>
  );
};

export default Home;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F1F1F1',
  },
  imageBackground: {
    height: screenHeight * 1,
    width: '100%',
    alignItems: 'center',
    resizeMode: 'cover',
  },
  noInternetOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 9,
    right: 0,

    height: '100%',
    width: '96%',
    justifyContent: 'flex-end',
    zIndex: 999,
    alignSelf: 'center',
  },
  noInternetContainer: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    alignItems: 'center',
    width: '98%',
    alignSelf: 'center',
    height: '24%',
    flexDirection: 'column',
    backgroundColor: '#252A34E9',
    justifyContent: 'center',
  },
  noInternetTitle: {
    fontFamily: Fonts.BaiJamjuree_Bold,
    fontSize: 25,
    color: 'white',

    marginLeft: '5%',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
    marginTop: '-2%',
  },
  noInternetMessage: {
    fontFamily: Fonts.BaiJamjuree_Regular,
    fontSize: 14,
    color: '#96999E',
    width: '90%',
  },
  contentContainer: {
    width: '90%',
    height: '20%',

    flexDirection: 'row',
  },
  button: {
    borderRadius: 8,
    width: '90%',
    alignItems: 'center',
    height: 80,
    justifyContent: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
  },
});
