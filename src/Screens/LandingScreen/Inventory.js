import {StyleSheet, Text, View} from 'react-native';
import React, {useCallback, useState} from 'react';
import {useThemeStyles} from '../../Constants/useThemeStyles';
import {GetsiteID} from '../../redux/Slices/SiteIdSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import InventoryManagementScreenHeader from './Components/InventoryManagementComponents/InventoryManagementScreenHeader/InventoryManagementScreenHeader';
import InventoryManagementAccordion from './Components/InventoryManagementComponents/InventoryManagementAccordion/InventoryManagementAccordion';
import {Fonts} from '../../styles/fonts';

const Inventory = () => {
  const themeStyles = useThemeStyles();
  const [siteId, setSiteId] = useState('');

  // const handleSiteIdChange = useCallback(
  //   async newSiteId => {
  //     setSiteId(newSiteId);
  //     dispatch(GetsiteID(newSiteId));
  //     try {
  //       await AsyncStorage.setItem('SelectedSiteId', newSiteId);
  //       fetchSiteDevices(newSiteId);
  //     } catch (error) {
  //       console.error('Error saving site ID:', error);
  //     }
  //   },
  //   [dispatch, fetchSiteDevices],
  // );
  return (
    // <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
    //   <InventoryManagementScreenHeader
    //     siteId={siteId}
    //     showSearch={true}
    //     toggleSearch={true}
    //     // parameterCount={siteIdChanging ? 0 : siteLoad?.length}
    //   />
    //   <InventoryManagementAccordion />
    // </View>
    <View
      style={{
        backgroundColor: '#0C121D',
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <Text
        style={{
          color: 'white',
          fontSize: 20,
          fontFamily: Fonts.BaiJamjuree_SemiBold,
        }}>
        Coming Soon.....
      </Text>
    </View>
  );
};

export default Inventory;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
});
