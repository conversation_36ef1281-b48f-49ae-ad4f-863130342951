import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  BackHandler,
} from 'react-native';
import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {useThemeStyles} from '../../Constants/useThemeStyles';
import ParametersScreenHeader from './Components/ParametersComponent.js/Common/ParametersScreenHeader';
import {useDispatch, useSelector} from 'react-redux';
import {GetsiteID} from '../../redux/Slices/SiteIdSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../middleWare/ApiCaller';
import {
  APPLY_CHANGES_CONFIG,
  GET_CONFIGURATIONS,
  GET_PARAMETERS,
  Request_Types,
} from '../../api/uri';
import {formatTimestamp} from '../../Constants/FormatTimestamp';
import {determineValueType} from '../../Constants/DetermineValueType';
import NoDataFoundMessageComponent from './Components/ParametersComponent.js/Common/NoDataFoundMessageComponent';
import ConfigurationAccordion from './Components/ConfigurationComponents/ConfigurationAccordion/ConfigurationAccordion';
import {Fonts} from '../../styles/fonts';
import UserDefinedConfigurations from './Components/ConfigurationComponents/UserDefinedConfigurations/UserDefinedConfigurations';
import Toast from 'react-native-toast-message';
import {useNavigation} from '@react-navigation/native';

const Configuration = () => {
  const themeStyles = useThemeStyles();
  const navigation = useNavigation();

  const [siteId, setSiteId] = useState('');
  const [siteLoad, setSiteLoad] = useState([]);
  const [selectedType, setSelectedType] = useState('Config');

  const isSiteID = useSelector(state => state.siteId.siteId);

  const [selectedItem, setSelectedItem] = useState();
  const dispatch = useDispatch();
  const [isTypeChanging, setIsTypeChanging] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [timestamp, setTimestamp] = useState(null);
  const [updatedValuesJson, setUpdatedValuesJson] = useState({});
  const [userDefinedConfigs, setUserDefinedConfigs] = useState(false);
  const [hasModifiedValues, setHasModifiedValues] = useState(false);
  const [resetTrigger, setResetTrigger] = useState(false);

  const handleSiteIdChange = useCallback(
    async newSiteId => {
      setSiteId(newSiteId);
      dispatch(GetsiteID(newSiteId));
      try {
        await AsyncStorage.setItem('SelectedSiteId', newSiteId);
      } catch (error) {
        console.error('Error saving site ID:', error);
      }
    },
    [dispatch],
  );
  const toggleSearch = useCallback(() => {
    if (!userDefinedConfigs) {
      // Only toggle if not in userDefinedConfigs
      setShowSearch(prev => !prev);
    }
  }, [userDefinedConfigs]);
  useEffect(() => {
    const handleBackButtonPress = () => {
      // Navigate to LandingScreen and open the Site Profile (Home) screen
      navigation.navigate('LandingScreen', {screen: 'Site Profile'});
      return true; // Prevents default back button behavior
    };

    // Add event listener for hardware back button
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    // Add listener for navigation events
    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        // If we're navigating away from this screen
        if (event.data.action.type === 'GO_BACK') {
          // Prevent default navigation behavior
          event.preventDefault();

          // Navigate to LandingScreen and open the Site Profile (Home) screen
          navigation.navigate('LandingScreen', {screen: 'Site Profile'});
        }
      },
    );

    // Clean up event listeners when component unmounts
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation]);
  const fetchSiteConfigs = useCallback(
    async id => {
      if (!id || !selectedItem) return;

      try {
        setLoading(true);
        setIsTypeChanging(true);
        const token = await AsyncStorage.getItem('Authorization');

        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_CONFIGURATIONS(id, selectedItem.title)}`,
          headers: {Authorization: token},
        });

        const dataObject = response.data.data[0].result?.[id];
        const timestamp = response.data.data[0].result?.[id].ts;

        // Convert values based on their types
        const formattedData = Object.entries(dataObject || {})
          .filter(([key]) => key !== 'ts')
          .map(([key, value]) => {
            const {value: convertedValue, type} = determineValueType(value);
            return {
              key,
              value: convertedValue,
              dataType: type,
            };
          });

        setSiteLoad(formattedData);
        const formattedTimestamp = formatTimestamp(timestamp);
        setTimestamp(formattedTimestamp);
      } catch (error) {
        console.error('Site Parameters Fetch Error:', error);
      } finally {
        setLoading(false);
        setIsTypeChanging(false);
      }
    },
    [selectedItem], // Add selectedItem as dependency
  );

  useEffect(() => {
    if (isSiteID && isSiteID !== siteId) {
      setSiteId(isSiteID);
      fetchSiteConfigs(isSiteID);
    }
  }, [isSiteID, fetchSiteConfigs]);

  useEffect(() => {
    if (siteId) {
      fetchSiteConfigs(siteId);
    }
  }, [selectedItem, siteId, fetchSiteConfigs]);
  const handleCloseUserDefinedConfigs = useCallback(() => {
    setUserDefinedConfigs(false);
    setShowSearch(false); // Reset search bar visibility
    setSearchQuery(''); // Clear search query
  }, []);

  const handleOpenUserDefinedParams = () => {
    setUserDefinedConfigs(true);
  };

  // Add filtered data logic using useMemo
  const filteredData = useMemo(() => {
    if (!searchQuery.trim() || !siteLoad) return siteLoad;

    const query = searchQuery.toLowerCase();
    return siteLoad.filter(item => {
      const key = String(item.key || '').toLowerCase();
      const value = String(item.value || '').toLowerCase();
      const dataType = String(item.dataType || '').toLowerCase();

      return (
        key.includes(query) || value.includes(query) || dataType.includes(query)
      );
    });
  }, [siteLoad, searchQuery]);

  const handleValueModification = isModified => {
    setHasModifiedValues(isModified);
  };

  const handleResetAll = () => {
    setResetTrigger(true); // Trigger reset in child component
    setHasModifiedValues(false);
  };
  const handleEditConfiguration = async () => {
    // Implement your logic here

    try {
      setLoading(true);
      setIsTypeChanging(true);
      const token = await AsyncStorage.getItem('Authorization');

      const response = await ApiCaller({
        method: Request_Types.POST,
        url: `${APPLY_CHANGES_CONFIG}`,
        headers: {Authorization: token},
        data: updatedValuesJson,
      });

      const dataObject = response.data.success;
      if (dataObject) {
        Toast.show({
          type: 'info',
          text1: 'Info',
          text2: 'Updated Successfully',
        });
      }

      // Convert values based on their types
    } catch (error) {
      console.error('Site Parameters Fetch Error:', error);
    } finally {
      setLoading(false);
      setIsTypeChanging(false);
    }
  };

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <ParametersScreenHeader
        siteId={siteId}
        onSiteIdChange={handleSiteIdChange}
        showSearch={showSearch || userDefinedConfigs}
        toggleSearch={toggleSearch}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedItem={selectedItem}
        setSelectedItem={setSelectedItem}
        parameterCount={isTypeChanging ? 0 : filteredData?.length}
        selectedType={selectedType}
        showCrossIcon={!userDefinedConfigs && showSearch}
        showCount={!userDefinedConfigs}
        showParamsDropdown={!userDefinedConfigs}
      />

      {userDefinedConfigs ? (
        <UserDefinedConfigurations
          setUserDefinedConfigs={handleCloseUserDefinedConfigs}
          searchQuery={searchQuery}
        />
      ) : (
        <>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                animating={isLoading}
                size="large"
                color="#FF7F02"
              />
            </View>
          ) : !filteredData || filteredData.length === 0 ? ( // Update condition to use filteredData
            <NoDataFoundMessageComponent searchQuery={searchQuery} />
          ) : (
            <ConfigurationAccordion
              siteLoad={filteredData} // Pass filtered data instead of siteLoad
              selectedItem={selectedItem}
              timestamp={timestamp}
              onValueModification={handleValueModification}
              resetTrigger={resetTrigger}
              setResetTrigger={setResetTrigger}
              setUpdatedValuesJson={setUpdatedValuesJson}
            />
          )}
          {hasModifiedValues ? (
            <View style={styles.footer}>
              <TouchableOpacity
                style={[
                  styles.backButton,
                  {borderWidth: 1, borderColor: '#FF2322'},
                ]}
                onPress={handleResetAll}>
                <Text style={styles.clearButtonText}>Reset All</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.backButton, {backgroundColor: '#FF7F02'}]}
                onPress={handleEditConfiguration}>
                <Text style={styles.changeParamsText}>Update All</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.UserDefinedConfigButton}
              activeOpacity={0.7}
              onPress={handleOpenUserDefinedParams}>
              <Text style={styles.userDefinedButtonText}>
                User Defined Configurations
              </Text>
            </TouchableOpacity>
          )}
        </>
      )}
    </View>
  );
};
export default Configuration;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  UserDefinedConfigButton: {
    height: '6%',

    width: '90%',
    backgroundColor: '#0E121A',
    borderRadius: 10,
    marginBottom: '2%',
    borderColor: '#FF7F02',
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDefinedButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FF7F02',
  },
  footer: {
    width: '90%',
    height: '6.5%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '1%',
  },
  backButton: {
    height: '90%',
    width: '48%',

    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FF2322',
  },
  changeParamsText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#0E121A',
  },
});
