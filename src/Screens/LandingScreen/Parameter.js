import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  TextInput,
  BackHandler,
} from 'react-native';
import React, {memo, useCallback, useEffect, useState, useMemo} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {ApiCaller} from '../../middleWare/ApiCaller';
import {GET_PARAMETERS, GET_SITE_ALARMS, Request_Types} from '../../api/uri';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DropDown from './Components/SearchScreen/DropDown';
import {useThemeStyles} from '../../Constants/useThemeStyles';
import {Fonts} from '../../styles/fonts';
import {GetsiteID} from '../../redux/Slices/SiteIdSlice';
import ParamsDropDown from './Components/ParametersComponent.js/ParamsDropDown/ParamsDropDown';
import SearchIconDark from '../../assets/svgs/SearchIconDark.js';
import ParameterAccordion from './Components/ParametersComponent.js/ParameterAccordion/ParameterAccordion';
import EmptyFolder from '../../assets/svgs/EmptyFolder.js';
import CrossSvg from '../../assets/svgs/CrossSvg.js';
import {useNavigation} from '@react-navigation/native';
import UserDefinedParams from './Components/ParametersComponent.js/UserDefinedParams/UserDefinedParams';
import ParametersFilter from './Components/ParametersComponent.js/Common/ParametersFilter';
import ParametersScreenHeader from './Components/ParametersComponent.js/Common/ParametersScreenHeader.js';
import NoDataFoundMessageComponent from './Components/ParametersComponent.js/Common/NoDataFoundMessageComponent.js';
import {determineValueType} from '../../Constants/DetermineValueType';
import {formatTimestamp} from '../../Constants/FormatTimestamp';

const Parameter = () => {
  const themeStyles = useThemeStyles();
  const [siteId, setSiteId] = useState('');
  const [siteLoad, setSiteLoad] = useState([]);
  const [selectedType, setSelectedType] = useState('Config');
  const [timestamp, setTimestamp] = useState(null);
  const dispatch = useDispatch();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [selectedItem, setSelectedItem] = useState({
    id: '1',
    title: 'General',
    name: 'General',
  });
  const [isLoading, setLoading] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const navigation = useNavigation();
  const [userDefinedParams, setUserDefinedParams] = useState(false);
  const getFilteredParameters = () => {
    if (!searchQuery.trim() || !siteLoad) return siteLoad;

    const query = searchQuery.toLowerCase();
    return siteLoad.filter(
      param =>
        param.key.toLowerCase().includes(query) ||
        (param.value !== null &&
          String(param.value).toLowerCase().includes(query)),
    );
  };
  useEffect(() => {
    const handleBackButtonPress = () => {
      // Navigate to LandingScreen and open the Site Profile (Home) screen
      navigation.navigate('LandingScreen', {screen: 'Site Profile'});
      return true; // Prevents default back button behavior
    };

    // Add event listener for hardware back button
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    // Add listener for navigation events
    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        // If we're navigating away from this screen
        if (event.data.action.type === 'GO_BACK') {
          // Prevent default navigation behavior
          event.preventDefault();

          // Navigate to LandingScreen and open the Site Profile (Home) screen
          navigation.navigate('LandingScreen', {screen: 'Site Profile'});
        }
      },
    );

    // Clean up event listeners when component unmounts
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation]);
  // Fetch data function
  const fetchData = async (id, type, category) => {
    if (!id) return;

    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('Authorization');

      // Format the type properly
      const formattedType = type === 'Real+Time' ? 'Real Time' : type;

      const url = GET_PARAMETERS(id, category, formattedType);

      const response = await ApiCaller({
        method: Request_Types.GET,
        url: url,
        headers: {Authorization: token},
      });

      const dataObject = response.data.data[0].result?.[id];
      const timestamp = response.data.data[0].result?.[id].ts;

      if (!dataObject) {
        setSiteLoad([]);
        return;
      }

      const formattedData = Object.entries(dataObject || {})
        .filter(([key]) => key !== 'ts')
        .map(([key, value]) => {
          const {value: convertedValue, type} = determineValueType(value);
          return {
            key,
            value: convertedValue,
            dataType: type,
          };
        });

      setSiteLoad(formattedData);
      setTimestamp(formatTimestamp(timestamp));
    } catch (error) {
      console.error('API Error:', error);
      setSiteLoad([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle type change
  const handleTypeChange = newType => {
    setSelectedType(newType);
    setSearchQuery('');
    // Immediate fetch after type change
    fetchData(siteId, newType, selectedItem.title);
  };

  // Handle item change
  const handleItemChange = newItem => {
    setSelectedItem(newItem);
    setSearchQuery('');
    // Immediate fetch after item change
    fetchData(siteId, selectedType, newItem.title);
  };

  // Handle site ID change
  const handleSiteIdChange = async newSiteId => {
    setSiteId(newSiteId);
    dispatch(GetsiteID(newSiteId));
    try {
      await AsyncStorage.setItem('SelectedSiteId', newSiteId);
      // Fetch data with new site ID
      fetchData(newSiteId, selectedType, selectedItem.title);
    } catch (error) {
      console.error('Error saving site ID:', error);
    }
  };

  // Initial load and site ID sync
  useEffect(() => {
    if (isSiteID && isSiteID !== siteId) {
      handleSiteIdChange(isSiteID);
    }
  }, [isSiteID]);

  // Periodic refresh
  useEffect(() => {
    if (siteId) {
      const interval = setInterval(() => {
        fetchData(siteId, selectedType, selectedItem.title);
      }, 5 * 60 * 1000); // 5 minutes
      return () => clearInterval(interval);
    }
  }, [siteId, selectedType, selectedItem.title]);

  const toggleSearch = useCallback(() => {
    setShowSearch(prev => !prev);
  }, []);
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      setUserDefinedParams(false);
    });

    // Cleanup subscription on unmount
    return unsubscribe;
  }, [navigation]);
  const handleCloseUserDefinedParams = useCallback(() => {
    setUserDefinedParams(false);
  }, []);
  // Clear all intervals and timeouts when component unmounts or userDefinedParams changes
  useEffect(() => {
    return () => {
      // Cleanup function
      clearTimeout();
      clearInterval();
    };
  }, [userDefinedParams]);
  // Simple close handler
  const handleClose = () => {
    setUserDefinedParams(false);
  };

  const handleOpenUserDefinedParams = () => {
    setUserDefinedParams(true);
  };

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      {userDefinedParams ? (
        <UserDefinedParams
          onClose={handleClose} // Pass onClose instead of setUserDefinedParams
        />
      ) : (
        <>
          <ParametersScreenHeader
            siteId={siteId}
            onSiteIdChange={handleSiteIdChange}
            showSearch={showSearch}
            toggleSearch={toggleSearch}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            selectedItem={selectedItem}
            setSelectedItem={handleItemChange}
            parameterCount={siteLoad?.length}
            selectedType={selectedType}
            showCrossIcon={true}
            showCount={true}
          />

          <ParametersFilter
            setSelectedType={handleTypeChange}
            currentType={selectedType}
            disabled={isLoading}
          />
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FF7F02" />
            </View>
          ) : !siteLoad || siteLoad.length === 0 ? (
            <NoDataFoundMessageComponent
              searchQuery={searchQuery}
              selectedType={selectedType}
            />
          ) : (
            <ParameterAccordion
              siteLoad={getFilteredParameters()}
              alarmsType={selectedType}
              timestamp={timestamp}
            />
          )}

          <TouchableOpacity
            style={styles.UserDefinedParamsButton}
            activeOpacity={0.7}
            onPress={() => setUserDefinedParams(true)}>
            <Text style={styles.userDefinedButtonText}>
              User Defined Parameters
            </Text>
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

export default Parameter;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  siteIdContainer: {
    height: '61%',
    width: '50%',
    flexDirection: 'row',
    alignContent: 'center',
    overflow: 'visible',

    marginLeft: '0%',
    alignItems: 'center',
  },
  siteContainer: {
    width: '96%',
    height: '8%',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: '15%',
    flexDirection: 'row',
    marginBottom: '-6%', // Add small margin at bottom of site container
  },
  totalParametersContainer: {
    height: '43%',
    width: '50%',

    justifyContent: 'center',
    flexDirection: 'column',
  },
  filterContainer: {
    width: '96%',
    height: '8%',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '3%',
    flexDirection: 'row',
    alignSelf: 'center',
  },
  searchFieldContainer: {
    width: '96%',
    height: '5.5%',

    alignItems: 'center',
    justifyContent: 'space-around',
    marginTop: '6%',
    flexDirection: 'row',
    alignSelf: 'center',
    borderRadius: 6,
    marginBottom: '2.2%',
  },
  dropDown: {
    width: '85%',
    height: '70%',
    borderRadius: 10,
    marginLeft: 0, // Add this to ensure no left margin
  },
  searchContainer: {
    height: 46,
    width: '13%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF1A',
    borderRadius: 6,
    marginRight: 0, // Add this to ensure no right margin
  },
  selectionContainer: {
    width: '96%',
    height: '5.5%',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '1%',
    flexDirection: 'row',
    borderRadius: 6,
    borderWidth: 0.5,
    borderColor: '#50555E',
    paddingHorizontal: '1%',
    alignSelf: 'center', // Add this to ensure center alignment
  },
  colorDot: {
    height: 12,
    width: 12,
    backgroundColor: '#00EE5D',
    borderRadius: 40,

    elevation: 2,
    marginLeft: '0%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedFilter: {
    borderRadius: 6,
    width: '32%',
    height: '90%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },

  UserDefinedParamsButton: {
    height: '6%',

    width: '90%',
    backgroundColor: '#0E121A',
    borderRadius: 10,
    marginBottom: '2%',
    borderColor: '#FF7F02',
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDefinedButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FF7F02',
  },
});
