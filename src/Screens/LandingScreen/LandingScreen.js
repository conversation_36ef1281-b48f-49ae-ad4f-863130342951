import {
  Image,
  StyleSheet,
  Text,
  Vibration,
  View,
  Animated,
  Platform,
  InteractionManager,
} from 'react-native';
import React, {useEffect, useState, useRef, useCallback} from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {useNavigation} from '@react-navigation/native';

import Home from './Home';

import {useSelector} from 'react-redux';
import SiteViewSvg from '../../assets/svgs/SiteViewSvg';

import {createDrawerNavigator} from '@react-navigation/drawer';
import LandingHeader from './Components/LandingHeader';
import CustomDrawerContent from './Components/CustomDrawerContent';
import {Fonts} from '../../styles/fonts';
import Alarms from './Alarms';
import KpiMonitoring from './KpiMonitoring';
import Parameter from './Parameter';
import Configuration from './Configuration';
import Inventory from './Inventory';
import SiteIntegration from './SiteIntegration';
import ThunderChatBot from './ThunderChatBot';
import Notifications from './Notifications';
import Settings from './Settings';
import AlarmsSvg from '../../assets/svgs/AlarmsSvg';
import KpiMonitoringSvg from '../../assets/svgs/KpiMonitoringSvg';
import ParameterSvg from '../../assets/svgs/ParameterSvg';
import ConfigurationSvg from './Components/ConfigurationSvg';
import InventorySvg from '../../assets/svgs/InventorySvg.';
import SiteIntegrationSvg from '../../assets/svgs/SiteIntegrationSvg';
import ChatBotSvg from '../../assets/svgs/ChatBotSvg';
import NotificationsSvg from '../../assets/svgs/NotificationsSvg';
import SettingsSvg from '../../assets/svgs/SettingsSvg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AlarmsHeader from './Components/AlarmsComponents/AlarmsHeader/AlarmsHeader';
import ParametersHeader from './Components/ParametersComponent.js/ParametersHeader';
import ConfigurationHeader from './Components/ConfigurationComponents/Header/ConfigurationHeader';
import IntegrationMainHeader from './Components/IntegrationComponents/IntegrationMainHeader/IntegrationMainHeader';
import InventoryManagementMainHeader from './Components/InventoryManagementComponents/InventoryManagementMainHeader/InventoryManagementMainHeader';
import ChatBotHeader from './Components/ChatBotComponents/ChatBotHeaders/ChatBotHeader';
import {AUTHORIZE, Request_Types} from '../../api/uri';
import {ApiCaller} from '../../middleWare/ApiCaller';
const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

const LandingScreen = () => {
  const isDark = false;
  const navigation = useNavigation();
  const activeTintColor = isDark ? '#FFC400' : '#FF7F02';
  const scrollY = useRef(new Animated.Value(0)).current;

  const [roles, setRoles] = useState(null);
  const [hasFetchedRoles, setHasFetchedRoles] = useState(false);

  const background = isDark ? '#F1F1F1' : '#0C121D';
  const iconColor = isDark ? '#0E121A' : '#FFFFFF';

  // Handle drawer state changes

  const axiosCAll = async () => {
    const token = await AsyncStorage.getItem('Authorization');
    const payload = {
      token: token,
    };
    try {
      const response = await ApiCaller({
        method: Request_Types.POST,
        url: `${AUTHORIZE}`,
        data: payload,
      });
      const fetchedRole = response.data?.data || {};

      setTimeout(() => {
        setRoles(fetchedRole);
      }, 500); // Wait for drawer to fully close
    } catch (error) {
      console.error('API Error:', error.response?.data || error.message);
    }
  };
  useEffect(() => {
    const interaction = InteractionManager.runAfterInteractions(() => {
      axiosCAll(); // Only run this after UI animations are done
    });

    return () => interaction.cancel();
  }, []);
  // Add effect to listen for drawer state changes
  useEffect(() => {
    const unsubscribe = navigation.addListener('drawerOpen', () => {
      if (!hasFetchedRoles) {
        InteractionManager.runAfterInteractions(async () => {
          const token = await AsyncStorage.getItem('Authorization');
          const payload = {token};

          try {
            const response = await ApiCaller({
              method: Request_Types.POST,
              url: `${AUTHORIZE}`,
              data: payload,
            });
            const fetchedRole = response.data?.data || {};
            setRoles(fetchedRole);
            setHasFetchedRoles(true);
          } catch (error) {
            console.error(
              'Role API Error:',
              error.response?.data || error.message,
            );
          }
        });
      }
    });

    return unsubscribe;
  }, [hasFetchedRoles, navigation]);

  return (
    <Drawer.Navigator
      detachInactiveScreens={true}
      useLegacyImplementation={false}
      lazy={true}
      swipeEdgeWidth={20}
      swipeEnabled={true}
      drawerContent={props => <CustomDrawerContent {...props} roles={roles} />}
      screenOptions={{
        drawerActiveTintColor: activeTintColor,
        drawerHideStatusBarOnOpen: false,
        swipeEdgeWidth: 20, // ✅ always enabled
        drawerInactiveTintColor: isDark ? '#0E121A' : '#FFFFFF',
        header: () => <LandingHeader scrollY={scrollY} />,
        drawerLabelStyle: {
          fontFamily: Fonts.BaiJamjuree_SemiBold,
          marginLeft: 8,
          fontSize: 16,
        },
        drawerStyle: {
          backgroundColor: background,
        },
        drawerType: 'front',
        overlayColor: 'rgba(0,0,0,0.5)',
        drawerStatusBarAnimation: 'fade',
        gestureEnabled: true, // ✅ keep this always enabled
        gestureHandlerProps: {
          enabled: true,
          minDist: 10,
          hitSlop: {left: -5, right: 5},
        },
      }}>
      <Drawer.Screen
        name="Site Profile"
        children={() => <Home scrollY={scrollY} />}
        options={{
          drawerItemStyle: styles.drawerItem,
          drawerIcon: ({focused}) => (
            <SiteViewSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="Alarms"
        component={Alarms}
        options={{
          header: () => <AlarmsHeader />,
          drawerItemStyle: styles.drawerItem,
          drawerIcon: ({focused, color}) => (
            <AlarmsSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="KPIs Monitoring"
        component={KpiMonitoring}
        options={{
          drawerItemStyle: styles.drawerItem,
          drawerIcon: ({focused, color}) => (
            <KpiMonitoringSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="Parameter"
        component={Parameter}
        options={{
          header: () => <ParametersHeader />,
          drawerItemStyle: styles.drawerItem,
          drawerIcon: ({focused, color}) => (
            <ParameterSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />

      <Drawer.Screen
        name="Configuration"
        component={Configuration}
        options={{
          header: () => <ConfigurationHeader arrowIcon={true} />,
          drawerItemStyle: styles.drawerItem, // Add custom style for drawer items
          drawerIcon: ({focused, color}) => (
            <ConfigurationSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="Inventory Managment"
        component={Inventory}
        options={{
          header: () => <InventoryManagementMainHeader />,
          drawerItemStyle: styles.drawerItem, // Add custom style for drawer items
          drawerIcon: ({focused, color}) => (
            <InventorySvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="Integration"
        component={SiteIntegration}
        options={{
          header: () => <IntegrationMainHeader />,
          drawerItemStyle: styles.drawerItem, // Add custom style for drawer items
          drawerIcon: ({focused, color}) => (
            <SiteIntegrationSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="Thunder Chatbot"
        component={ThunderChatBot}
        options={{
          header: () => <ChatBotHeader />,
          drawerItemStyle: styles.drawerItem, // Add custom style for drawer items
          drawerIcon: ({focused, color}) => (
            <ChatBotSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="Notifications"
        component={Notifications}
        options={{
          drawerItemStyle: styles.drawerItem, // Add custom style for drawer items
          drawerIcon: ({focused, color}) => (
            <NotificationsSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
      <Drawer.Screen
        name="Settings"
        component={Settings}
        options={{
          drawerItemStyle: {display: 'none'}, // Add custom style for drawer items
          drawerIcon: ({focused, color}) => (
            <SettingsSvg color={focused ? activeTintColor : iconColor} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
};

export default LandingScreen;

const styles = StyleSheet.create({
  drawerItem: {
    borderRadius: 10,
    alignContent: 'center',
    width: '90%',
    height: '8%',
    marginLeft: '3%',
  },
});
