import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  Image,
  StatusBar,
  BackHandler,
} from 'react-native';
import React, {useState, useRef, useEffect, useMemo} from 'react';
import {useThemeStyles} from '../../Constants/useThemeStyles';
import {Fonts} from '../../styles/fonts';
import ThunderSvg from '../../assets/svgs/ThunderSvg';
import FastImage from 'react-native-fast-image';
import SendSvg from '../../assets/svgs/SendSvg';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {ApiCaller} from '../../middleWare/ApiCaller';
import {AUTHORIZE, Request_Types} from '../../api/uri';

import {
  createChatUpdater,
  generateUniqueId,
  processChunksSequentially,
} from './Components/ChatBotComponents/Constants/ChatBotConstants';

import {renderChartItem} from './Components/ChatBotComponents/Constants/RenderChartItem';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ThunderChatBot = () => {
  const themeStyles = useThemeStyles();
  const navigation = useNavigation();
  const route = useRoute();
  const sessionData = route.params?.sessionData;
  const [message, setMessage] = useState('');
  const [chatHistory, setChatHistory] = useState([]);
  const [showWelcome, setShowWelcome] = useState(true);
  const [showProfile, setShowProfile] = useState(true);
  const flatListRef = useRef(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState();
  const axiosCAll = async () => {
    const token = await AsyncStorage.getItem('Authorization');
    const payload = {
      token: token,
    };
    try {
      const response = await ApiCaller({
        method: Request_Types.POST,
        url: `${AUTHORIZE}`,
        data: payload,
      });
      const role = response.data?.data;

      setRoles(response.data?.data || {});
    } catch (error) {
      console.error('API Error:', error.response?.data || error.message);
    }
  };
  useEffect(() => {
    axiosCAll();
  }, []);
  useEffect(() => {
    // If session data is provided, add it to the chat history
    if (sessionData) {
      // Hide welcome message
      setShowWelcome(false);
      setShowProfile(false);

      // Add user message to chat
      const userMessage = {
        id: Date.now().toString(),
        text: sessionData.prompt,
        isBot: false,
      };

      // Add bot response to chat
      const botResponse = {
        id: (Date.now() + 1).toString(),
        text: sessionData.response,
        isBot: true,
      };

      // Set chat history with the session data
      setChatHistory([userMessage, botResponse]);
    }
  }, [sessionData]);

  // Reset to welcome screen when navigating to this screen
  useFocusEffect(
    React.useCallback(() => {
      // Only reset if there's no session data
      if (!sessionData) {
        setShowWelcome(true);
        setShowProfile(true);
        setChatHistory([]);
      }

      // Prevent keyboard from automatically showing
      Keyboard.dismiss();

      return () => {};
    }, [sessionData]),
  );

  // Handle hardware back button
  useEffect(() => {
    const handleBackButtonPress = () => {
      navigation.navigate('LandingScreen', {screen: 'Site Profile'});
      return true; // Prevents default back button behavior
    };

    // Add event listener for hardware back button
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    // Add listener for navigation events
    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        // If we're navigating away from this screen
        if (event.data.action.type === 'GO_BACK') {
          // Prevent default navigation behavior
          event.preventDefault();

          // Navigate to LandingScreen and open the Site Profile (Home) screen
          navigation.navigate('LandingScreen', {screen: 'Site Profile'});
        }
      },
    );

    // Clean up event listeners when component unmounts
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation]);

  // Add keyboard listeners to detect when keyboard appears/disappears
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
        // Hide profile when keyboard shows
        setShowProfile(false);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        // Only show profile again if we're still on welcome screen
        if (showWelcome) {
          setShowProfile(true);
        }
      },
    );

    // Clean up listeners
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [showWelcome]);

  // In your ThunderChatBot component, modify the processChunksSequentially function:

  const chatUpdater = useMemo(() => createChatUpdater(setChatHistory), []);

  const handleApiResponse = async responseText => {
    try {
      const chunks = responseText
        .split('\n')
        .filter(line => line.trim())
        .map(line => {
          try {
            return JSON.parse(line);
          } catch (err) {
            console.error('JSON parse error:', err);
            return {
              type: 'text',
              content: `[Error parsing response chunk: ${err.message}]`,
            };
          }
        });

      await processChunksSequentially(chunks, chatUpdater);
    } catch (error) {
      console.error('Error processing API response:', error);
      await chatUpdater.addToChat({
        id: generateUniqueId(),
        text: 'Sorry, I encountered an error processing the response.',
        isBot: true,
        isError: true,
      });
    }
  };
  const sendMessage = async () => {
    if (message.trim() === '') return;
    Keyboard.dismiss();
    if (showWelcome) {
      setShowWelcome(false);
      setShowProfile(false);
    }

    // Add user message
    await chatUpdater.addToChat({
      id: Date.now().toString(),
      text: message,
      isBot: false,
    });
    setMessage('');
    setLoading(true);
    const generatingId = generateUniqueId();
    await chatUpdater.addToChat({
      id: generatingId,
      text: 'Generating...',
      isBot: true,
      isTemp: true, // Add this flag to identify temporary messages
    });
    try {
      const token = await AsyncStorage.getItem('Authorization');
      const response = await fetch(
  'http://10.8.0.225:5012/api/stream-process',
  {
    method: 'POST',
    headers: {
      'Authorization': token, // Make sure 'token' includes 'Bearer' prefix if required
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ prompt: message.trim() }),
  }
);

      if (!response.ok) {
        throw new Error(`HTTP error: ${response.status}`);
      }

      const responseText = await response.text();
      await handleApiResponse(responseText);
      chatUpdater.removeFromChat(generatingId);
    } catch (error) {
      console.error('Chatbot API Error:', error);
      await chatUpdater.addToChat({
        id: generateUniqueId(),
        text: 'Sorry, I encountered an error processing your request.',
        isBot: true,
        isError: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle text input focus
  const handleInputFocus = () => {
    setShowProfile(false);
  };

  return (
    <View
      style={[
        styles.outerContainer,
        {backgroundColor: themeStyles.background},
      ]}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={themeStyles.background}
      />

      {/* Main content with KeyboardAvoidingView */}
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
        {/* Content area */}
        <View style={styles.contentContainer}>
          {showWelcome ? (
            <View style={styles.welcomeContainer}>
              {showProfile && (
                <>
                  <View style={styles.profile}>
                    <Image
                      source={require('../../assets/Images/chatbot.png')}
                      style={styles.image}
                    />
                  </View>
                  <Text style={[styles.welcomeTitle]}>Good afternoon,</Text>

                  <Text style={styles.welcomeText}>{roles?.name}</Text>
                </>
              )}

              <View style={styles.emptyContainer}>
                <FastImage
                  style={{
                    width: 392,
                    height: 392,
                    opacity: 0.15,
                    marginTop: '-55%',
                    marginLeft: '30%',
                  }}
                  source={require('../../assets/Images/chatbotgif.gif')}
                  resizeMode={FastImage.resizeMode.contain}
                />
              </View>
              <View style={styles.content}>
                <ThunderSvg />
                <Text style={[styles.contentHeading]}>Let me know</Text>
                <Text style={styles.contentHeading}>
                  how I can assist you today?
                </Text>
                <Text style={styles.mainContent}>
                  Need insights on your energy usage? I can help you track and
                  analyze consumption patterns, find inefficiencies, and suggest
                  improvements.
                </Text>
              </View>
            </View>
          ) : (
            <FlatList
              ref={flatListRef}
              data={chatHistory}
              renderItem={props => renderChartItem(props, themeStyles)}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.chatContainer}
              onContentSizeChange={() =>
                flatListRef.current?.scrollToEnd({animated: true})
              }
            />
          )}
        </View>

        <View style={styles.inputContainer}>
          <View
            style={[
              styles.inputWrapper,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <FastImage
              style={{width: 28, height: 28, opacity: 0.8, flex: 0.15}}
              source={require('../../assets/Images/chatbotgif_2.gif')}
              resizeMode={FastImage.resizeMode.contain}
            />
            <TextInput
              style={[styles.input, {color: themeStyles.textColor}]}
              placeholder="Type your query..."
              placeholderTextColor="#96999E"
              value={message}
              onChangeText={setMessage}
              onFocus={handleInputFocus}
              multiline
            />
            <TouchableOpacity
              style={[styles.sendButton]}
              onPress={sendMessage}
              disabled={!message.trim()}>
              <SendSvg />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

export default ThunderChatBot;

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
    paddingBottom: 10,
  },
  welcomeContainer: {
    width: '90%',
    alignSelf: 'center',
    paddingTop: '30%',
  },
  chatContainer: {
    paddingBottom: 20,
    width: '90%',
    alignSelf: 'center',
    marginTop: '20%',
    paddingBottom: 60,
    minHeight: '100%',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 6,
    marginBottom: 20,
    flexDirection: 'row',
    paddingBottom: 30,
  },

  messageText: {
    fontFamily: Fonts.BaiJamjuree_Regular,
    fontSize: 16,
    marginLeft: '3%',
  },
  inputContainer: {
    padding: 16,
    backgroundColor: 'transparent',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 6,
    height: 60,
    padding: '1%',
    justifyContent: 'center',
  },
  input: {
    flex: 1,
    fontFamily: Fonts.BaiJamjuree_Regular,
    fontSize: 16,
    height: '80%',
    padding: 2,
    marginTop: '0%',
  },
  sendButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: '2%',
  },
  emptyContainer: {
    height: '27%',
    marginTop: '2%',
  },
  profile: {
    height: 100,
    width: 100,
  },
  image: {
    height: 100,
    width: 100,
    borderRadius: 80,
  },
  content: {
    height: '40%',
    marginTop: '3%',
    justifyContent: 'center',
  },
  contentHeading: {
    fontSize: 20,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#FFFFFF',
    marginBottom: '-3%',
  },
  mainContent: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Regular,
    color: '#96999E',
    marginTop: '4%',
  },
  welcomeTitle: {
    fontSize: 30,
    fontFamily: Fonts.BaiJamjuree_Regular,
    color: '#96999E',
    marginBottom: '-4%',
  },
  welcomeText: {
    fontSize: 30,
    fontFamily: Fonts.BaiJamjuree_Regular,
    color: '#FFFFFF',
  },
});
