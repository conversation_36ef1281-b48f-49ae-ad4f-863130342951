import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  BackHandler,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import {Fonts} from '../../styles/fonts';
import {useDispatch, useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {changeTheme} from '../../redux/Slices/themeSlice';
import {useNavigation} from '@react-navigation/native';
import TestComponent from '../../../TestComponent';

const Settings = () => {
  const dispatch = useDispatch();
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(istheme === 'dark');
  const navigation = useNavigation();

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');
        if (storedTheme !== null) {
          setIsDark(JSON.parse(storedTheme));
        } else {
          setIsDark(istheme === 'dark'); // Fallback to Redux theme
        }
      } catch (error) {
        console.error('Error fetching theme:', error);
        setIsDark(istheme === 'dark'); // Fallback
      }
    };
    fetchTheme();
  }, [istheme]);
  useEffect(() => {
    const handleBackButtonPress = () => {
      // Navigate to LandingScreen and open the Site Profile (Home) screen
      navigation.navigate('LandingScreen', {screen: 'Site Profile'});
      return true; // Prevents default back button behavior
    };

    // Add event listener for hardware back button
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    // Add listener for navigation events
    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        // If we're navigating away from this screen
        if (event.data.action.type === 'GO_BACK') {
          // Prevent default navigation behavior
          event.preventDefault();

          // Navigate to LandingScreen and open the Site Profile (Home) screen
          navigation.navigate('LandingScreen', {screen: 'Site Profile'});
        }
      },
    );

    // Clean up event listeners when component unmounts
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation]);
  const setTheme = async theme => {
    setIsDark(theme === 'dark');
    dispatch(changeTheme(theme)); // Dispatch Redux action
    try {
      await AsyncStorage.setItem('Theme', JSON.stringify(theme === 'dark'));
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };
  const background = isDark ? '#FFFFFF' : '#0E121A';
  const buttonColorDark = isDark ? '#FFFFFF' : '#FFC400E5';
  const buttonColorLight = !isDark ? '#FFFFFF' : '#FFC400E5';
  return (
    <View style={[styles.container, {backgroundColor: background}]}>
      <View style={styles.containerMain}>
        <TouchableOpacity
          style={[
            styles.button,
            isDark && styles.selected,
            {backgroundColor: buttonColorDark},
          ]}
          onPress={() => setTheme('light')}>
          <Text style={styles.text}>Dark</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            !isDark && styles.selected,
            {backgroundColor: buttonColorLight},
          ]}
          onPress={() => setTheme('dark')}>
          <Text style={styles.text}>Light</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Settings;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: '95%',
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ddd',
    marginVertical: 10,
    borderRadius: 8,
  },
  text: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  selected: {},
  containerMain: {
    height: '100%',
    width: '100%',
    marginTop: '37%',
    alignItems: 'center',
  },
});
