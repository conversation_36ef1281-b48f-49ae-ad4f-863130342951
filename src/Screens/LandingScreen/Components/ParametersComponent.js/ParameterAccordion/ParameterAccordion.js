import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  FlatList,
  useCallback,
} from 'react-native';
import React, {useState} from 'react';
import Accordion from 'react-native-collapsible/Accordion';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DownArrowSvg from '../../../../../assets/svgs/DownArrowSvg';
import RightArrowSvg from '../../../../../assets/svgs/RightArrowSvg';
import {Fonts} from '../../../../../styles/fonts';

const ParameterAccordion = ({siteLoad, alarmsType, timestamp}) => {
  const themeStyles = useThemeStyles();
  const [activeSections, setActiveSections] = useState([]);

  const _renderHeader = (section, _, isActive) => {
    return (
      <View
        style={[
          styles.header,
          {
            backgroundColor: isActive
              ? themeStyles.alarmsBackground
              : 'transparent',
          },
        ]}>
        <View style={styles.leftContent}>
          {isActive ? (
            <DownArrowSvg color={'#FF7F02'} />
          ) : (
            <RightArrowSvg color={'#96999E'} />
          )}
          <Text style={[styles.key, {color: isActive ? '#FF7F02' : '#96999E'}]}>
            {section.key}
          </Text>
        </View>
      </View>
    );
  };

  const _renderContent = section => {
    const displayValue = value => {
      if (value === null || value === undefined) return '';
      if (typeof value === 'boolean') return value.toString();
      if (typeof value === 'object') return JSON.stringify(value);
      return value.toString(); // Convert all values to string for display
    };

    return (
      <View style={styles.content}>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Value
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, marginLeft: '20%', width: '64%'},
            ]}
            numberOfLines={0}>
            {displayValue(section.value)}
          </Text>
        </View>

        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Data Type
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {
                color: themeStyles.textColor,
                marginLeft: '10%',
                width: '64%',
                textAlign: 'left',
              },
            ]}>
            {section.dataType || typeof section.value}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Time Stamp
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, marginLeft: '6%', width: '64%'},
            ]}>
            {timestamp}
          </Text>
        </View>
      </View>
    );
  };

  const _updateSections = activeSections => {
    setActiveSections(activeSections);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <Accordion
          sections={siteLoad || []}
          activeSections={activeSections}
          renderHeader={_renderHeader}
          renderContent={_renderContent}
          onChange={_updateSections}
          underlayColor="transparent"
          sectionContainerStyle={styles.sectionContainer}
          style={{backgroundColor: themeStyles.alarmsBackground}}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  sectionContainer: {},
  scrollContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '2%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 6,
    borderRadius: 4,
    width: '100%',
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: '2%',
  },
  key: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    marginLeft: '4.6%',
    flex: 1,
  },
  content: {
    padding: 12,
    paddingTop: 0,
    marginLeft: '2%',
  },
  contentItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '2%',
  },
  contentItemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
});

export default ParameterAccordion;
