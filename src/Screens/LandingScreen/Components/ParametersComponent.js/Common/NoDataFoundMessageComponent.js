import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import EmptyFolder from '../../../../../assets/svgs/EmptyFolder';
import {Fonts} from '../../../../../styles/fonts';

const NoDataFoundMessageComponent = ({searchQuery}) => {
  return (
    <View style={styles.emptyContainer}>
      <EmptyFolder />
      <Text style={[styles.emptyText]}>
        {searchQuery.trim()
          ? 'No matching parameters found'
          : 'No records to display'}
      </Text>
      <Text style={styles.emptySubText}>
        {searchQuery.trim()
          ? 'Try adjusting your search terms.'
          : "Looks like there's nothing here yet. Try refreshing or checking back later."}
      </Text>
    </View>
  );
};

export default NoDataFoundMessageComponent;

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,

    alignItems: 'center',
    marginTop: '16%',
    width: '100%',
  },
  emptyText: {
    fontSize: 18,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
    textAlign: 'center',
    width: '80%',
  },
});
