import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {Fonts} from '../../../../../styles/fonts';

const ParametersFilter = ({setSelectedType}) => {
  const [isConfig, setIsConfig] = useState(true);
  const [isRealTime, setIsRealTime] = useState(false);
  const [isAlarm, setIsAlarm] = useState(false);

  return (
    <View style={styles.selectionContainer}>
      <TouchableOpacity
        style={[
          styles.selectedFilter,
          {backgroundColor: isConfig ? '#FF7F02' : 'transparent'},
        ]}
        onPress={() => {
          setIsConfig(true);
          setIsRealTime(false);
          setIsAlarm(false);
          setSelectedType('Config');
        }}>
        <Text
          style={{
            color: isConfig ? '#0E121A' : '#96999E',
            fontFamily: Fonts.BaiJamjuree_Medium,
            fontSize: 15,
          }}>
          Config
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.selectedFilter,
          {backgroundColor: isRealTime ? '#FF7F02' : 'transparent'},
        ]}
        onPress={() => {
          setIsConfig(false);
          setIsRealTime(true);
          setIsAlarm(false);
          setSelectedType('Real+Time');
        }}>
        <Text
          style={{
            color: isRealTime ? '#0E121A' : '#96999E',
            fontFamily: Fonts.BaiJamjuree_Medium,
            fontSize: 15,
          }}>
          Realtime
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.selectedFilter,
          {backgroundColor: isAlarm ? '#FF7F02' : 'transparent'},
        ]}
        onPress={() => {
          setIsConfig(false);
          setIsRealTime(false);
          setIsAlarm(true);
          setSelectedType('Alarm');
        }}>
        <Text
          style={{
            color: isAlarm ? '#0E121A' : '#96999E',
            fontFamily: Fonts.BaiJamjuree_Medium,
            fontSize: 15,
          }}>
          Alarm
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ParametersFilter;

const styles = StyleSheet.create({
  selectedFilter: {
    borderRadius: 6,
    width: '32%',
    height: '90%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectionContainer: {
    width: '96%',
    height: '5.5%',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '1%',
    flexDirection: 'row',
    borderRadius: 6,
    borderWidth: 0.5,
    borderColor: '#50555E',
    paddingHorizontal: '1%',
    alignSelf: 'center', // Add this to ensure center alignment
  },
});
