import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
  memo,
} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GET_USER_DEFINED_PARAMS, Request_Types} from '../../../../../api/uri';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import ParameterAccordion from '../ParameterAccordion/ParameterAccordion';
import NoDataFoundMessageComponent from '../Common/NoDataFoundMessageComponent';
import {Fonts} from '../../../../../styles/fonts';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import {determineValueType} from '../../../../../Constants/DetermineValueType';

const FetchUserDefinedParams = ({
  selectedType,
  siteId,
  data,
  setShowFetchedData,
  onClose,
  searchQuery,
}) => {
  const themeStyles = useThemeStyles();
  const [siteLoad, setSiteLoad] = useState([]);
  const [loading, setLoading] = useState(false);
  const [timestamp, setTimestamp] = useState(null);
  const [shouldFetch, setShouldFetch] = useState(true);
  const prevSiteIdRef = useRef(siteId);
  const prevSelectedTypeRef = useRef(selectedType);

  const fetchSiteParams = useCallback(
    async id => {
      if (!id) return;

      try {
        setLoading(true);
        const token = await AsyncStorage.getItem('Authorization');
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_USER_DEFINED_PARAMS(id, data, selectedType)}`,
          headers: {Authorization: token},
        });

        const dataObject = response.data.data[0].result?.[id];
        const timestamp = dataObject?.ts;

        const formattedData = Object.entries(dataObject || {})
          .filter(([key]) => key !== 'ts')
          .map(([key, value]) => {
            const {value: convertedValue, type} = determineValueType(value);
            return {
              key,
              value: convertedValue,
              dataType: type,
            };
          });

        setSiteLoad(formattedData);
        setTimestamp(formatTimestamp(timestamp));
        setShouldFetch(false);
      } catch (error) {
        console.error('Site Parameters Fetch Error:', error);
      } finally {
        setLoading(false);
      }
    },
    [selectedType, data, determineValueType, formatTimestamp],
  );

  // Add this effect to handle selectedType changes
  useEffect(() => {
    if (siteId) {
      setShouldFetch(true);
      fetchSiteParams(siteId);
    }
  }, [selectedType]); // Only depend on selectedType

  // Modify the existing fetch effect to handle other cases
  useEffect(() => {
    if (siteId && shouldFetch) {
      fetchSiteParams(siteId);

      // Set up interval for periodic updates
      const interval = setInterval(() => {
        fetchSiteParams(siteId);
      }, 5 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [siteId, shouldFetch, fetchSiteParams]);

  const filteredParameters = useMemo(() => {
    if (!searchQuery.trim() || !siteLoad) return siteLoad;

    const query = searchQuery.toLowerCase();
    return siteLoad.filter(param => {
      const description = String(param.description || '').toLowerCase();
      const key = String(param.key || '').toLowerCase();
      return description.includes(query) || key.includes(query);
    });
  }, [siteLoad, searchQuery]);

  const handleClose = useCallback(() => {
    setShouldFetch(false);
    onClose();
  }, [onClose]);

  const handleChangeParams = useCallback(() => {
    setShouldFetch(false);
    setShowFetchedData(false);
  }, [setShowFetchedData]);

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator animating={loading} size="large" color="#FF7F02" />
        </View>
      ) : siteLoad.length > 0 ? (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContentContainer}
          showsVerticalScrollIndicator={false}>
          <ParameterAccordion
            siteLoad={filteredParameters}
            alarmsType={selectedType}
            timestamp={timestamp}
          />
        </ScrollView>
      ) : (
        <NoDataFoundMessageComponent searchQuery={searchQuery} />
      )}

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.backButton, {borderWidth: 1, borderColor: '#FF2322'}]}
          onPress={handleClose}>
          <Text style={styles.clearButtonText}>Clear</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.backButton, {backgroundColor: '#FF7F02'}]}
          onPress={handleChangeParams}>
          <Text style={styles.changeParamsText}>Change Params</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default memo(FetchUserDefinedParams);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  scrollView: {
    flex: 1,
    width: '100%',
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingVertical: 10,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    width: '90%',
    height: '9.5%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '1%',
  },
  backButton: {
    height: '90%',
    width: '48%',

    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FF2322',
  },
  changeParamsText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#0E121A',
  },
});
