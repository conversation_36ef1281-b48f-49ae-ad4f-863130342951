import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useState, useCallback, useMemo} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Dropdown} from 'react-native-element-dropdown';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {Fonts} from '../../../../../styles/fonts';
import {
  GET_ALL_PARAMS,
  GET_DROPDOWN_CATEGORY,
  Request_Types,
} from '../../../../../api/uri';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import {determineValueType} from '../../../../../Constants/DetermineValueType';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';

const ParamsDropDown = ({
  setDate,
  isLandscape,
  modalHeight,
  setSelectedItem,
  selectedItem,
}) => {
  const themeStyles = useThemeStyles();

  const [siteId, setSiteId] = useState('');
  const [siteLoad, setSiteLoad] = useState([]);

  const isSiteID = useSelector(state => state.siteId.siteId);

  const [isTypeChanging, setIsTypeChanging] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const fetchCategory = useCallback(
    async id => {
      if (!id) return;

      try {
        setLoading(true);
        setIsTypeChanging(true);
        const token = await AsyncStorage.getItem('Authorization');

        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_ALL_PARAMS(id)}`,
          headers: {Authorization: token},
        });

        const dataArray = response.data.data;

        // Extract unique categories
        const uniqueCategories = new Set(
          dataArray.map(item => item.category).filter(Boolean),
        );

        // Convert to required format for dropdown
        const formattedCategories = Array.from(uniqueCategories).map(
          (category, index) => ({
            id: index.toString(),
            title: category,
            name: category,
          }),
        );

        setSiteLoad(formattedCategories);

        // Find and set the General category as default
        const generalCategory = formattedCategories.find(
          category => category.name.toLowerCase() === 'General',
        );

        if (generalCategory && !selectedItem) {
          setSelectedItem(generalCategory);
        } else if (formattedCategories.length > 0 && !selectedItem) {
          // If no General category, set the first item as default
          setSelectedItem(formattedCategories[0]);
        }
      } catch (error) {
        console.error('Site Parameters Fetch Error:', error);
      } finally {
        setLoading(false);
        setIsTypeChanging(false);
      }
    },
    [isSiteID, selectedItem, setSelectedItem],
  );

  useEffect(() => {
    if (isSiteID && isSiteID !== siteId) {
      setSiteId(isSiteID);
      fetchCategory(isSiteID);
    }
  }, [isSiteID, fetchCategory]);

  useEffect(() => {
    if (siteId) {
      fetchCategory(siteId);
    }
  }, [selectedItem, siteId, fetchCategory]);

  const handleSelectItem = useCallback(
    item => {
      if (item) {
        setSelectedItem(item);
      }
    },
    [setSelectedItem],
  );

  return (
    <View
      style={[styles.dayFilter, {marginLeft: !isLandscape ? '7%' : '155%'}]}>
      <Dropdown
        style={[
          styles.dropdown,
          {
            backgroundColor: '#FFFFFF1A',
            borderColor: '#50555E',
          },
        ]}
        placeholderStyle={[
          styles.placeholderStyle,
          {color: themeStyles.textColor},
        ]}
        selectedTextStyle={[
          styles.selectedTextStyle,
          {color: themeStyles.textColor},
        ]}
        data={siteLoad}
        maxHeight={modalHeight * 0.25}
        labelField="name"
        valueField="id"
        value={selectedItem?.id}
        placeholder="Select category"
        onChange={handleSelectItem}
        containerStyle={[
          styles.dropdownContainer,
          {backgroundColor: themeStyles.powerSourceChart},
        ]}
        itemContainerStyle={styles.itemContainer}
        activeColor="#FF7F02"
        itemTextStyle={[styles.itemText, {color: themeStyles.textColor}]}
        renderItem={item => (
          <Text style={[styles.itemText, {color: themeStyles.textColor}]}>
            {item.name}
          </Text>
        )}
      />
    </View>
  );
};

export default ParamsDropDown;

const styles = StyleSheet.create({
  dayFilter: {
    height: '40%',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '4%',
    justifyContent: 'center',
    borderRadius: 6,
  },
  dropdown: {
    height: 46,
    width: '100%',

    borderRadius: 6,
    borderWidth: 0,
    paddingHorizontal: 15,
    marginLeft: '-14%',
  },
  placeholderStyle: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Bold,
  },
  selectedTextStyle: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Bold,
    textAlign: 'left',
  },
  itemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    padding: 10,
  },
  dropdownContainer: {
    borderRadius: 6,
    marginTop: 5,
    borderWidth: 1,
  },
  itemContainer: {
    height: 47,
    justifyContent: 'center',
  },
});
