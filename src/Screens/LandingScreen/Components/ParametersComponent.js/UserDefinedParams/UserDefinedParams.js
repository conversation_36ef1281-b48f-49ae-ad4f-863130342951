import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import DropDown from '../../SearchScreen/DropDown';
import {useDispatch, useSelector} from 'react-redux';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {Fonts} from '../../../../../styles/fonts';
import {GetsiteID} from '../../../../../redux/Slices/SiteIdSlice';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SearchIconDark from '../../../../../assets/svgs/SearchIconDark';
import {
  GET_ALL_PARAMS,
  GET_PARAMETERS,
  Request_Types,
} from '../../../../../api/uri';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import {Checkbox} from '@ant-design/react-native';
import TickSvg from '../../../../../assets/svgs/TickSvg';
import qs from 'qs';
import ParametersFilter from '../Common/ParametersFilter';
import ParametersHeader from '../Common/ParametersScreenHeader';
import FetchUserDefinedParams from '../FetchUserDefinedParams/FetchUserDefinedParams';
import Toast from 'react-native-toast-message';

const UserDefinedParams = ({onClose}) => {
  const dispatch = useDispatch();
  const [checked, setChecked] = useState(false);
  const [siteId, setSiteId] = useState('');
  const themeStyles = useThemeStyles();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [siteLoad, setSiteLoad] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState('Config');
  const [selectedParams, setSelectedParams] = useState([]);
  const [showFetchedData, setShowFetchedData] = useState(false);
  const [configDisabled, setConfigDisabled] = useState(false);
  const [realtimeDisabled, setRealtimeDisabled] = useState(false);
  const [alarmDisabled, setAlarmDisabled] = useState(false);
  const [totalParams, setTotalParams] = useState([]);
  const [shouldFetch, setShouldFetch] = useState(true); // Add this state

  const handleSiteIdChange = useCallback(
    async newSiteId => {
      setSiteId(newSiteId);
      dispatch(GetsiteID(newSiteId));
      setShouldFetch(true); // Enable fetching when site ID changes
      try {
        await AsyncStorage.setItem('SelectedSiteId', newSiteId);
      } catch (error) {
        console.error('Error saving site ID:', error);
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (isSiteID && isSiteID !== siteId) {
      setSiteId(isSiteID);
      setShouldFetch(true); // Enable fetching when isSiteID changes
    }
  }, [isSiteID]);

  const fetchSiteParams = useCallback(
    async id => {
      if (!id || !shouldFetch) return; // Add shouldFetch check

      try {
        setLoading(true);
        const token = await AsyncStorage.getItem('Authorization');
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_ALL_PARAMS(id)}`,
          headers: {Authorization: token},
        });

        const filteredParams = (response.data.data || []).filter(param => {
          switch (selectedType) {
            case 'Config':
              return param.subcategory === 'Config';
            case 'Real+Time':
              return param.subcategory === 'Real Time';
            case 'Alarm':
              return param.subcategory === 'Alarm';
            default:
              return true;
          }
        });
        setTotalParams(response.data.data || []);

        const paramsWithIds = filteredParams.map((param, index) => ({
          ...param,
          uniqueId: `param_${index}`,
        }));

        setSiteLoad(paramsWithIds);
        setShouldFetch(false); // Disable fetching after successful fetch
      } catch (error) {
        console.error('Site Parameters Fetch Error:', error);
      } finally {
        setLoading(false);
      }
    },
    [selectedType, shouldFetch], // Add shouldFetch to dependencies
  );

  // Modify the type change handler
  const handleTypeChange = newType => {
    setSelectedType(newType);
    setShouldFetch(true); // Enable fetching when type changes
  };

  useEffect(() => {
    if (siteId && shouldFetch) {
      fetchSiteParams(siteId);
    }
  }, [siteId, fetchSiteParams, selectedType, shouldFetch]);

  const filteredParameters = useMemo(() => {
    if (!searchQuery.trim() || !siteLoad) return siteLoad;

    const query = searchQuery.toLowerCase();
    return siteLoad.filter(param => {
      // Safely handle potentially undefined description
      const description = String(param.description || '').toLowerCase();
      const key = String(param.key || '').toLowerCase();

      return description.includes(query) || key.includes(query);
    });
  }, [siteLoad, searchQuery]);
  const handleCheckboxToggle = paramId => {
    setSelectedParams(prev => {
      if (prev.includes(paramId)) {
        // Remove the description if it's already selected
        const newSelection = prev.filter(desc => desc !== paramId);
        setChecked(newSelection.length === siteLoad.length);
        return newSelection;
      } else {
        // Add the description
        const newSelection = [...prev, paramId];
        setChecked(newSelection.length === siteLoad.length);
        return newSelection;
      }
    });
  };

  const handleSelectAll = () => {
    const newChecked = !checked;
    setChecked(newChecked);

    if (newChecked) {
      // Select all descriptions
      const allDescriptions = siteLoad.map(param => param.description);
      setSelectedParams(allDescriptions);
    } else {
      // Deselect all parameters
      setSelectedParams([]);
    }
  };

  const encoded = encodeURIComponent(selectedParams)
    .replace(/%20/g, '+')
    .replace(/\(/g, '%28')
    .replace(/\)/g, '%29');

  // Handle back button press
  const handleBack = () => {
    setShouldFetch(false); // Disable fetching when going back
    if (onClose) {
      onClose();
    }
  };

  // Handle clear button press
  const handleClear = () => {
    setSelectedParams([]);
    setChecked(false);
    if (onClose) {
      onClose();
    }
  };
  const showToast = () => {
    Toast.show({
      type: 'info',
      text1: 'Info',
      text2: 'Atleast Select One Parameter',
    });
  };
  const handleShowFetchedData = () => {
    if (selectedParams.length === 0) {
      showToast();
    } else {
      setShouldFetch(false); // Disable fetching when showing fetched data
      setShowFetchedData(true);
    }
  };
  return (
    <View style={styles.container}>
      <ParametersHeader
        siteId={siteId}
        onSiteIdChange={handleSiteIdChange}
        showSearch={true}
        showParamsDropdown={false}
        showCount={false}
        setSearchQuery={setSearchQuery}
        showCrossIcon={false}
        onClose={handleBack} // Add close handler to header
      />

      <ParametersFilter
        setSelectedType={handleTypeChange} // Use new handler
        configDisabled={configDisabled}
        realtimeDisabled={realtimeDisabled}
        alarmDisabled={alarmDisabled}
      />
      {showFetchedData ? (
        <FetchUserDefinedParams
          selectedType={selectedType}
          siteId={siteId}
          data={encoded}
          setShowFetchedData={setShowFetchedData}
          setConfigDisabled={setConfigDisabled}
          setRealtimeDisabled={setRealtimeDisabled}
          setAlarmDisabled={setAlarmDisabled}
          onClose={handleBack} // Pass the close handler'
          searchQuery={searchQuery}
        />
      ) : (
        <>
          <View style={styles.totalParametersContainer}>
            <View style={styles.selectCheckBoxContainer}>
              <TouchableOpacity
                activeOpacity={0.7}
                style={[
                  styles.checkBox,
                  {
                    borderColor: checked
                      ? themeStyles.isDark
                        ? '#96999E'
                        : '#0E121ACC'
                      : themeStyles.isDark
                      ? '#0E121ACC'
                      : '#96999E',
                  },
                  checked && styles.checked,
                ]}
                onPress={handleSelectAll}>
                {checked && <TickSvg />}
              </TouchableOpacity>
              <Text style={styles.selectAllText}>Select All</Text>
            </View>
            <Text style={styles.counterText}>
              {selectedParams.length}/{totalParams?.length || 0}
            </Text>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                animating={loading}
                size="large"
                color="#FF7F02"
              />
            </View>
          ) : (
            <ScrollView
              style={styles.parametersList}
              contentContainerStyle={{
                alignItems: 'center',
              }}>
              {filteredParameters?.map(param => (
                <View key={param.uniqueId} style={styles.parameterItem}>
                  <TouchableOpacity
                    activeOpacity={0.7}
                    style={[
                      styles.checkBox,
                      {
                        borderColor: selectedParams.includes(param.description)
                          ? '#FF7F02'
                          : '#96999E',
                      },
                      selectedParams.includes(param.description) &&
                        styles.checked,
                    ]}
                    onPress={() => handleCheckboxToggle(param.description)}>
                    {selectedParams.includes(param.description) && <TickSvg />}
                  </TouchableOpacity>
                  <View style={styles.parameterTextContainer}>
                    <Text style={styles.parameterDescription}>
                      {param.description || 'No description available'}
                    </Text>
                  </View>
                </View>
              ))}
            </ScrollView>
          )}

          <View style={styles.footer}>
            <TouchableOpacity
              style={[
                styles.backButton,
                {borderWidth: 1, borderColor: '#616161'},
              ]}
              onPress={handleBack} // Use handleBack
              activeOpacity={0.7}>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: Fonts.BaiJamjuree_SemiBold,
                  color: '#FFFFFF',
                }}>
                Back
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.backButton, {backgroundColor: '#FF7F02'}]}
              onPress={handleShowFetchedData}>
              <Text
                style={{
                  fontSize: 15,
                  fontFamily: Fonts.BaiJamjuree_SemiBold,
                  color: '#0E121A',
                }}>
                FetchData
              </Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );
};

export default UserDefinedParams;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    alignItems: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  header: {
    height: '7%',
    backgroundColor: '#0C121D',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectionContainer: {
    width: '96%',
    height: '5.5%',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '1%',
    flexDirection: 'row',
    borderRadius: 6,
    borderWidth: 0.5,
    borderColor: '#50555E',
    paddingHorizontal: '1%',
    alignSelf: 'center', // Add this to ensure center alignment
  },
  selectedFilter: {
    borderRadius: 6,
    width: '32%',
    height: '90%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  siteIdContainer: {
    height: '61%',
    width: '96%',
    flexDirection: 'row',
    alignContent: 'center',
    overflow: 'visible',

    marginLeft: '0%',
    alignItems: 'center',
  },
  siteContainer: {
    width: '96%',
    height: '8%',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: '15%',
    flexDirection: 'row',
    marginBottom: '-6%', // Add small margin at bottom of site container
  },
  totalParametersContainer: {
    height: '8%',
    width: '93%',

    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: '#616161',
    marginBottom: '-2%',
  },
  colorDot: {
    height: 12,
    width: 12,
    backgroundColor: '#00EE5D',
    borderRadius: 40,

    elevation: 2,
    marginLeft: '0%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchFieldContainer: {
    width: '96%',
    height: '5.5%',

    alignItems: 'center',
    justifyContent: 'space-around',
    marginTop: '6%',
    flexDirection: 'row',
    alignSelf: 'center',
    borderRadius: 6,
    marginBottom: '2.2%',
  },
  checkBox: {
    height: 18,
    width: 18,
    borderWidth: 1,
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: '3%',
  },
  checked: {
    backgroundColor: '#FF7F02',
    borderColor: '#FF7F02',
  },
  selectCheckBoxContainer: {
    width: '50%',
    height: '50%',
    alignItems: 'center',

    flexDirection: 'row',
  },
  parameterTextContainer: {
    marginLeft: '5.5%',
  },
  parameterLabel: {
    fontSize: 14,
    fontFamily: Fonts.BaiJamjuree_Regular,
    color: '#FFFFFF',
  },
  parameterDescription: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Regular,
    color: '#96999E',
  },
  parametersList: {
    width: '100%',
    marginTop: '2%',
  },
  parameterItem: {
    flexDirection: 'row',
    height: 'auto',
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: '96%',
    marginTop: '4%',
  },
  icon: {
    width: 8,
    height: 8,
    resizeMode: 'cover',
  },
  selectAllText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#FFFFFF',
    marginLeft: '11%',
  },
  counterText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },
  footer: {
    width: '90%',
    height: '6%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'absolute',
    bottom: '1%',
    alignSelf: 'center',
  },
  backButton: {
    height: '100%',
    width: '48%',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0E121A',
  },
  buttonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FFFFFF',
  },
});
