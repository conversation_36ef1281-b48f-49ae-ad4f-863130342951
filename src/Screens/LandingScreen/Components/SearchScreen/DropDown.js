import {
  StyleSheet,
  Text,
  View,
  InteractionManager,
  TouchableOpacity,
} from 'react-native';
import React, {useCallback, useEffect, useState, useMemo, useRef} from 'react';
import {AutocompleteDropdown} from 'react-native-autocomplete-dropdown';
import {Fonts} from '../../../../styles/fonts';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {GET_SITES_LIST, Request_Types} from '../../../../api/uri';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useDispatch, useSelector} from 'react-redux';
import {GetsiteID, GetsiteIDAlarms} from '../../../../redux/Slices/SiteIdSlice';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';

const DropDown = ({onSiteIdChange, setLineColor, isIndependent = false}) => {
  const dispatch = useDispatch();
  const [inputValue, setInputValue] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [siteIds, setSiteIds] = useState([]);
  const currentSiteId = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();
  const siteDataRef = useRef([]);

  const fetchSiteData = useCallback(async () => {
    try {
      const token = await AsyncStorage.getItem('Authorization');
 
      console.log('Token:', token);
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_SITES_LIST}`,
        headers: {Authorization: token},
      });
     
 

      const siteIds = response.data?.data
        ?.map((item, index) => ({
          id: index.toString(),
          name: item?.siteId?.toString() || '',
          title: item?.siteId?.toString() || '',
        }))
        .filter(item => item.name); // Filter out items with empty names



      siteDataRef.current = siteIds || [];
      setSiteIds(siteIds || []);

      // Only auto-select if currentSiteId is null (not empty string)
      if (currentSiteId === null && siteIds?.length > 0) {
        handleSelectItem(siteIds[0]);
      } else if (currentSiteId && currentSiteId !== '' && siteIds?.length > 0) {
        const currentSite = siteIds.find(site => site?.name === currentSiteId);
        if (currentSite) {
          setInputValue(currentSiteId);
          setSelectedItem(currentSite);
        }
      }
    } catch (error) {
      console.error('Site Data Fetch Error:', error);
      siteDataRef.current = [];
      setSiteIds([]);
    }
  }, [currentSiteId, handleSelectItem]);

  // Update local state when Redux state changes
  useEffect(() => {
    if (currentSiteId && siteIds.length > 0) {
      const matchingSite = siteIds.find(site => site.name === currentSiteId);
      if (matchingSite) {
        setInputValue(currentSiteId);
        setSelectedItem(matchingSite);
      }
    } else if (currentSiteId === '') {
      // If currentSiteId is empty string (cleared), ensure input stays empty
      setInputValue('');
      setSelectedItem(null);
    }
  }, [currentSiteId, siteIds]);

  useEffect(() => {
    fetchSiteData();
  }, [fetchSiteData]);

  const [isDark, setIsDark] = useState(false);
  const istheme = useSelector(state => state.theme.theme);

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');
        setIsDark(storedTheme ? JSON.parse(storedTheme) : istheme === 'dark');
      } catch (error) {
        console.error('Error fetching theme:', error);
        setIsDark(istheme === 'dark');
      }
    };

    fetchTheme();
    fetchSiteData();
  }, [istheme]);

  const handleSelectItem = useCallback(
    async item => {
      if (!item?.name) return; // Early return if item or item.name is undefined

      // Don't proceed if the name is empty
      if (item.name.trim() === '') return;

      if (item.name !== inputValue) {
        setSelectedItem(item);
        setInputValue(item.name);

        if (!isIndependent) {
          dispatch(GetsiteID(item.name));
          await AsyncStorage.setItem('SelectedSiteId', item.name);
        } else {
          dispatch(GetsiteIDAlarms(item.name));
          await AsyncStorage.setItem('SelectedAlarmsSiteId', item.name);
        }

        if (onSiteIdChange) {
          onSiteIdChange(item.name);
        }

        if (setLineColor) {
          setLineColor(false);
        }
      }
    },
    [dispatch, inputValue, onSiteIdChange, setLineColor, isIndependent],
  );
  const handleSearch = useCallback(query => {
    setInputValue(query);
  }, []);

  const handleFocus = useCallback(() => {
    setLineColor('#FF7F02');
  }, []);

  const handleBlur = useCallback(() => {
    setLineColor('#96999E');
  }, []);

  const handleClear = useCallback(() => {
    setInputValue('');
    setSelectedItem(null);

    // Clear Redux state
    dispatch(GetsiteID('')); // Use empty string instead of null

    // Remove from AsyncStorage properly
    try {
      AsyncStorage.removeItem('SelectedSiteId');
    } catch (error) {
      console.error('Error removing site ID:', error);
    }

    if (onSiteIdChange) {
      onSiteIdChange(''); // Use empty string instead of null
    }
  }, [dispatch, onSiteIdChange]);

  const filteredData = useMemo(() => {
    // Return empty array if no data
    if (!siteDataRef.current) return [];

    // If no input value, return all data
    if (!inputValue?.trim()) return siteDataRef.current;

    const lowerQuery = inputValue.toLowerCase();
    return siteDataRef.current.filter(
      item => item?.name?.toLowerCase()?.includes(lowerQuery) || false,
    );
  }, [inputValue]);

  const textInputProps = {
    value: inputValue,
    onChangeText: handleSearch,
    style: {
      color: themeStyles.dropDownTextColor,
      fontFamily: Fonts.BaiJamjuree_Bold,
      fontSize: 30,
      textAlign: 'center',
      textAlignVertical: 'center',

      overflow: 'hidden',
      width: 'auto',
      height: 70,
      textAlign: 'left',
      marginLeft: '10%',
    },
    placeholderTextColor: themeStyles.dropDownTextColor,
  };

  return (
    <View style={styles.dropdown}>
      <AutocompleteDropdown
        dataSet={filteredData}
        forcePopupIcon={false}
        showClear={true} // Enable the built-in clear button
        clearOnFocus={false}
        onSelectItem={handleSelectItem}
        onClear={handleClear}
        onChangeText={handleSearch}
        textInputProps={textInputProps}
        useFilter={false}
        showChevron={false}
        inputValue={inputValue}
        EmptyResultComponent={() => (
          <Text
            style={{
              color: themeStyles.textColor,
              fontSize: 15,
              fontFamily: Fonts.BaiJamjuree_Medium,
              padding: 15,
            }}>
            Nothing found
          </Text>
        )}
        renderItem={item => (
          <Text
            style={{
              color: themeStyles.textColor,
              fontSize: 15,
              fontFamily: Fonts.BaiJamjuree_Medium,
              height: 55, // Ensure consistent height
              padding: 15,
              backgroundColor: 'transparent',
            }}>
            {item.name}
          </Text>
        )}
        rightIcon={null}
        clearIcon={() => (
          <TouchableOpacity
            onPress={handleClear}
            style={styles.clearIconButton}>
            <Text style={styles.clearIconText}>×</Text>
          </TouchableOpacity>
        )}
        containerStyle={{
          paddingRight: 0,
          backgroundColor: 'transparent',
          width: 170,
          height: 200, // You can increase this if needed
          alignItems: 'flex-end',
          marginLeft: !isIndependent ? '12%' : '9%',
          zIndex: 1000, // Add this to ensure it appears above other elements
        }}
        inputContainerStyle={{
          backgroundColor: 'transparent',
          height: 70,
          width: '120%',
          borderWidth: 0,
          borderBottomWidth: 0,
        }}
        suggestionsListContainerStyle={{
          backgroundColor: themeStyles.background,
          maxHeight: 300, // Adjust this value as needed
          width: 200, // You can make the dropdown wider if needed
          borderRadius: 8, // Optional: add rounded corners
          borderWidth: 1, // Optional: add a border
          borderColor: '#404346', // Optional: border color
          marginTop: 5, // Add some space between input and suggestions
        }}
        suggestionsListTextStyle={{
          color: themeStyles.textColor, // This will affect all suggestion text including "Nothing found"
        }}
        renderRightIcon={() => null}
        renderClearIcon={() => null}
        ItemSeparatorComponent={() => null}
      />
    </View>
  );
};

export default React.memo(DropDown);

const styles = StyleSheet.create({
  dropdown: {
    height: '160%',
    backgroundColor: 'transparent',
    paddingHorizontal: 8,
    width: 160,
    alignSelf: 'center',
  },
  clearIconButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(150, 150, 150, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  clearIconText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
