import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DrawerIcon from '../../../../../assets/svgs/DrawerIcon';
import {Fonts} from '../../../../../styles/fonts';

const ChatBotHeader = () => {
  const isDark = false;

  const navigation = useNavigation();
  const themeStyles = useThemeStyles();

  const background = isDark ? '#FFFFFF1A' : '#0C121D';

  return (
    <View style={[styles.container, {backgroundColor: background}]}>
      <TouchableOpacity
        style={styles.drawerIcon}
        onPress={() => navigation.toggleDrawer()}>
        <DrawerIcon color={isDark ? '#0E121A' : '#FFFFFF'} />
      </TouchableOpacity>
      <Text style={[styles.alarmsHeaderText, {color: themeStyles.textColor}]}>
        Chatbot
      </Text>
      <TouchableOpacity
        style={styles.rightContainer}
        // onPress={() => navigation.navigate('AskedQueries')}
      >
        <Text style={styles.text}>Asked Queries</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ChatBotHeader;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#0C121D1A',
    justifyContent: 'space-between', // Use space-between for proper spacing
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  drawerIcon: {
    width: 50,
    height: 50,
    alignItems: 'flex-start',
    flex: 0.5,
    marginLeft: '5%',
    justifyContent: 'center',
  },
  alarmsHeaderText: {
    fontSize: 20,
    flex: 1.8,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  rightContainer: {
    flexDirection: 'row',
    // Allow the container to take up remaining space
    justifyContent: 'flex-end', // Align icons to the right
    alignItems: 'center',
    backgroundColor: '#FFFFFF1A',
    marginRight: '5%',
    height: '50%',
    width: '35%',
    borderRadius: 6,
    justifyContent: 'center',
  },
  icon: {
    marginHorizontal: 35, // Add some horizontal margin between icons
  },
  icon1: {
    marginHorizontal: 1, // Add some horizontal margin between icons
  },
  text: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Regular,
    color: '#FFFFFF',
    textAlign: 'center',
  },
});
