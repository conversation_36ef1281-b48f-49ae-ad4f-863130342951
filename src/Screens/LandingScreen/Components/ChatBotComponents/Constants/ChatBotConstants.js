export const generateUniqueId = () => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${performance
    .now()
    .toString(36)
    .replace('.', '')}`;
};

export const createChatUpdater = setChatHistory => {
  const addToChat = newMessage => {
    return new Promise(resolve => {
      setChatHistory(prevChat => [...prevChat, newMessage]);
      setTimeout(() => resolve(), 100);
    });
  };

  const updateChat = updaterFn => {
    setChatHistory(updaterFn);
  };
  const removeFromChat = idToRemove => {
    setChatHistory(prevChat => prevChat.filter(msg => msg.id !== idToRemove));
  };
  const renderTextChunk = async text => {
    const words = text.split(' ');
    const id = generateUniqueId();

    // Add initial empty message
    await addToChat({
      id,
      type: 'text',
      text: '',
      isBot: true,
    });

    // Add words one by one
    for (const word of words) {
      await new Promise(resolve => setTimeout(resolve, 50));
      updateChat(prevChat =>
        prevChat.map(msg =>
          msg.id === id ? {...msg, text: `${msg.text} ${word}`.trim()} : msg,
        ),
      );
    }
  };

  return {
    addToChat,
    updateChat,
    renderTextChunk,
    removeFromChat,
  };
};

export const processChunksSequentially = async (chunks, chatUpdater) => {
  for (const chunk of chunks) {
    try {
      if (!chunk.type) {
        console.warn('Received chunk with no type:', chunk);
        continue;
      }

      switch (chunk.type) {
        case 'text':
        case 'analysis':
          await chatUpdater.renderTextChunk(chunk.content);
          break;

        case 'dataframe':
          await chatUpdater.addToChat({
            id: generateUniqueId(),
            type: 'dataframe',
            data: tryParseJson(chunk.content),
            isBot: true,
          });
          break;

        case 'json':
          await chatUpdater.addToChat({
            id: generateUniqueId(),
            type: 'json',
            data: tryParseJson(chunk.content),
            isBot: true,
          });
          break;

        default:
          await chatUpdater.addToChat({
            id: generateUniqueId(),
            type: 'text',
            text: `[Unknown message type: ${chunk.type}]`,
            isBot: true,
            isError: true,
          });
      }
    } catch (error) {
      console.error('Error processing chunk:', error);
      await chatUpdater.addToChat({
        id: generateUniqueId(),
        text: `Error processing ${chunk.type} chunk: ${error.message}`,
        isBot: true,
        isError: true,
      });
    }
  }
};

const tryParseJson = content => {
  try {
    return JSON.parse(content);
  } catch (err) {
    console.error('JSON parse error:', err);
    return {error: 'Failed to parse data'};
  }
};
