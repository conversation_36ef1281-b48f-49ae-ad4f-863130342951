import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {FormattedText} from './FormattedText';
import ThunderSvg from '../../../../../assets/svgs/ThunderSvg';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import Plotly<PERSON>hart from '../../../../Components/Charts/PlotlyChart';

export const renderChartItem = ({item}, themeStyles) => {
  if (!item.isBot) {
    return (
      <View style={[styles.messageBubble, styles.userBubble]}>
        <FormattedText
          text={item.text}
          style={[styles.messageText, {color: '#96999E'}]}
        />
      </View>
    );
  }
  if (item.isTemp) {
    return (
      <View style={[styles.messageBubble, styles.botBubble]}>
        <ThunderSvg />
        <Text
          style={[
            styles.messageText,
            {color: themeStyles.textColor, fontStyle: 'italic', opacity: 0.6},
          ]}>
          {item.text}
        </Text>
      </View>
    );
  }

  switch (item.type) {
    case 'text':
      return (
        <View style={[styles.messageBubble, styles.botBubble]}>
          <ThunderSvg />
          <FormattedText
            text={item.text}
            style={[styles.messageText, {color: themeStyles.textColor}]}
          />
        </View>
      );

    case 'dataframe':
      return <DataFrameMessage data={item.data} themeStyles={themeStyles} />;

    case 'analysis':
      return (
        <View style={[styles.messageBubble, styles.botBubble]}>
          <ThunderSvg />
          <View style={{marginLeft: 6}}>
            <Text
              style={[
                styles.messageText,
                {color: themeStyles.textColor, width: '100%'},
              ]}>
              {item.data}
            </Text>
          </View>
        </View>
      );

    case 'json':
      return (
        <View style={[styles.messageBubble, styles.botBubble]}>
          <ThunderSvg />
          <View style={{marginLeft: 6}}>
            <PlotlyChart plotlyJson={item.data} />
          </View>
        </View>
      );

    default:
      console.warn('Skipped item because of missing content:', item);
      return null;
  }
};
const COLUMN_WIDTH = 170;
const DataFrameMessage = ({data, themeStyles}) => {
  const headers = data.length > 0 ? Object.keys(data[0]) : [];

  return (
    <View style={[styles.messageBubble, styles.botBubble]}>
      <ThunderSvg />
      <ScrollView horizontal>
        <View style={{marginLeft: 10, width: headers.length * COLUMN_WIDTH}}>
          <View style={{flexDirection: 'row', marginTop: 8}}>
            {headers.map((header, idx) => (
              <Text key={idx} style={styles.headerText(themeStyles.textColor)}>
                {header}
              </Text>
            ))}
          </View>
          <ScrollView style={{maxHeight: 300}}>
            {data.map((row, index) => (
              <View key={index} style={{flexDirection: 'row'}}>
                {headers.map((header, idx) => (
                  <Text
                    key={idx}
                    style={styles.cellText(themeStyles.textColor)}>
                    {formatDataValue(row[header])}
                  </Text>
                ))}
              </View>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </View>
  );
};
const formatDataValue = value => {
  if (typeof value === 'number' && value > 1000000000000) {
    return formatTimestamp(value);
  }
  return typeof value === 'object' ? JSON.stringify(value) : value;
};

const styles = StyleSheet.create({
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 6,
    marginBottom: 16,
    flexDirection: 'row',
  },
  botBubble: {
    alignSelf: 'flex-start',
  },
  userBubble: {
    alignSelf: 'flex-end',
    backgroundColor: '#FFFFFF1A',
  },
  messageText: {
    fontFamily: 'BaiJamjuree-Regular',
    fontSize: 16,
    marginLeft: '3%',
  },
  headerText: color => ({
    width: COLUMN_WIDTH,
    fontWeight: 'bold',
    color,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    padding: 4,
  }),
  cellText: color => ({
    width: COLUMN_WIDTH,
    color,
    padding: 4,
  }),
});
