import {Text} from 'react-native';
import {Fonts} from '../../../../../styles/fonts';

export const FormattedText = ({text, style}) => {
  if (!text || !text.includes('**')) {
    return <Text style={style}>{text}</Text>;
  }

  // Split the text by ** markers
  const segments = text.split(/(\*\*.*?\*\*)/g);

  return (
    <Text style={style}>
      {segments.map((segment, index) => {
        if (segment.startsWith('**') && segment.endsWith('**')) {
          // This is bold text - remove the ** markers and apply bold style
          const boldText = segment.slice(2, -2);
          return (
            <Text
              key={index}
              style={{
                fontFamily: Fonts.BaiJamjuree_Medium,
                fontWeight: 'bold',
                color: style.color,
              }}>
              {boldText}
            </Text>
          );
        } else if (segment) {
          // Regular text
          return <Text key={index}>{segment}</Text>;
        }
        return null;
      })}
    </Text>
  );
};
