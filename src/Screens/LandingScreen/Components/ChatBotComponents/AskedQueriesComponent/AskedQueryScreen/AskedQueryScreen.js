import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import AskedQueryScreenHeader from '../AskedQueryScreenHeader/AskedQueryScreenHeader';
import {useThemeStyles} from '../../../../../../Constants/useThemeStyles';
import {ScrollView} from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useSelector} from 'react-redux';
import {CHATBOT_SESSIONS, Request_Types} from '../../../../../../api/uri';
import {ApiCaller} from '../../../../../../middleWare/ApiCaller';
import {
  DayFilter,
  LastMonthFilter,
  LastWeekFilter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '../../../../../../Constants/DateFilter';
import moment from 'moment';
import {Fonts} from '../../../../../../styles/fonts';
import FastImage from 'react-native-fast-image';
import {useNavigation} from '@react-navigation/native';

const AskedQueryScreen = () => {
  const themeStyles = useThemeStyles();
  const [selectedFilter, setSelectedFilter] = useState('Today');
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [isLoading, setIsloading] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [siteId, setSiteId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [chatSessions, setChatSessions] = useState([]);
  const [openMenu, setOpenMenu] = useState(false);
  const navigation = useNavigation();
  const data = [
    {id: '1', title: 'Today', name: 'Today'},
    {id: '2', title: 'Yesterday', name: 'Yesterday'},
    {id: '3', title: 'This Week', name: 'This Week'},
    {id: '4', title: 'Last Week', name: 'Last Week'},
    {id: '5', title: 'This Month', name: 'This Month'},
    {id: '6', title: 'Last Month', name: 'Last Month'},
  ];

  const dateFilters = {
    Today: [DayFilter(), DayFilter()],
    Yesterday: [YesterdayFilter(), YesterdayFilter()],
    'This Week': [ThisWeekFilter(), DayFilter()],
    'Last Week': [
      LastWeekFilter().startOfLastWeek,
      LastWeekFilter().endOfLastWeek,
    ],
    'This Month': [
      ThisMonthFilter().startOfThisMonth,
      ThisMonthFilter().currentDate,
    ],
    'Last Month': [
      LastMonthFilter().startOfLastMonth,
      LastMonthFilter().endOfLastMonth,
    ],
  };

  // Update dates when filter changes
  useEffect(() => {
    const [start, end] = dateFilters[selectedFilter] || dateFilters['Today'];
    setStartDate(start);
    setEndDate(end);
    setErrorMessage(''); // Clear error when filter changes
  }, [selectedFilter]);

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (site) {
          setSiteId(site);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
        setErrorMessage('Failed to retrieve site information');
      }
    };

    fetchSiteId();
  }, [isSiteID]);

  const fetchChatSessions = useCallback(async () => {
    if (!startDate || !endDate) return;

    try {
      setIsloading(true);
      setErrorMessage('');
      const token = await AsyncStorage.getItem('Authorization');

      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${CHATBOT_SESSIONS(startDate, endDate)}`,
        headers: {Authorization: token},
      });

      if (response.data && Array.isArray(response.data)) {
        // Log each session separately for debugging

        setChatSessions(response.data);
      } else {
        setErrorMessage('Invalid response format from server');
      }
    } catch (error) {
      console.error('Thunder Bot Error:', error);

      if (error.message === 'Network Error') {
        setErrorMessage(
          'Network connection error. Please check your internet connection.',
        );
      } else if (error.response) {
        setErrorMessage(
          `Server error: ${error.response.status} ${error.response.statusText}`,
        );
      } else if (error.request) {
        setErrorMessage('No response from server. Please try again later.');
      } else {
        setErrorMessage(`Error: ${error.message}`);
      }
    } finally {
      setIsloading(false);
    }
  }, [startDate, endDate]);

  useEffect(() => {
    if (startDate && endDate) {
      fetchChatSessions();
    }

    const interval = setInterval(() => {
      if (startDate && endDate) {
        fetchChatSessions();
      }
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [fetchChatSessions, startDate, endDate]);

  const handleRetry = () => {
    if (startDate && endDate) {
      fetchChatSessions();
    }
  };
  const handleLongPress = item => {
    setOpenMenu(true);
    // Log any other properties you need
  };

  const renderChatItem = ({item}) => {
    const date = moment(item.createdat).format('MMM DD, YYYY • h:mm A');

    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => {
          // Navigate to ThunderChatBot with the session data
          navigation.navigate('LandingScreen', {
            screen: 'Thunder Chatbot',
            params: {
              sessionData: {
                prompt: item.session['1'].prompt,
                response: item.session['1'].response,
                alias: item.alias,
                date: date,
              },
            },
          });
        }}
        onLongPress={() => handleLongPress(item)}
        delayLongPress={500} // Adjust the delay as needed
      >
        <Text style={styles.chatQuery} numberOfLines={2}>
          {item.alias}
        </Text>
        <Text style={styles.chatDate}>{date}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      {/* Background Image - Positioned absolutely */}
      <View style={styles.backgroundImageContainer}>
        <FastImage
          style={styles.backgroundImage}
          source={require('../../../../../../assets/Images/chatbotgif.gif')}
          resizeMode={FastImage.resizeMode.contain}
        />
      </View>

      <AskedQueryScreenHeader />
      <View style={styles.scrollContainer}>
        <ScrollView
          style={{width: '100%', height: '100%'}}
          horizontal={true}
          showsHorizontalScrollIndicator={false}>
          {data.map(item => (
            <TouchableOpacity
              key={item.id}
              style={[
                styles.filterButton,
                selectedFilter === item.name && styles.selectedFilter,
              ]}
              onPress={() => setSelectedFilter(item.name)}>
              <Text
                style={[
                  styles.filterText,
                  selectedFilter === item.name && styles.selectedFilterText,
                ]}>
                {item.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF7F02" />
          <Text style={styles.loadingText}>Loading chat history...</Text>
        </View>
      ) : errorMessage ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{errorMessage}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.contentContainer}>
          {chatSessions.length > 0 ? (
            <FlatList
              data={chatSessions}
              renderItem={renderChatItem}
              keyExtractor={item => item.sessionid}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <Text style={styles.emptyText}>
                  No chat history found for this period
                </Text>
              }
            />
          ) : (
            <Text style={styles.emptyText}>
              No chat history found for this period
            </Text>
          )}
        </View>
      )}
    </View>
  );
};

export default AskedQueryScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  backgroundImageContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 0,
    left: '30%',
    right: 0,
    top: 0,
  },
  backgroundImage: {
    width: 392,
    height: 392,
    opacity: 0.15,
  },
  scrollContainer: {
    width: '96%',
    height: '6%',
    marginTop: '20%',

    zIndex: 1, // Higher zIndex to ensure it's above the background
  },
  filterButton: {
    paddingHorizontal: 16,
    marginHorizontal: 5,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    borderRadius: 6,
    backgroundColor: '#FFFFFF1A',
  },
  selectedFilter: {
    backgroundColor: '#FF7F02',
  },
  filterText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  selectedFilterText: {
    color: '#000',
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  loadingText: {
    marginTop: 10,
    color: '#96999E',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 1,
  },
  errorText: {
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#FF7F02',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
  },
  retryButtonText: {
    color: '#000',
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 16,
    marginTop: 20,
    zIndex: 1,
  },
  listContainer: {
    paddingBottom: 20,
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#FF7F02',
  },
  chatItem: {
    backgroundColor: '#FFFFFF1A',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  chatQuery: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  chatDate: {
    fontSize: 12,
    color: '#96999E',
  },
  emptyText: {
    textAlign: 'center',
    color: '#96999E',
    marginTop: 40,
  },
});
