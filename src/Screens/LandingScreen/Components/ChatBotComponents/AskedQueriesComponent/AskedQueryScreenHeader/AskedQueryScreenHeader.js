import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  BackHandler,
} from 'react-native';
import React, {useEffect} from 'react';
import LeftArrowSvg from '../../../../../../assets/svgs/LeftArrowSvg';
import SearchIconDark from '../../../../../../assets/svgs/SearchIconDark';
import {useThemeStyles} from '../../../../../../Constants/useThemeStyles';
import {useNavigation, CommonActions} from '@react-navigation/native';
import {Fonts} from '../../../../../../styles/fonts';

const AskedQueryScreenHeader = () => {
  const themeStyles = useThemeStyles();
  const navigation = useNavigation();

  // Simple navigation back to the previous screen
  const handleGoBack = () => {
    // Navigate to the screen with the drawer that contains Thunder Chatbot
    navigation.navigate('LandingScreen', {
      screen: 'Thunder Chatbot',
    });
  };

  // Add hardware back button handler
  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        handleGoBack();
        return true; // Prevent default behavior
      },
    );

    return () => backHandler.remove();
  }, []);

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <TouchableOpacity style={styles.drawerIcon} onPress={handleGoBack}>
        <LeftArrowSvg />
      </TouchableOpacity>
      <Text style={[styles.alarmsHeaderText, {color: themeStyles.textColor}]}>
        Asked Queries
      </Text>
      <TouchableOpacity style={styles.rightIconsContainer}>
        <SearchIconDark color={themeStyles.iconColor} style={styles.icon1} />
      </TouchableOpacity>
    </View>
  );
};

export default AskedQueryScreenHeader;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#0C121D1A',
    justifyContent: 'space-between',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  drawerIcon: {
    width: 50,
    height: 50,
    alignItems: 'flex-start',
    flex: 0.5,
    marginLeft: '5%',
    justifyContent: 'center',
  },
  alarmsHeaderText: {
    fontSize: 20,
    flex: 1.9,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  rightIconsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingRight: '7%',
    height: '100%',
  },
  icon1: {
    marginHorizontal: 1,
  },
});
