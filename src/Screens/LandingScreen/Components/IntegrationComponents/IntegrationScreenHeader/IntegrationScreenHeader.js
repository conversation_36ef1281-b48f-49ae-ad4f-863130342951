import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';

import SearchIconDark from '../../../../../assets/svgs/SearchIconDark';
import {Fonts} from '../../../../../styles/fonts';
import DropDown from '../../SearchScreen/DropDown';

const IntegrationScreenHeader = ({
  siteId,
  onSiteIdChange,
  showSearch,
  toggleSearch,
  searchQuery,
  setSearchQuery,

  parameterCount,

  showCrossIcon,
}) => {
  const themeStyles = useThemeStyles();

  return (
    <>
      <View
        style={[
          styles.siteContainer,
          // Apply left alignment when count is hidden
        ]}>
        <View
          style={[
            styles.siteIdContainer,
            // Apply full width when count is hidden
          ]}>
          <View style={styles.colorDot}></View>
          <TouchableOpacity>
            <DropDown
              onSiteIdChange={onSiteIdChange}
              isIndependent={false}
              currentSiteId={siteId}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.totalParametersContainer}>
          <Text style={[styles.parameterCount, {color: themeStyles.textColor}]}>
            {parameterCount || '0'}
          </Text>
          <Text style={styles.parameterType}>Total Integration</Text>
        </View>
      </View>

      <View
        style={[styles.searchFieldContainer, {backgroundColor: '#FFFFFF1A'}]}>
        <TextInput
          placeholder="Search"
          placeholderTextColor={themeStyles.dropDownTextColor}
          style={[
            styles.searchInput,
            {
              color: themeStyles.dropDownTextColor,
              width: showCrossIcon ? '83%' : '83%', // Adjust width based on cross icon presence
            },
          ]}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
        />

        <View style={styles.searchIconContainer}>
          <SearchIconDark color={themeStyles.iconColor} />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  siteContainer: {
    width: '96%',
    height: '8%',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: '15%',
    flexDirection: 'row',
    marginBottom: '-6%',
  },
  // New style for left-aligned container
  siteContainerLeft: {
    justifyContent: 'flex-start',
  },
  siteIdContainer: {
    height: '61%',
    width: '50%',
    flexDirection: 'row',
    alignContent: 'center',
    overflow: 'visible',
    marginLeft: '0%',
    alignItems: 'center',
  },
  // New style for full-width siteIdContainer
  siteIdContainerFull: {
    width: '100%',
  },
  colorDot: {
    height: 12,
    width: 12,
    backgroundColor: '#00EE5D',
    borderRadius: 40,
    elevation: 2,
    marginLeft: '0%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  totalParametersContainer: {
    height: '43%',
    width: '50%',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  parameterCount: {
    fontSize: 20,
    fontFamily: Fonts.BaiJamjuree_Medium,
    textAlign: 'right',
    width: 'auto',
  },
  parameterType: {
    color: '#96999E',
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_Medium,
    textAlign: 'right',
    marginTop: '-3%',
  },
  filterContainer: {
    width: '96%',
    height: '8%',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '3%',
    flexDirection: 'row',
    alignSelf: 'center',
  },
  searchFieldContainer: {
    width: '96%',
    height: '5.5%',
    alignItems: 'center',
    justifyContent: 'flex-start', // Changed to flex-start
    marginTop: '6%',
    flexDirection: 'row',
    alignSelf: 'center',
    borderRadius: 6,
    marginBottom: '2.2%',
    paddingHorizontal: 10, // Add padding for consistent spacing
  },
  searchInput: {
    fontSize: 15,
    marginLeft: '0%',
  },
  clearButton: {
    height: '100%',
    width: '10%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropDown: {
    width: '85%',
    height: '70%',
  },
  searchContainer: {
    height: 46,
    width: '13%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF1A',
    borderRadius: 6,
    marginRight: 0,
  },
  searchIconContainer: {
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 'auto', // Push to the right
  },
  calenderView: {
    height: '70%',
    width: '85%',
    backgroundColor: '#FFFFFF1A',
    borderRadius: 6,
    marginLeft: 0,
    alignItems: 'center',
    justifyContent: 'space-around',
    flexDirection: 'row',
  },
});

export default IntegrationScreenHeader;
