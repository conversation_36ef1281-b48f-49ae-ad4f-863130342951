import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  FlatList,
  useCallback,
  TouchableOpacity,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import Accordion from 'react-native-collapsible/Accordion';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DownArrowSvg from '../../../../../assets/svgs/DownArrowSvg';

import {Fonts} from '../../../../../styles/fonts';
import RightArrowSvg from '../../../../../assets/svgs/RightArrowSvg';
import {Switch} from '@ant-design/react-native';
import EditSvg from '../../../../../assets/svgs/EditSvg';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import {useNavigation} from '@react-navigation/native';

const IntegrationAccordion = ({
  siteLoad,
  alarmsType,
  timestamp,
  setEditDevice,
}) => {
  const themeStyles = useThemeStyles();
  const [activeSections, setActiveSections] = useState([]);
  const [switchStates, setSwitchStates] = useState({});
  const navigation = useNavigation();
  // Reset switch states whenever siteLoad changes
  useEffect(() => {
    if (siteLoad) {
      const newStates = {};
      siteLoad.forEach((item, index) => {
        newStates[index] = Boolean(item.active); // Changed from isactive to active
      });
      setSwitchStates(newStates);
    }
  }, [siteLoad]);

  const handleSwitchToggle = (index, value) => {
    setSwitchStates(prev => ({
      ...prev,
      [index]: value,
    }));
  };

  function capitalizeFirstLetter(string) {
    if (!string) return ''; // Return empty string if input is null/undefined
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  const _renderHeader = (section, index, isActive) => {
    return (
      <View
        style={[
          styles.header,
          {
            backgroundColor: isActive
              ? themeStyles.alarmsBackground
              : 'transparent',
          },
        ]}>
        <View style={styles.leftContent}>
          <View style={styles.arrowWrapper}>
            <View style={styles.arrowContainer}>
              {isActive ? (
                <DownArrowSvg color={'#FF7F02'} />
              ) : (
                <RightArrowSvg color={'#96999E'} />
              )}
            </View>
          </View>
          <Text
            style={[styles.key, {color: isActive ? '#FF7F02' : '#96999E'}]}
            numberOfLines={1}>
            {capitalizeFirstLetter(section?.devicetype || '')}
          </Text>
          <View style={styles.editContainer}>
            <Switch
              disabled={true}
              trackColor={{false: '#717171', true: '#FF7F02'}}
              thumbColor="#FFFFFF"
              checked={switchStates[index]}
              onChange={value => handleSwitchToggle(index, value)}
              style={styles.switch}
            />
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => {
                navigation.navigate('EditDeviceScreen', {
                  deviceData: {
                    ipAddress: section.ip,

                    vendor: section.hw_vendor_name,
                    version: section.csu_sw_version,

                    protocol: section.protocol,

                    communityString: section.protocol_data,
                    readCommunity: section.protocol_data,
                    writeCommunity: section.writecommunity,

                    macAddress: section.mac,
                    deviceType: section.devicetype,
                    bmsAvailable: section.device_modules,
                    onboardingStatus: section.onboarding_status,
                    trapstatus: section.trapstatus,

                    id: section.id,
                    vendor_profile_id: section.vendor_profile_id,
                    site_device_id: section.site_device_id,
                  },
                  refresh: true,
                });
              }}>
              <EditSvg />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };
  const formatDeviceModules = modules => {
    if (!modules) return '';
    return modules.split(',').join(', '); // Add space after comma for better readability
  };
  const _renderContent = section => {
    const displayValue = value => {
      if (value === null || value === undefined) return '';
      if (typeof value === 'boolean') return value.toString();
      if (typeof value === 'object') return JSON.stringify(value);
      return value.toString(); // Convert all values to string for display
    };

    return (
      <View style={styles.content}>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            IP Address
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {section.ip || '---'}
          </Text>
        </View>

        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Provided Read Community
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {
                color: themeStyles.textColor,
                marginLeft: '8%',
                width: '64%',
                textAlign: 'left',
              },
            ]}>
            {section.protocol_data || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Identified Read Community
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {section.identifiedreadcommunity || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Provided Write Community
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {capitalizeFirstLetter(section?.writecommunity || '---')}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Identified Write Community
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {capitalizeFirstLetter(section?.identifiedwritecommunity || '---')}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Device Modules
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {
                color: themeStyles.textColor,
                width: '50%',
                marginLeft: '8%',
              },
            ]}>
            {formatDeviceModules(section.device_modules) || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Provided MIB Version
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {section.csu_sw_version || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Identified MIB Version
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {section.actual_csu_sw_version || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Last Update Time
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {formatTimestamp(section.updated_on) || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Last Packet Time
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {formatTimestamp(section.lastpacket) || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Ping Date Time
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {formatTimestamp(section.ping_date_time) || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            SNMP Date Time
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {formatTimestamp(section.snmp_date_time) || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Onboarding Status
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {capitalizeFirstLetter(section.onboarding_status) || '---'}
          </Text>
        </View>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Trap Enabled
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, width: '64%', marginLeft: '8%'},
            ]}>
            {section.trapstatus ? 'Yes' : 'No' || '---'}
          </Text>
        </View>
      </View>
    );
  };

  const _updateSections = activeSections => {
    setActiveSections(activeSections);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <Accordion
          sections={siteLoad || []}
          activeSections={activeSections}
          renderHeader={_renderHeader}
          renderContent={_renderContent}
          onChange={_updateSections}
          underlayColor="transparent"
          sectionContainerStyle={styles.sectionContainer}
          style={{backgroundColor: themeStyles.alarmsBackground}}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '96%',
  },
  sectionContainer: {},
  scrollContainer: {
    width: '100%',
  },
  header: {
    width: '100%',
    minHeight: 56,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  leftContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
  },
  arrowWrapper: {
    width: 32,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  key: {
    flex: 1,
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    paddingHorizontal: 8,
  },
  editContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: 100,
    marginLeft: 8,
  },
  switch: {
    transform: [{scale: 0.8}],
    marginRight: 12,
  },
  editButton: {
    width: 34,
    height: 34,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 16,
    paddingTop: 8,
  },
  contentItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  contentItemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    width: '40%',
  },
});

export default IntegrationAccordion;
