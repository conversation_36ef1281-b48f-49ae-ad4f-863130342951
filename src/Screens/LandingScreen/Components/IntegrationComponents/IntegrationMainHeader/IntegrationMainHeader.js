import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DrawerIcon from '../../../../../assets/svgs/DrawerIcon';
import ReloadSvg from '../../../../../assets/svgs/ReloadSvg';
import {Fonts} from '../../../../../styles/fonts';
import AddSvg from '../../../../../assets/svgs/AddSvg';

const IntegrationMainHeader = () => {
  const isDark = false;

  const navigation = useNavigation();
  const themeStyles = useThemeStyles();

  const background = isDark ? '#FFFFFF1A' : '#0C121D1A';

  return (
    <View style={[styles.container, {backgroundColor: background}]}>
      <TouchableOpacity
        style={styles.drawerIcon}
        onPress={() => navigation.toggleDrawer()}>
        <DrawerIcon color={isDark ? '#0E121A' : '#FFFFFF'} />
      </TouchableOpacity>
      <Text style={[styles.alarmsHeaderText, {color: themeStyles.textColor}]}>
        Site Integration
      </Text>
      <TouchableOpacity
        style={styles.rightIconsContainer}
        onPress={() => {
          navigation.navigate('AddDeviceScreen');
        }}>
        <AddSvg color={themeStyles.iconColor} style={styles.icon1} />
      </TouchableOpacity>
    </View>
  );
};

export default IntegrationMainHeader;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#0C121D1A',
    justifyContent: 'space-between', // Use space-between for proper spacing
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  drawerIcon: {
    width: 50,
    height: 50,
    alignItems: 'flex-start',
    flex: 0.4,
    marginLeft: '5%',
    justifyContent: 'center',
  },
  alarmsHeaderText: {
    fontSize: 20,
    flex: 1.8,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  rightIconsContainer: {
    // Allow the container to take up remaining space
    justifyContent: 'center', // Align icons to the right
    alignItems: 'center',

    width: '9.6%',

    borderWidth: 1,
    borderColor: '#616161',
    height: '55%',
    marginRight: '3%',
    borderRadius: 6,
  },
  icon: {
    marginHorizontal: 35, // Add some horizontal margin between icons
  },
});
