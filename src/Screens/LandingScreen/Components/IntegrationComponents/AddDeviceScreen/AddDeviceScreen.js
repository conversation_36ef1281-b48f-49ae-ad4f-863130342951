import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Switch,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import EditScreenHeader from '../EditDeviceScreen/EditScreenHeader';
import EditFieldComponent from '../Common/EditFieldComponent';
import {useSelector} from 'react-redux';
import DropDownFieldComponent from '../Common/DropDownFieldComponent';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import {
  ADD_NEW_SITE_DEVICE,
  GET_INTEGRATION_DROPDOWNS,
  GET_SITE_DEVICE_INFORMATION,
  Request_Types,
  SEARCH_SITE_DEVICE,
  UPDATE_SITE_DEVICE_INFORMATION,
} from '../../../../../api/uri';
import {Fonts} from '../../../../../styles/fonts';
import VerticalSplitter from '../../../../../assets/svgs/VerticalSplitter';
import Splitter from '../../../../../assets/svgs/Splitter';
import AddSiteSplitterSvg from '../../../../../assets/svgs/AddSiteSplitterSvg';
import Toast from 'react-native-toast-message';
import {useNavigation} from '@react-navigation/native';

const AddDeviceScreen = () => {
  const themeStyles = useThemeStyles();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [siteId, setSiteId] = useState('');
  const [siteLoad, setSiteLoad] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [versions, setVersions] = useState([]);
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [siteIdChanging, setSiteIdChanging] = useState(false);
  const [initialLoad, setInitialLoad] = useState([]);
  const [formValues, setFormValues] = useState({
    ip: null,
    hw_vender_name: null,
    csu_sw_version: null,
    protocol: null,
    protocol_data: null,
    writecommunity: null,
    mac_address: null,
    devicetype: null,
    device_modules: null,
    vender_profile_id: null,
  });
  const [bmsEnabled, setBmsEnabled] = useState(false);
  const [isInitialDeviceTypeSelected, setIsInitialDeviceTypeSelected] =
    useState(false);
  const [initialDeviceType, setInitialDeviceType] = useState(null);
  const [isApplied, setIsApplied] = useState(false);
  const [defaultVersion, setDefaultVersion] = useState(null);
  // Add new state to track if form is valid
  const [isFormValid, setIsFormValid] = useState(true);
  const navigation = useNavigation();
  const handleTextChange = (field, value) => {
    setFormValues(prev => ({
      ...prev,
      [field]:
        !value && isApplied
          ? initialLoad[0]?.[getInitialLoadField(field)] || '' // Use initial load value if available
          : value === null
          ? '' // Convert only null to empty string
          : value, // Keep the value as is (including empty string)
    }));
  };

  const handleDropdownChange = (field, value) => {
    setFormValues(prev => ({
      ...prev,
      [field]:
        !value && isApplied
          ? initialLoad[0]?.[getInitialLoadField(field)] || '' // Use initial load value if available
          : value === null
          ? '' // Convert only null to empty string
          : value, // Keep the value as is (including empty string)
    }));
  };
  const fetchSiteDevices = useCallback(async id => {
    if (!id) return;

    try {
      setLoading(true);
      setSiteIdChanging(true);

      const token = await AsyncStorage.getItem('Authorization');
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_INTEGRATION_DROPDOWNS}`,
        headers: {Authorization: token},
      });

      setSiteLoad(response.data.data);
    } catch (error) {
      console.error('Site Device info Fetch Error:', error);
    } finally {
      setLoading(false);
      setSiteIdChanging(false);
    }
  }, []);
  useEffect(() => {
    if (isSiteID) {
      setSiteId(isSiteID);
      fetchSiteDevices(isSiteID);
    }
  }, [isSiteID, fetchSiteDevices]);
  const handleVendorChange = useCallback(
    value => {
      // Changed from item to value

      if (!value) {
        setSelectedVendor(null);
        setVersions([]);
        handleDropdownChange('hw_vender_name', '');
        handleDropdownChange('csu_sw_version', '');
        return;
      }

      setSelectedVendor(value);
      handleDropdownChange('hw_vender_name', value);

      if (siteLoad.vendors) {
        const vendorData = siteLoad.vendors.filter(
          vendor => vendor.vendors === value,
        );

        if (vendorData.length > 0) {
          const uniqueVersions = [
            ...new Set(vendorData.map(item => item.versions)),
          ].filter(Boolean);

          const formattedVersions = uniqueVersions.map(version => ({
            id: version,
            name: version,
            title: version,
            versions: version,
          }));

          setVersions(formattedVersions);
          setDefaultVersion(null);
          handleDropdownChange('csu_sw_version', '');

          // Update vendor profile ID
          if (vendorData[0]?.id) {
            handleDropdownChange('vender_profile_id', vendorData[0].id);
          }
        } else {
          setVersions([]);
          setDefaultVersion(null);
          handleDropdownChange('csu_sw_version', '');
          handleDropdownChange('vender_profile_id', null);
        }
      }
    },
    [siteLoad.vendors, handleDropdownChange],
  );
  // Remove or comment out the separate useEffect for versions since we're handling it in handleVendorChange
  // useEffect(() => {
  //   if (!siteLoad.vendors) return;
  //   ...
  // }, [selectedVendor, siteLoad.vendors]);
  const deviceModuleChangeHandler = isBmsEnabled => {
    let modules = '';
    if (formValues?.devicetype === 'hybrid') {
      modules = isBmsEnabled
        ? 'Default,DG,Battery,Solar,Wind,BMS'
        : 'Default,DG,Battery,Solar,Wind';
    } else if (formValues?.devicetype === 'main') {
      modules = isBmsEnabled
        ? 'Default,DG,Battery,Wind,BMS'
        : 'Default,DG,Battery,Wind';
    } else {
      modules = 'Default,Solar';
    }
    return modules;
  };

  const handleDeviceTypeChange = value => {
    handleDropdownChange('devicetype', value);
    setBmsEnabled(false);
    handleDropdownChange('device_modules', deviceModuleChangeHandler(false));

    // Update profile ID when device type changes
    if (formValues.hw_vender_name) {
      const vendorData = siteLoad.vendors.find(
        item => item.vendors === formValues.hw_vender_name,
      );
      handleDropdownChange('vender_profile_id', vendorData?.id || null);
    }
  };

  const handleBMSChange = value => {
    setBmsEnabled(value);
    handleDropdownChange('device_modules', deviceModuleChangeHandler(value));
  };
  useEffect(() => {
    if (formValues.hw_vender_name && !formValues.csu_sw_version) {
      setIsFormValid(false);
    } else {
      setIsFormValid(true);
    }
  }, [formValues.hw_vender_name, formValues.csu_sw_version]);
  const handleInitialDeviceTypeChange = value => {
    setInitialDeviceType(value || null);
    setIsInitialDeviceTypeSelected(!!value);
  };

  const handleApply = async () => {
    if (initialDeviceType) {
      setIsApplied(true);
      handleDropdownChange('devicetype', initialDeviceType);
      if (!isSiteID) return;

      try {
        setLoading(true);
        setSiteIdChanging(true);

        const token = await AsyncStorage.getItem('Authorization');
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${SEARCH_SITE_DEVICE(isSiteID, initialDeviceType)}`,
          headers: {Authorization: token},
        });

        const initialData = response.data.data;
        setInitialLoad(initialData);

        if (initialData && initialData[0]) {
          const initialVendor = initialData[0].hw_vendor_name;
          setFormValues({
            ...formValues,
            mac_address:
              initialData[0]?.mac_address === 'NULL'
                ? ''
                : initialData[0]?.mac_address, // Handle null specifically for MAC
            // ... other fields remain the same
          });
          // Set all initial form values
          setFormValues({
            ip: initialData[0]?.ip || '',
            hw_vender_name: initialData[0]?.hw_vendor_name || '',
            csu_sw_version: initialData[0]?.csu_sw_version || '',
            protocol: initialData[0]?.protocol || '',
            protocol_data: initialData[0]?.community || '',
            writecommunity: initialData[0]?.writecommunity || '',

            devicetype: initialDeviceType || '',
            device_modules: initialData[0]?.device_modules || '',
            vender_profile_id: initialData[0]?.vender_profile_id || '',
          });

          // Set the initial vendor
          setSelectedVendor(initialVendor);

          // Get versions for initial vendor
          if (siteLoad.vendors) {
            const vendorData = siteLoad.vendors.filter(
              vendor => vendor.vendors === initialVendor,
            );

            const uniqueVersions = [
              ...new Set(vendorData.map(item => item.versions)),
            ];
            const formattedVersions = uniqueVersions.map((version, index) => ({
              id: index.toString(),
              name: version,
              title: version,
              versions: version,
            }));

            setVersions(formattedVersions);

            // Set initial version if available
            if (initialData[0].csu_sw_version) {
              setDefaultVersion(initialData[0].csu_sw_version);
              handleDropdownChange(
                'csu_sw_version',
                initialData[0].csu_sw_version,
              );
            }
          }
        }
      } catch (error) {
        console.error('Site Device info Fetch Error:', error);
      } finally {
        setLoading(false);
        setSiteIdChanging(false);
      }
    }
  };
  // Add this function to check required fields
  const validateForm = () => {
    const requiredFields = {
      ip: 'IP Address',
      hw_vender_name: 'Vendor',
      csu_sw_version: 'Version',
      protocol: 'Protocol',
      protocol_data: 'Read Community String',
      writecommunity: 'Write Community String',
      devicetype: 'Device Type',
    };

    const emptyFields = Object.entries(requiredFields)
      .filter(([key, _]) => !formValues[key])
      .map(([_, label]) => label);

    if (emptyFields.length > 0) {
      Toast.show({
        type: 'error',
        text1: 'Required Fields Empty',
        text2: `Please fill in: ${emptyFields.join(', ')}`,
      });
      return false;
    }

    return true;
  };

  const updateSiteDevice = async () => {
    if (!isSiteID) return;

    // Check form validation before proceeding
    if (!validateForm()) {
      return;
    }

    try {
      const token = await AsyncStorage.getItem('Authorization');

      // Filter out null values if needed
      const formDataToSubmit = Object.fromEntries(
        Object.entries(formValues).filter(([_, value]) => value !== null),
      );
      if (initialLoad.length > 0) {
        const response = await ApiCaller({
          method: Request_Types.POST,
          url: `${UPDATE_SITE_DEVICE_INFORMATION(
            isSiteID,
            initialLoad[0].site_device_id,
          )}`,
          headers: {Authorization: token},
          data: formValues, // or use formDataToSubmit if you want to exclude null values
        });
        if (response.data.success) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: response.data.message,
          });
          navigation.goBack();
        }
        return;
      } else {
        const response = await ApiCaller({
          method: Request_Types.POST,
          url: `${ADD_NEW_SITE_DEVICE(isSiteID)}`,
          headers: {Authorization: token},
          data: formValues, // or use formDataToSubmit if you want to exclude null values
        });
        if (response.data.success) {
          Toast.show({
            type: 'success',
            text1: 'Success',
            text2: response.data.message,
          });
          navigation.goBack();
        }
      }
    } catch (error) {
      console.error('Update Site Device info Fetch Error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.response?.data?.message || 'An error occurred',
      });
    }
  };
  const handleBack = () => {
    navigation.goBack();
  };

  // Add a debug effect to track state changes

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <EditScreenHeader headerText={'Add New Device'} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.content}
        contentContainerStyle={styles.scrollContent}>
        <View style={{height: '68%'}}>
          <EditFieldComponent
            text={'Site ID *'}
            placeholder={isSiteID}
            editable={false}
            color={'#FFFFFF'}
            placeholderTextColor={'#FFFFFF'}
          />
          <DropDownFieldComponent
            autocomplete={false}
            text={'Device Type *'}
            color={'#FFFFFF'}
            placeholder={'Select Device Type'}
            data={siteLoad.deviceTypes}
            onChangeText={handleInitialDeviceTypeChange}
            disabled={isApplied}
          />

          <TouchableOpacity
            style={[
              styles.backButton,
              {
                borderWidth: 1,
                borderColor: '#404346',
                backgroundColor: !isApplied ? '#FF7F02' : '#242A34',
              },
            ]}
            onPress={handleApply}
            disabled={isApplied}
            activeOpacity={0.7}>
            <Text
              style={[
                styles.buttonText,
                {color: !isApplied ? '#0E121A' : '#50555F'},
              ]}>
              Apply
            </Text>
          </TouchableOpacity>
          <AddSiteSplitterSvg style={{marginTop: '6%', marginBottom: '2%'}} />
          <EditFieldComponent
            text={'IP Address *'}
            color={'#50555F'}
            placeholder={'Select IP Address'}
            onChangeText={value => handleTextChange('ip', value)}
            editable={isApplied}
            placeholderTextColor={'#50555F'}
            defaultValue={initialLoad[0]?.ip || ''}
          />
          <DropDownFieldComponent
            autocomplete={false}
            text={'Vendor *'}
            color={'#50555F'}
            placeholder={'Select Vendor'}
            data={siteLoad.vendors}
            onChangeText={handleVendorChange}
            disabled={!isApplied}
            defaultValue={initialLoad[0]?.hw_vendor_name || ''}
            labelField="vendors" // Add this to specify which field to display
            valueField="vendors" // Add this to specify which field to use as value
          />
          <DropDownFieldComponent
            autocomplete={false}
            text={'Version *'}
            color={'#50555F'}
            placeholder={'Select Version'}
            data={versions}
            onChangeText={value => {
              handleDropdownChange('csu_sw_version', value);
            }}
            disabled={!isApplied}
            defaultValue={defaultVersion || initialLoad[0]?.csu_sw_version}
            value={formValues.csu_sw_version || defaultVersion}
          />
          <DropDownFieldComponent
            autocomplete={false}
            text={'Protocol *'}
            color={'#50555F'}
            placeholder={'Select Protocol'}
            data={siteLoad.protocols}
            onChangeText={value => handleDropdownChange('protocol', value)}
            disabled={!isApplied}
            defaultValue={initialLoad[0]?.protocol || ''}
          />

          <DropDownFieldComponent
            autocomplete={true}
            text={'Read Community String*'}
            color={'#50555F'}
            data={siteLoad.community}
            placeholder={'Enter Read Community String'}
            onChangeText={value => handleDropdownChange('protocol_data', value)}
            editable={isApplied}
            placeholderTextColor={'#50555F'}
            defaultValue={initialLoad[0]?.community || ''}
          />
          <DropDownFieldComponent
            autocomplete={true}
            text={'Write Community String *'}
            color={'#50555F'}
            data={siteLoad.community}
            placeholder={'Enter Write Community String'}
            onChangeText={value =>
              handleDropdownChange('writecommunity', value)
            }
            editable={isApplied}
            placeholderTextColor={'#50555F'}
            defaultValue={initialLoad[0]?.writecommunity || ''}
          />
          <EditFieldComponent
            text={'Mac Address *'}
            color={'#50555F'}
            placeholder={'Enter Mac Address'}
            onChangeText={value => handleTextChange('mac_address', value)}
            editable={isApplied}
            placeholderTextColor={'#50555F'}
            defaultValue={initialLoad[0]?.mac_address || ''} // For default value of mac_address field
          />

          <DropDownFieldComponent
            autocomplete={false}
            text={'Device Type *'}
            color={'#50555F'}
            placeholder={'Select Device Type'}
            data={siteLoad.deviceTypes}
            onChangeText={handleDeviceTypeChange}
            disabled={!isApplied}
            defaultValue={initialLoad[0]?.devicetype || ''}
          />
        </View>
      </ScrollView>
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.button,
            styles.cancelButton,
            !isApplied && styles.disabledButton,
          ]}
          onPress={handleBack}
          disabled={!isApplied}
          activeOpacity={0.7}>
          <Text
            style={[
              styles.cancelButtonText,
              !isApplied && styles.disabledButtonText,
            ]}>
            Cancel
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            styles.updateButton,
            (!isFormValid || !isApplied) && styles.disabledButton,
          ]}
          onPress={updateSiteDevice}
          disabled={!isFormValid || !isApplied}>
          <Text
            style={[
              styles.updateButtonText,
              (!isFormValid || !isApplied) && styles.disabledButtonText,
            ]}>
            Update
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AddDeviceScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  content: {
    marginTop: '15%',
    width: '96%',
    backgroundColor: '#0C121D1A',

    maxHeight: '83%',
  },
  scrollContent: {
    minHeight: '90%',
    paddingBottom: '43%',
  },
  backButton: {
    width: '100%',
    borderRadius: 10,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF7F02',
    marginTop: '4%',
  },
  buttonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#0E121A',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingVertical: 10,
    marginTop: 10,
  },
  switchLabel: {
    fontSize: 14,
    color: '#50555F',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    width: '100%',
  },
  button: {
    height: 48,
    flex: 0.48,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: '#404346',
  },
  updateButton: {
    backgroundColor: '#FF7F02',
  },
  disabledButton: {
    backgroundColor: '#242A34',
  },
  cancelButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },
  updateButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#0E121A',
  },
  disabledButtonText: {
    color: '#50555F',
  },
  disabledText: {
    color: '#50555F',
  },
});

// Helper function to map form fields to initialLoad fields
const getInitialLoadField = field => {
  const fieldMapping = {
    ip: 'ip',
    hw_vender_name: 'hw_vendor_name',
    csu_sw_version: 'csu_sw_version',
    protocol: 'protocol',
    protocol_data: 'protocol_data',
    writecommunity: 'writecommunity',
    mac_address: 'mac_address',
    devicetype: 'devicetype',
    device_modules: 'device_modules',
    vender_profile_id: 'vender_profile_id',
  };
  return fieldMapping[field] || field;
};
