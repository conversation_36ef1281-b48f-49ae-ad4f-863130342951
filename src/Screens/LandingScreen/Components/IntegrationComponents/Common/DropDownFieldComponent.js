import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import DropDownFilter from './DropDownFilter';
import {Fonts} from '../../../../../styles/fonts';

import AutocompleteDropdownField from './AutocompleteDropdownField';

const DropDownFieldComponent = ({
  text,
  data,
  defaultValue,
  setSelectedItem,
  selectedVendor,
  isVersionDropdown,
  onChangeText,
  color,
  placeholder,
  disabled,
  autocomplete,
}) => {
  return (
    <View style={styles.container}>
      <Text style={[styles.headingText, {color: color}]}>{text}</Text>
      {autocomplete ? (
        <AutocompleteDropdownField
          data={data}
          defaultValue={defaultValue}
          setSelectedItem={setSelectedItem}
          selectedVendor={selectedVendor}
          isVersionDropdown={isVersionDropdown}
          onChangeText={onChangeText}
          placeholder={placeholder}
          disabled={disabled}
        />
      ) : (
        <DropDownFilter
          data={data}
          defaultValue={defaultValue}
          setSelectedItem={setSelectedItem}
          selectedVendor={selectedVendor}
          isVersionDropdown={isVersionDropdown}
          onChangeText={onChangeText}
          placeholder={placeholder}
          disabled={disabled}
        />
      )}
    </View>
  );
};

export default DropDownFieldComponent;

const styles = StyleSheet.create({
  container: {
    width: '100%',

    alignItems: 'flex-start',
    height: '15%',
    marginTop: '2%',
  },
  headingText: {
    fontSize: 15,
    color: '#50555F',
    fontFamily: Fonts.BaiJamjuree_Medium,

    textAlign: 'left', // Ensure left alignment
    alignSelf: 'flex-start', // Ensure left alignment
  },
});
