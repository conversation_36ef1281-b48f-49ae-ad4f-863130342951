import {StyleSheet, Text, View} from 'react-native';
import React, {useCallback, useState, useEffect, useMemo} from 'react';
import {AutocompleteDropdown} from 'react-native-autocomplete-dropdown';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {Fonts} from '../../../../../styles/fonts';

const AutocompleteDropdownField = ({
  setDate,
  isLandscape,
  modalHeight = 300,
  setSelectedItem,
  selectedItem,
  data,
  defaultValue,
  selectedVendor,
  isVersionDropdown,
  onChangeText,
  placeholder,
  disabled,
}) => {
  const themeStyles = useThemeStyles();
  const [inputValue, setInputValue] = useState(defaultValue || '');

  // Format data consistently
  const formattedData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }

    return data.map((item, index) => ({
      id: index.toString(),
      title: typeof item === 'string' ? item : item?.name || item?.title || '',
    }));
  }, [data]);

  // Update input value when defaultValue changes
  useEffect(() => {
    setInputValue(defaultValue || '');
  }, [defaultValue]);

  // Find the initial value
  const initialValue = useMemo(() => {
    if (!defaultValue || !formattedData.length) {
      return undefined;
    }

    const found = formattedData.find(
      item => item && item.title === defaultValue,
    );
    return found || undefined;
  }, [formattedData, defaultValue]);

  const handleChange = useCallback(
    item => {
      if (!item) {
        setInputValue('');
        if (setSelectedItem) setSelectedItem('');
        if (onChangeText) onChangeText('');
        return;
      }

      setInputValue(item.title || '');
      if (setSelectedItem) setSelectedItem(item.title || '');
      if (onChangeText) onChangeText(item.title || '');
    },
    [setSelectedItem, onChangeText],
  );

  return (
    <View style={styles.container}>
      <AutocompleteDropdown
        clearOnFocus={false}
        closeOnBlur={true}
        closeOnSubmit={false}
        initialValue={initialValue}
        onSelectItem={handleChange}
        dataSet={formattedData}
        suggestionsListMaxHeight={150}
        textInputProps={{
          placeholder: placeholder,
          placeholderTextColor: '#50555F',
          style: [styles.inputStyle, {color: themeStyles.textColor}],
          value: inputValue,
          onChangeText: setInputValue,
        }}
        inputContainerStyle={styles.inputContainerStyle}
        containerStyle={styles.dropdownContainerStyle}
        suggestionsListContainerStyle={styles.suggestionsListStyle}
        renderItem={item =>
          item ? (
            <Text style={[styles.itemText, {color: themeStyles.textColor}]}>
              {item.title || ''}
            </Text>
          ) : null
        }
        showChevron={true}
        onClear={() => handleChange(null)}
        useFilter={true}
        emptyResultText="No results found"
        loading={false}
        disabled={disabled}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 46,
    zIndex: 1,
  },
  inputContainerStyle: {
    backgroundColor: '#FFFFFF1A',
    borderRadius: 6,

    height: 50,
  },
  inputStyle: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    paddingHorizontal: 12,
    height: '100%',
  },
  dropdownContainerStyle: {
    flexGrow: 1,
    flexShrink: 1,
    height: 46,
  },
  suggestionsListStyle: {
    backgroundColor: '#1E1E1E',
    borderRadius: 6,
    marginTop: 5,
    maxHeight: 150,
    borderWidth: 1,
    borderColor: '#50555E',
  },
  itemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    padding: 12,
    color: '#FFFFFF',
  },
});

export default AutocompleteDropdownField;
