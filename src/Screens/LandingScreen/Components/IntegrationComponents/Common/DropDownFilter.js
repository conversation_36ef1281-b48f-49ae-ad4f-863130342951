import {StyleSheet, Text, View} from 'react-native';
import React, {useCallback} from 'react';
import {Dropdown} from 'react-native-element-dropdown';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {Fonts} from '../../../../../styles/fonts';

const DropDownFilter = ({
  setDate,
  isLandscape,
  modalHeight,
  setSelectedItem,
  selectedItem,
  data,
  defaultValue,
  selectedVendor,
  isVersionDropdown,
  onChangeText,
  placeholder,
  disabled,
}) => {
  const themeStyles = useThemeStyles();

  // First, define formattedData
  const formattedData = React.useMemo(() => {
    // Return empty array if data is null, undefined, or not an array
    if (!data || !Array.isArray(data)) return [];

    if (isVersionDropdown) {
      return data.map((item, index) => ({
        name: item.versions,
        id: index.toString(),
        title: item.versions,
      }));
    } else {
      if (
        data.length > 0 &&
        (typeof data[0] === 'string' || typeof data[0] === 'number')
      ) {
        return data.map((item, index) => ({
          name: item,
          id: index.toString(),
          title: item,
        }));
      }

      const uniqueMap = new Map();

      data.forEach((item, index) => {
        if (!item) return; // Skip null or undefined items

        if (item.id && item.name && item.title) {
          if (!uniqueMap.has(item.name)) {
            uniqueMap.set(item.name, {
              name: item.name,
              id: item.id,
              title: item.title,
            });
          }
        } else if (item.vendors && Array.isArray(item.vendors.versions)) {
          item.vendors.versions.forEach((version, vIndex) => {
            if (!uniqueMap.has(version)) {
              uniqueMap.set(version, {
                name: version,
                id: `${index}-${vIndex}`,
                title: version,
              });
            }
          });
        } else {
          const value = item.vendors || item.value || item;
          if (value && !uniqueMap.has(value)) {
            uniqueMap.set(value, {
              name: value,
              id: index.toString(),
              title: value,
            });
          }
        }
      });
      return Array.from(uniqueMap.values());
    }
  }, [data, isVersionDropdown]);

  const findDefaultItem = React.useMemo(() => {
    if (!defaultValue || !formattedData || formattedData.length === 0)
      return null;

    const foundItem = formattedData.find(
      item => item && item.name && item.name === defaultValue,
    );

    return foundItem ? foundItem.id : null;
  }, [defaultValue, formattedData]);

  const handleChange = useCallback(
    item => {
      if (setSelectedItem) {
        setSelectedItem(item.name);
      }
      if (onChangeText) {
        onChangeText(item.name);
      }
    },
    [setSelectedItem, onChangeText],
  );

  return (
    <View style={[styles.dayFilter]}>
      <Dropdown
        style={[
          styles.dropdown,
          {
            backgroundColor: '#FFFFFF1A',
            borderColor: '#50555E',
          },
        ]}
        placeholderStyle={[styles.placeholderStyle, {color: '#50555F'}]}
        selectedTextStyle={[
          styles.selectedTextStyle,
          {color: themeStyles.textColor},
        ]}
        data={formattedData || []}
        maxHeight={modalHeight ? modalHeight * 0.25 : 300}
        labelField="name"
        valueField="id"
        value={findDefaultItem}
        placeholder={placeholder || 'Select an option'}
        onChange={item => item && handleChange(item)}
        containerStyle={[
          styles.dropdownContainer,
          {backgroundColor: themeStyles.powerSourceChart},
        ]}
        itemContainerStyle={styles.itemContainer}
        activeColor="#FF7F02"
        itemTextStyle={[styles.itemText, {color: themeStyles.textColor}]}
        disable={disabled}
        renderItem={item =>
          item && (
            <Text style={[styles.itemText, {color: themeStyles.textColor}]}>
              {item.name}
            </Text>
          )
        }
      />
    </View>
  );
};

export default React.memo(DropDownFilter);

const styles = StyleSheet.create({
  dayFilter: {
    height: '60%',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdown: {
    height: '100%',
    width: '100%',
    borderRadius: 6,
    borderWidth: 0,
    paddingHorizontal: 8,
  },
  placeholderStyle: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    textAlign: 'left',
  },
  selectedTextStyle: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    textAlign: 'left',
  },
  itemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    padding: 10,
    textAlign: 'left',
  },
  dropdownContainer: {
    borderRadius: 6,
    marginTop: 5,
    borderWidth: 1,
  },
  itemContainer: {
    height: 47,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
});
