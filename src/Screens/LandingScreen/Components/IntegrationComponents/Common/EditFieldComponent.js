import {StyleSheet, Text, TextInput, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import DropDownFilter from './DropDownFilter';
import {Fonts} from '../../../../../styles/fonts';

const EditFieldComponent = ({
  text,
  defaultValue,
  placeholder,
  onChangeText,
  editable,
  color,
  placeholderTextColor,
}) => {
  const [defaultValueText, setDefaultValueText] = useState('');
  useEffect(() => {
    if (
      defaultValue === 'NULL' ||
      defaultValue === undefined ||
      defaultValue === null
    ) {
      setDefaultValueText('');
    } else {
      setDefaultValueText(defaultValue);
    }
  }, [defaultValue]);

  return (
    <View style={styles.container}>
      <Text style={[styles.headingText, {color: color}]}>{text}</Text>
      <TextInput
        style={styles.input}
        defaultValue={defaultValueText}
        placeholder={placeholder}
        editable={editable}
        onChangeText={onChangeText}
        placeholderTextColor={placeholderTextColor}></TextInput>
    </View>
  );
};

export default EditFieldComponent;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'flex-start',
    height: '15%',
    marginTop: '1%',
  },
  headingText: {
    fontSize: 15,
    color: '#50555F',
    fontFamily: Fonts.BaiJamjuree_Medium,

    textAlign: 'left',
    alignSelf: 'flex-start',
  },
  input: {
    height: '55%',

    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '1%',
    backgroundColor: '#FFFFFF1A',
    borderColor: '#50555E',
    borderRadius: 6,
    paddingHorizontal: 10,
    color: '#FFFFFF',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
});
