import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import LeftArrowSvg from '../../../../../assets/svgs/LeftArrowSvg';
import {Fonts} from '../../../../../styles/fonts';

const EditScreenHeader = ({headerText}) => {
  const dispatch = useDispatch();
  const [isDark, setIsDark] = useState(false);
  const istheme = useSelector(state => state.theme.theme);
  const navigation = useNavigation();
  const themeStyles = useThemeStyles();

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');
        if (storedTheme !== null) {
          const parsedTheme = JSON.parse(storedTheme);
          setIsDark(parsedTheme);
        } else {
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching/parsing theme:', error);
        setIsDark(istheme === 'dark');
      }
    };
    fetchTheme();
  }, [istheme]);

  const background = isDark ? '#FFFFFF1A' : '#0C121D1A';

  return (
    <View style={[styles.container, {backgroundColor: background}]}>
      <TouchableOpacity
        style={styles.drawerIcon}
        onPress={() => navigation.goBack()}>
        <LeftArrowSvg color={isDark ? '#0E121A' : '#FFFFFF'} />
      </TouchableOpacity>
      <Text style={[styles.alarmsHeaderText, {color: themeStyles.textColor}]}>
        {headerText}
      </Text>
    </View>
  );
};

export default EditScreenHeader;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#0C121D1A',
    justifyContent: 'space-between', // Use space-between for proper spacing
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  drawerIcon: {
    width: 50,
    height: 50,
    alignItems: 'flex-start',
    flex: 0.3,
    marginLeft: '5%',
    justifyContent: 'center',
  },
  alarmsHeaderText: {
    fontSize: 20,
    flex: 1.9,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
});
