import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import EditScreenHeader from './EditScreenHeader';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DropDownFieldComponent from '../Common/DropDownFieldComponent';
import EditFieldComponent from '../Common/EditFieldComponent';
import TickSvg from '../../../../../assets/svgs/TickSvg';
import {Fonts} from '../../../../../styles/fonts';
import {useRoute, useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import {
  GET_INTEGRATION_DROPDOWNS,
  Request_Types,
  UPDATE_SITE_DEVICE_INFORMATION,
} from '../../../../../api/uri';
import Toast from 'react-native-toast-message';

const windowHeight = Dimensions.get('window').height;

const EditDeviceScreen = () => {
  const themeStyles = useThemeStyles();
  const route = useRoute();
  const navigation = useNavigation();
  const deviceData = route.params?.deviceData || {};
  const isSiteID = useSelector(state => state.siteId.siteId);

  const [load, setLoad] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState();
  const [isApplied, setIsApplied] = useState();
  // Initialize checked state based on device_modules
  const [checked, setChecked] = useState(() => {
    if (!deviceData.bmsAvailable) return false;
    if (deviceData.bmsAvailable.includes('BMS')) {
      return true;
    } else return false;
  });

  const [versions, setVersions] = useState([]);
  const [formValues, setFormValues] = useState({
    ipAddress: deviceData.ipAddress || '',
    vendor: deviceData.vendor || '',
    version: deviceData.version || '',
    protocol: deviceData.protocol || '',
    protocol_data: deviceData.readCommunity || '', // Changed field name
    writecommunity: deviceData.writeCommunity || '', // Changed field name
    macAddress: deviceData.macAddress || '',
    deviceType: deviceData.deviceType || '',
    onBoardingStatus: deviceData.onboardingStatus,
  });

  // Add new state to track if form is valid
  const [isFormValid, setIsFormValid] = useState(true);

  const handleTextChange = (field, value) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDropdownChange = (field, value) => {
    setFormValues(prev => {
      const newValues = {
        ...prev,
        [field]: value,
      };

      return newValues;
    });
  };
  const fetchDropDowns = useCallback(async () => {
    if (!isSiteID) return;

    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('Authorization');

      const response = await ApiCaller({
        method: Request_Types.GET,
        url: GET_INTEGRATION_DROPDOWNS,
        headers: {Authorization: token},
      });

      setLoad(response.data.data);
    } catch (error) {
      console.error('Site Device info Fetch Error:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDropDowns();
  }, [fetchDropDowns]);
  useEffect(() => {
    if (!load || !load.vendors) return;

    if (selectedVendor) {
      const vendorData = load.vendors.filter(
        item => item && item.vendors === selectedVendor,
      );

      // Extract unique versions
      const uniqueVersions = [
        ...new Set(vendorData.map(item => item?.versions).filter(Boolean)),
      ];

      // Format versions for dropdown
      const formattedVersions = uniqueVersions.map(version => ({
        id: version,
        name: version,
        title: version,
        versions: version,
      }));

      setVersions(formattedVersions);
    } else if (deviceData && deviceData.vendor) {
      const vendorData = load.vendors.filter(
        item => item && item.vendors === deviceData.vendor,
      );

      // Extract unique versions
      const uniqueVersions = [
        ...new Set(vendorData.map(item => item?.versions).filter(Boolean)),
      ];

      // Format versions for dropdown
      const formattedVersions = uniqueVersions.map(version => ({
        id: version,
        name: version,
        title: version,
        versions: version,
      }));

      setVersions(formattedVersions);
    } else {
      setVersions([]);
    }
  }, [selectedVendor, load.vendors, deviceData]);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleVendorChange = selectedItem => {
    if (!selectedItem) return; // Add null check

    setSelectedVendor(selectedItem);
    setFormValues(prev => ({
      ...prev,
      vendor: selectedItem,
      version: '', // Reset version when vendor changes
    }));

    // Immediately update versions when vendor changes
    if (load && load.vendors) {
      const vendorData = load.vendors.filter(
        item => item && item.vendors === selectedItem,
      );

      const uniqueVersions = [
        ...new Set(vendorData.map(item => item?.versions).filter(Boolean)),
      ];

      const formattedVersions = uniqueVersions.map(version => ({
        id: version,
        name: version,
        title: version,
        versions: version,
      }));

      setVersions(formattedVersions);
    }
  };
  const deviceModuleChangeHandler = checked => {
    if (formValues?.deviceType === 'hybrid') {
      return checked
        ? 'Default,DG,Battery,Solar,Wind,BMS'
        : 'Default,DG,Battery,Solar,Wind';
    } else if (formValues?.deviceType === 'main') {
      return checked
        ? 'Default,DG,Battery,Wind,BMS'
        : 'Default,DG,Battery,Wind';
    } else {
      return 'Default,Solar';
    }
  };

  const updateSiteDevice = async () => {
    if (!isSiteID) return;
    try {
      const profileId = load.vendors.filter(
        item => item.vendors === formValues.vendor,
      );

      const token = await AsyncStorage.getItem('Authorization');
      const requestData = {
        csu_sw_version: formValues.version,
        device_modules: deviceModuleChangeHandler(checked),
        devicetype: formValues.deviceType,
        hw_vender_name: formValues.vendor,
        ip: formValues.ipAddress,
        mac_address: formValues.macAddress,
        protocol: formValues.protocol,
        protocol_data: formValues.protocol_data, // Match the state field name
        writecommunity: formValues.writecommunity, // Match the state field name
        vender_profile_id: profileId[0].id,
      };

      const response = await ApiCaller({
        method: Request_Types.POST,
        url: `${UPDATE_SITE_DEVICE_INFORMATION(
          isSiteID,
          deviceData.site_device_id,
        )}`,
        headers: {Authorization: token},
        data: requestData,
      });

      if (response.data.success) {
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: response.data.message,
        });
        navigation.goBack();
      }
    } catch (error) {
      console.error('Update Site Device info Fetch Error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.response?.data?.message || 'An error occurred',
      });
    }
  };

  // Add a function to check if value exists in dropdown data
  const isValueInDropdownData = (value, dropdownData) => {
    if (!value || !dropdownData) return false;

    const isValid = dropdownData.some(item => {
      if (!item) return false; // Add check for null item

      const matches =
        item.name === value ||
        item.title === value ||
        item.id === value ||
        item === value; // Add direct value comparison

      return matches;
    });

    return isValid;
  };

  // Update validation effect with logging
  useEffect(() => {
    // Only run validation if load.community exists
    if (!load || !load.community) return;

    const isReadCommunityValid =
      Boolean(formValues.protocol_data) &&
      isValueInDropdownData(formValues.protocol_data, load.community);

    const isWriteCommunityValid =
      Boolean(formValues.writecommunity) &&
      isValueInDropdownData(formValues.writecommunity, load.community);

    const isVendorVersionValid =
      !formValues.vendor || (formValues.vendor && formValues.version);

    const newIsFormValid =
      isReadCommunityValid && isWriteCommunityValid && isVendorVersionValid;

    setIsFormValid(newIsFormValid);
  }, [
    formValues.vendor,
    formValues.version,
    formValues.protocol_data,
    formValues.writecommunity,
    load,
  ]);

  // Update validateForm function
  const validateForm = () => {
    const errors = [];

    if (!isValueInDropdownData(formValues.protocol_data, load.community)) {
      errors.push(
        'Please select a valid Read Community String from the dropdown',
      );
    }

    if (!isValueInDropdownData(formValues.writecommunity, load.community)) {
      errors.push(
        'Please select a valid Write Community String from the dropdown',
      );
    }

    if (formValues.vendor && !formValues.version) {
      errors.push('Version is required when vendor is selected');
    }

    if (errors.length > 0) {
      Toast.show({
        type: 'error',
        text1: 'Validation Error',
        text2: errors.join(', '),
      });
      return false;
    }

    return true;
  };

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <EditScreenHeader headerText={'Add New Device'} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.content}
        contentContainerStyle={styles.scrollContent}>
        <EditFieldComponent
          text={'IP Address *'}
          color={'#50555F'}
          placeholder={'Select IP Address'}
          onChangeText={value => handleTextChange('ip', value)}
          placeholderTextColor={'#50555F'}
          defaultValue={deviceData.ipAddress}
        />
        <DropDownFieldComponent
          autocomplete={false}
          text={'Vendor *'}
          color={'#50555F'}
          placeholder={'Select Vendor'}
          data={load.vendors}
          onChangeText={handleVendorChange}
          defaultValue={deviceData.vendor}
        />
        <DropDownFieldComponent
          autocomplete={false}
          text={'Version *'}
          color={'#50555F'}
          placeholder={'Select Version'}
          data={versions}
          onChangeText={value => {
            handleDropdownChange('version', value);
          }}
          defaultValue={deviceData.version}
        />
        <DropDownFieldComponent
          autocomplete={false}
          text={'Protocol *'}
          color={'#50555F'}
          placeholder={'Select Protocol'}
          data={load.protocols}
          onChangeText={value => handleDropdownChange('protocol', value)}
          defaultValue={deviceData.protocol}
        />

        <DropDownFieldComponent
          autocomplete={false} // Change to false to prevent manual input
          text={'Read Community String*'}
          color={'#50555F'}
          data={load.community}
          placeholder={'Enter Read Community String'}
          onChangeText={value => {
            handleDropdownChange('protocol_data', value);
          }}
          placeholderTextColor={'#50555F'}
          defaultValue={deviceData.readCommunity}
          value={formValues.protocol_data}
          editable={false} // Add this to prevent manual editing
        />
        <DropDownFieldComponent
          autocomplete={false} // Change to false to prevent manual input
          text={'Write Community String *'}
          color={'#50555F'}
          data={load.community}
          placeholder={'Enter Write Community String'}
          onChangeText={value => {
            handleDropdownChange('writecommunity', value);
          }}
          placeholderTextColor={'#50555F'}
          defaultValue={deviceData.writeCommunity}
          value={formValues.writecommunity}
          editable={false} // Add this to prevent manual editing
        />
        <EditFieldComponent
          text={'Mac Address *'}
          color={'#50555F'}
          placeholder={'Enter Mac Address'}
          onChangeText={value => handleTextChange('mac_address', value)}
          placeholderTextColor={'#50555F'}
          defaultValue={deviceData.macAddress}
        />

        <DropDownFieldComponent
          autocomplete={false}
          text={'Device Type *'}
          color={'#50555F'}
          placeholder={'Select Device Type'}
          data={load.deviceTypes}
          onChangeText={value => handleTextChange('deviceType', value)}
          defaultValue={deviceData.deviceType}
        />
      </ScrollView>

      <View style={styles.selectCheckBoxContainer}>
        <TouchableOpacity
          activeOpacity={0.7}
          style={[
            styles.checkBox,
            {
              borderColor: checked
                ? themeStyles.isDark
                  ? '#96999E'
                  : '#0E121ACC'
                : themeStyles.isDark
                ? '#0E121ACC'
                : '#96999E',
            },
            checked && styles.checked,
          ]}
          onPress={() => setChecked(prev => !prev)}>
          {checked && <TickSvg />}
        </TouchableOpacity>
        <Text style={styles.selectAllText}>BMS Available</Text>
      </View>
      <View style={styles.statusContainer}>
        <View style={styles.status}>
          <Text style={styles.statusText}>Onboarding</Text>
          <Text
            style={[
              styles.statusText,
              {color: deviceData.trapstatus ? '#00EE5D' : 'red'},
            ]}>
            {deviceData.onboardingStatus}
          </Text>
        </View>
        <View style={styles.status}>
          <Text style={styles.statusText}>Data Polling</Text>
          <Text
            style={[
              styles.statusText,
              {
                textAlign: 'left',
                width: '44%',
                marginLeft: '2%',
                color: deviceData.trapstatus ? '#00EE5D' : 'red',
              },
            ]}>
            {deviceData.trapstatus ? 'Yes' : 'No'}
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.cancelButton]}
          onPress={handleBack}
          activeOpacity={0.7}>
          <Text style={[styles.cancelButtonText]}>Cancel</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            styles.updateButton,
            !isFormValid && styles.disabledButton,
          ]}
          onPress={updateSiteDevice}
          disabled={!isFormValid}>
          <Text
            style={[
              styles.updateButtonText,
              !isFormValid && styles.disabledButtonText,
            ]}>
            Update
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default EditDeviceScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  content: {
    marginTop: '15%',
    width: '96%',
    backgroundColor: '#0C121D1A',

    maxHeight: '70%',
  },
  scrollContent: {
    minHeight: '90%',
    paddingBottom: '46%',
  },
  backButton: {
    width: '100%',
    borderRadius: 10,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF7F02',
    marginTop: '4%',
  },
  buttonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#0E121A',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingVertical: 10,
    marginTop: 10,
  },
  switchLabel: {
    fontSize: 14,
    color: '#50555F',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    width: '100%',
  },
  button: {
    height: 48,
    flex: 0.48,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: '#404346',
  },
  updateButton: {
    backgroundColor: '#FF7F02',
  },
  disabledButton: {
    backgroundColor: '#242A34',
  },
  cancelButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },
  updateButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#0E121A',
  },
  disabledButtonText: {
    color: '#50555F',
  },
  disabledText: {
    color: '#50555F',
  },
  bmsContainer: {
    height: '13%',
    width: '96%',
    backgroundColor: 'white',
  },
  checkBox: {
    height: 18,
    width: 18,
    borderWidth: 1,
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: '3%',
  },
  checked: {
    backgroundColor: '#FF7F02',
    borderColor: '#FF7F02',
  },
  selectCheckBoxContainer: {
    width: '96%',
    height: '5%',
    alignItems: 'center',
    justifyContent: 'flex-start',

    flexDirection: 'row',

    marginTop: '3%',
    marginBottom: '3%',
  },
  selectAllText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
    marginLeft: '3%',
  },
  statusContainer: {
    height: '10%',
    width: '96%',
    backgroundColor: '#252A344D',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  status: {
    flexDirection: 'row',
    width: '51%',
    justifyContent: 'space-between',
    alignItems: 'center',

    alignSelf: 'flex-start',

    marginLeft: '3%',
  },
  statusText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },
});
