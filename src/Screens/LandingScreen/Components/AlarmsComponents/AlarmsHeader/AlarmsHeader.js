import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DrawerIcon from '../../../../../assets/svgs/DrawerIcon';
import {Fonts} from '../../../../../styles/fonts';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';

import FilterSvg from '../../../../../assets/svgs/FilterSvg';

const AlarmsHeader = () => {
  const navigation = useNavigation();
  const themeStyles = useThemeStyles();

  const background = '#0C121D1A';

  return (
    <View style={[styles.container, {backgroundColor: background}]}>
      <TouchableOpacity
        style={styles.drawerIcon}
        onPress={() => navigation.toggleDrawer()}>
        <DrawerIcon color={'#FFFFFF'} />
      </TouchableOpacity>
      <Text style={[styles.alarmsHeaderText, {color: themeStyles.textColor}]}>
        Alarms
      </Text>
      <View style={styles.rightIconsContainer}>
        <FilterSvg color={themeStyles.iconColor} style={styles.icon1} />
      </View>
    </View>
  );
};

export default React.memo(AlarmsHeader);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#0C121D1A',
    justifyContent: 'space-between', // Use space-between for proper spacing
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  drawerIcon: {
    width: 50,
    height: 50,
    alignItems: 'flex-start',
    flex: 0.5,
    marginLeft: '5%',
    justifyContent: 'center',
  },
  alarmsHeaderText: {
    fontSize: 20,
    flex: 1.9,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  rightIconsContainer: {
    flexDirection: 'row',
    flex: 1, // Allow the container to take up remaining space
    justifyContent: 'flex-end', // Align icons to the right
    alignItems: 'center',
    paddingRight: '5%',
  },
  icon: {
    marginHorizontal: 35, // Add some horizontal margin between icons
  },
  icon1: {
    marginHorizontal: 1, // Add some horizontal margin between icons
  },
});
