import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {GET_SITE_ALARMS, Request_Types} from '../../../../api/uri';
import GreenDotSvg from '../../../../assets/svgs/GreenDotSvg';
import Accordion from 'react-native-collapsible/Accordion';
import {AlarmsColorMaping} from '../../../../Constants/AlarmsColorMaping';
import {Fonts} from '../../../../styles/fonts';
import DownArrowSvg from '../../../../assets/svgs/DownArrowSvg';
import RightArrowSvg from '../../../../assets/svgs/RightArrowSvg';
import VerticalAlarmsLineSvg from '../../../../assets/svgs/VerticalAlarmsLineSvg';
import {alarmCategory} from '../../../../Constants/AlarmsNameMaping';

const AlarmsComponent = ({siteLoad, alarmsType}) => {
  // Make sure alarmsType is received

  const themeStyles = useThemeStyles();
  const [headingActiveSections, setHeadingActiveSections] = useState({
    'Recent Alarms': {},
    'Last 30 Mins': {},
    Yesterday: {},
    'Last 7 days': {},
    'Last Month': {},
    'Last 6 Months': {},
    'Last Year': {},
    // Removed 'Older than Last Year' as we'll handle it differently
  });

  // New function to group alarms by month and year for older alarms
  const groupAlarmsByMonthYear = alarms => {
    const grouped = {};
    const now = new Date();
    const lastYear = now.getFullYear() - 1;

    alarms.forEach(item => {
      const occurTime = new Date(item.occurTime);
      if (occurTime.getFullYear() >= lastYear) return; // Skip if not older than last year

      const monthYearKey = `${occurTime.toLocaleString('default', {
        month: 'long',
      })} ${occurTime.getFullYear()}`;

      if (!grouped[monthYearKey]) {
        grouped[monthYearKey] = [];
      }
      grouped[monthYearKey].push(item);
    });

    return grouped;
  };

  const getFilteredAlarms = (filter, usedAlarms) => {
    if (!siteLoad) {
      return []; // Return an empty array if siteLoad is undefined
    }

    let filteredSiteLoad = siteLoad.filter(item => {
      // First filter by alarmsType if it exists
      if (alarmsType && item.level !== alarmsType) {
        return false;
      }

      const occurTime = new Date(item.occurTime);
      const now = new Date();

      switch (filter) {
        case 'Last 30 Mins':
          return occurTime >= new Date(now.getTime() - 30 * 60 * 1000);

        case 'Yesterday':
          const yesterday = new Date();
          yesterday.setDate(now.getDate() - 1);
          return (
            occurTime.getFullYear() === yesterday.getFullYear() &&
            occurTime.getMonth() === yesterday.getMonth() &&
            occurTime.getDate() === yesterday.getDate()
          );

        case 'Last 7 days':
          return occurTime >= new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        case 'Last Month':
          return (
            occurTime.getFullYear() === now.getFullYear() &&
            occurTime.getMonth() === now.getMonth() - 1
          );

        case 'Last 6 Months':
          return (
            occurTime >=
            new Date(now.getFullYear(), now.getMonth() - 6, now.getDate())
          );

        case 'Last Year':
          return occurTime.getFullYear() === now.getFullYear() - 1;

        // Removed 'Older than Last Year' case as we'll handle it separately

        default: // Recent Alarms (today)
          return (
            occurTime.getFullYear() === now.getFullYear() &&
            occurTime.getMonth() === now.getMonth() &&
            occurTime.getDate() === now.getDate()
          );
      }
    });

    // Group alarms and remove duplicates
    const sectionsData = Object.values(
      filteredSiteLoad.reduce((acc, item) => {
        const uniqueKey = `${item.fullname}-${item.occurTime}`;
        if (!acc[uniqueKey]) {
          acc[uniqueKey] = {
            title: item.fullname,
            content: [],
            level: item.level,
            key: uniqueKey,
            occurTime: item.occurTime,
          };
        }
        const cleanedName = item.name.replace(/alarm/gi, '').trim();
        acc[uniqueKey].content.push({
          name: cleanedName,
          level: item.level,
          occurTime: item.occurTime,
        });
        return acc;
      }, {}),
    );

    const filteredSections = sectionsData.filter(
      section => !usedAlarms.has(section.key),
    );

    filteredSections.forEach(section => usedAlarms.add(section.key));

    return filteredSections.sort(
      (a, b) => new Date(b.occurTime) - new Date(a.occurTime),
    );
  };

  const _renderHeader = (section, index, isActive) => (
    <View
      style={[
        styles.header,
        {
          backgroundColor: isActive
            ? themeStyles.alarmsBackground
            : 'transparent',
          borderTopLeftRadius: 4,
          borderTopRightRadius: 4,
        },
      ]}>
      {isActive ? (
        <DownArrowSvg color={themeStyles.arrowIconColor} />
      ) : (
        <RightArrowSvg color={themeStyles.arrowIconColor} />
      )}
      <GreenDotSvg
        color={AlarmsColorMaping[section.level]}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          marginLeft: '4%',
        }}
      />
      <Text style={[styles.headerText, {color: themeStyles.dropDownTextColor}]}>
        {section.title}
      </Text>
    </View>
  );

  const _renderContent = section => (
    <View style={styles.content}>
      <FlatList
        data={section.content}
        nestedScrollEnabled={true}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item}) => (
          <View style={styles.contentItem}>
            <View style={styles.contentItemContainer}>
              <Text
                style={[
                  styles.contentItemText,
                  {color: '#96999E', width: 'auto'},
                ]}>
                Type
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: AlarmsColorMaping[item.level],
                    marginLeft: '25%',
                    width: 'auto',
                  },
                ]}>
                {alarmCategory(item.level)}
              </Text>
            </View>
            <View style={styles.contentItemContainer}>
              <Text
                style={[
                  styles.contentItemText,
                  {color: '#96999E', width: 'auto', flexWrap: 'wrap'},
                ]}>
                Category
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    marginLeft: '15%',
                    width: '64%',
                  },
                ]}
                numberOfLines={0}>
                {item.name}
              </Text>
            </View>
            <View style={[styles.contentItemContainer, {marginBottom: '3%'}]}>
              <Text style={[styles.contentItemText, {color: '#96999E'}]}>
                Occur Time
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    marginLeft: '10%',
                    width: 'auto',
                  },
                ]}>
                {item.occurTime}
              </Text>
            </View>
          </View>
        )}
      />
    </View>
  );

  const toggleSection = (heading, index) => {
    setHeadingActiveSections(prevState => {
      const currentState = prevState[heading] || {};
      const isCurrentlyActive = currentState[index];

      return {
        [heading]: {
          [index]: !isCurrentlyActive,
        },
      };
    });
  };

  const renderItem = (heading, {item, index}) => {
    const isActive = headingActiveSections[heading]?.[index] ?? false;

    return (
      <TouchableOpacity onPress={() => toggleSection(heading, index)}>
        <Accordion
          sections={[item]}
          activeSections={isActive ? [0] : []}
          renderHeader={_renderHeader}
          renderContent={_renderContent}
          onChange={() => toggleSection(heading, index)}
          touchableProps={{activeOpacity: 1}}
          style={{backgroundColor: themeStyles.alarmsBackground}}
          underlayColor={'transparent'}
        />
      </TouchableOpacity>
    );
  };

  const renderHeadingAndList = (heading, filteredAlarms) => {
    if (filteredAlarms.length === 0) return null; // Don't render empty headings

    return (
      <View style={{marginBottom: 20, width: '100%'}}>
        <View
          style={{
            flexDirection: 'row',
            width: '100%',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text
            style={[
              styles.headingText,
              {
                color: themeStyles.timeFilterColor,
                fontSize: 15,
                fontFamily: Fonts.BaiJamjuree_SemiBold,
                marginTop: '2%',
              },
            ]}>
            {heading}
          </Text>
          <VerticalAlarmsLineSvg color={themeStyles.splitterColor} />
        </View>
        <FlatList
          data={filteredAlarms}
          keyExtractor={item => item.key}
          renderItem={props => renderItem(heading, props)} // Pass the heading
          nestedScrollEnabled={true}
        />
      </View>
    );
  };

  const usedAlarms = new Set();

  // Get alarms older than last year
  const olderThanLastYearAlarms =
    siteLoad?.filter(item => {
      const occurTime = new Date(item.occurTime);
      const now = new Date();
      return occurTime < new Date(now.getFullYear() - 1, 0, 1);
    }) || [];

  // Group them by month and year
  const groupedOlderAlarms = groupAlarmsByMonthYear(olderThanLastYearAlarms);

  return (
    <ScrollView style={{height: '70%', width: '92%'}}>
      {renderHeadingAndList('Recent Alarms', getFilteredAlarms('', usedAlarms))}
      {renderHeadingAndList(
        'Last 30 Mins',
        getFilteredAlarms('Last 30 Mins', usedAlarms),
      )}
      {renderHeadingAndList(
        'Yesterday',
        getFilteredAlarms('Yesterday', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last 7 days',
        getFilteredAlarms('Last 7 days', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last Month',
        getFilteredAlarms('Last Month', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last 6 Months',
        getFilteredAlarms('Last 6 Months', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last Year',
        getFilteredAlarms('Last Year', usedAlarms),
      )}

      {/* Render grouped older alarms */}
      {Object.entries(groupedOlderAlarms)
        .sort(([aKey], [bKey]) => {
          // Sort by year and then month (newest first)
          const [aMonth, aYear] = aKey.split(' ');
          const [bMonth, bYear] = bKey.split(' ');
          if (aYear !== bYear) return bYear - aYear;
          return new Date(`1 ${bKey}`) - new Date(`1 ${aKey}`);
        })
        .map(([monthYear, alarms]) => {
          // Process these alarms into sections like getFilteredAlarms does
          const sectionsData = Object.values(
            alarms.reduce((acc, item) => {
              const uniqueKey = `${item.fullname}-${item.occurTime}`;
              if (!acc[uniqueKey]) {
                acc[uniqueKey] = {
                  title: item.fullname,
                  content: [],
                  level: item.level,
                  key: uniqueKey,
                  occurTime: item.occurTime,
                };
              }
              const cleanedName = item.name.replace(/alarm/gi, '').trim();
              acc[uniqueKey].content.push({
                name: cleanedName,
                level: item.level,
                occurTime: item.occurTime,
              });
              return acc;
            }, {}),
          );

          const filteredSections = sectionsData.filter(
            section => !usedAlarms.has(section.key),
          );

          filteredSections.forEach(section => usedAlarms.add(section.key));

          const sortedSections = filteredSections.sort(
            (a, b) => new Date(b.occurTime) - new Date(a.occurTime),
          );

          return renderHeadingAndList(monthYear, sortedSections);
        })}
    </ScrollView>
  );
};

export default AlarmsComponent;

const styles = StyleSheet.create({
  content: {padding: 2},
  contentItem: {paddingVertical: 2},
  contentItemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    height: 'auto',
  },
  contentItemContainer: {
    flexDirection: 'row',
    marginTop: '3%',
    marginLeft: '3%',
  },
  header: {
    padding: 10,
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },
  headingText: {
    fontSize: 18,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    marginBottom: 10,
  },
});
