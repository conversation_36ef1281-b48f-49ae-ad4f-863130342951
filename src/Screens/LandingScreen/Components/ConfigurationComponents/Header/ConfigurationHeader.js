import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DrawerIcon from '../../../../../assets/svgs/DrawerIcon';
import ReloadSvg from '../../../../../assets/svgs/ReloadSvg';
import {Fonts} from '../../../../../styles/fonts';
import ConfigurationSvg from '../../../../../assets/svgs/ConfigurationSvg';
import LeftArrowSvg from '../../../../../assets/svgs/LeftArrowSvg';

const ConfigurationHeader = ({arrowIcon, onRefresh}) => {
  const isDark = false;

  const navigation = useNavigation();
  const themeStyles = useThemeStyles();

  const background = isDark ? '#FFFFFF1A' : '#0C121D1A';
  const handleRefreshPress = () => {
    if (onRefresh) {
      onRefresh();
    }
  };
  return (
    <View style={[styles.container, {backgroundColor: background}]}>
      <TouchableOpacity
        style={styles.drawerIcon}
        onPress={() => {
          arrowIcon ? navigation.toggleDrawer() : navigation.goBack();
        }}>
        {arrowIcon ? (
          <DrawerIcon color={isDark ? '#0E121A' : '#FFFFFF'} />
        ) : (
          <LeftArrowSvg />
        )}
      </TouchableOpacity>
      <Text style={[styles.alarmsHeaderText, {color: themeStyles.textColor}]}>
        Configurations
      </Text>

      {arrowIcon ? (
        <TouchableOpacity
          style={styles.rightIconsContainer}
          onPress={() => {
            navigation.navigate('ConfigLogs');
          }}>
          <ConfigurationSvg
            color={themeStyles.iconColor}
            style={styles.icon1}
          />
        </TouchableOpacity>
      ) : (
        <>
          <TouchableOpacity
            style={styles.rightIconsContainer}
            onPress={handleRefreshPress}>
            <ReloadSvg color={themeStyles.iconColor} style={styles.icon1} />
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

export default ConfigurationHeader;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: '#0C121D1A',
    justifyContent: 'space-between', // Use space-between for proper spacing
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  drawerIcon: {
    width: 50,
    height: 50,
    alignItems: 'flex-start',
    flex: 0.5,
    marginLeft: '5%',
    justifyContent: 'center',
  },
  alarmsHeaderText: {
    fontSize: 20,
    flex: 1.9,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  rightIconsContainer: {
    flexDirection: 'row',
    flex: 1, // Allow the container to take up remaining space
    justifyContent: 'flex-end', // Align icons to the right
    alignItems: 'center',
    paddingRight: '7%',
    height: '100%',
  },
  icon: {
    marginHorizontal: 35, // Add some horizontal margin between icons
  },
  icon1: {
    marginHorizontal: 1, // Add some horizontal margin between icons
  },
});
