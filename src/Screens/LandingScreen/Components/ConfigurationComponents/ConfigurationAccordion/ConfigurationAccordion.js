import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import React, {useState, useCallback, useEffect, useMemo} from 'react';
import Accordion from 'react-native-collapsible/Accordion';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DownArrowSvg from '../../../../../assets/svgs/DownArrowSvg';
import RightArrowSvg from '../../../../../assets/svgs/RightArrowSvg';
import {Fonts} from '../../../../../styles/fonts';
import EditSvg from '../../../../../assets/svgs/EditSvg';
import SaveSvg from '../../../../../assets/svgs/SaveSvg';
import DenySvg from '../../../../../assets/svgs/DenySvg';
import {
  EDIT_CONFIGURATIONS,
  GET_CONFIGURATIONS,
  Request_Types,
} from '../../../../../api/uri';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import {useDispatch, useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GetsiteID} from '../../../../../redux/Slices/SiteIdSlice';
import {determineValueType} from '../../../../../Constants/DetermineValueType';
import {Dropdown} from 'react-native-element-dropdown';

const ConfigurationAccordion = ({
  siteLoad,
  timestamp,
  selectedItem,
  category,
  isUser,
  onValueModification,
  resetTrigger,
  setResetTrigger,
  setUpdatedValuesJson,
}) => {
  const dispatch = useDispatch();
  const themeStyles = useThemeStyles();
  const [siteId, setSiteId] = useState('');
  const [activeSections, setActiveSections] = useState([]);
  const [editingKey, setEditingKey] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [savedValues, setSavedValues] = useState({});
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [configurations, setConfigurations] = useState([]);
  const [mappingtype, setMappingtype] = useState();
  const [isFieldLoading, setIsFieldLoading] = useState(false);
  const [valuemapping, setValueMapping] = useState([]);
  const [keyboard, setKeyboard] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const displayValue = value => {
    if (value === null || value === undefined) return '';
    if (typeof value === 'boolean') return value.toString();
    if (typeof value === 'object') return JSON.stringify(value);
    return value.toString();
  };
  useEffect(() => {
    if (resetTrigger) {
      setSavedValues({});
      setEditingKey(null);
      setEditValue('');
      setActiveSections([]);
      setResetTrigger(false); // Reset the trigger
    }
  }, [resetTrigger, setResetTrigger]);
  useEffect(() => {}, [isSiteID]);
  const editConfigurations = useCallback(
    async (id, key) => {
      if (!id) return;
      if (isUser) {
        setSelectedCategory(category);
      } else {
        setSelectedCategory(selectedItem.title);
      }
      setIsFieldLoading(true);
      try {
        const token = await AsyncStorage.getItem('Authorization');
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${EDIT_CONFIGURATIONS(id, key, selectedCategory)}`,
          headers: {Authorization: token},
        });

        const data = response?.data?.data[0];

        setMappingtype(data?.mappingtype || null);
        setValueMapping(
          typeof data?.valuemapping === 'string'
            ? JSON.parse(data.valuemapping)
            : data?.valuemapping || [],
        );
        if (data?.datatype === 'INTEGER') {
          setKeyboard('numeric');
        } else {
          setKeyboard('name');
        }

        setConfigurations(data || {});
      } catch (error) {
        console.error('Site Configurations Fetch Error:', error);
        setMappingtype(null);
        setValueMapping([]);
        setConfigurations({});
      } finally {
        setIsFieldLoading(false);
      }
    },
    [selectedItem, category],
  );
  useEffect(() => {
    if (Object.keys(savedValues).length > 0) {
      const formattedJson = {
        data: Object.entries(savedValues).map(([key, value]) => ({
          parameters: key,
          sitecode: isSiteID,
          userinputvalue: value,
        })),
      };
      setUpdatedValuesJson(formattedJson);
    } else {
      setUpdatedValuesJson(null);
    }
  }, [savedValues, siteId]);
  useEffect(() => {}, [siteId, editConfigurations]);
  const handleEditClick = useCallback(
    async (key, value) => {
      if (
        activeSections.length > 0 &&
        siteLoad[activeSections[0]].key === key
      ) {
        setEditingKey(key);
        const valueToEdit =
          savedValues[key] !== undefined ? savedValues[key] : value;
        setEditValue(displayValue(valueToEdit));

        if (isSiteID) {
          const encoded = encodeURIComponent(key)
            .replace(/%20/g, '+')
            .replace(/\(/g, '%28')
            .replace(/\)/g, '%29');
          await editConfigurations(isSiteID, encoded);
        }
      }
    },
    [activeSections, siteLoad, savedValues, isSiteID],
  );

  // Add effect to handle value modifications
  useEffect(() => {
    const hasModifiedValues = Object.keys(savedValues).length > 0;
    onValueModification(hasModifiedValues);
  }, [savedValues, onValueModification]);

  const handleSaveClick = useCallback(
    key => {
      setEditingKey(null);
      setSavedValues(prev => ({
        ...prev,
        [key]: editValue,
      }));
    },
    [editValue],
  );

  const handleDenyClick = useCallback(key => {
    setEditingKey(null);
    setEditValue('');
    setSavedValues(prev => {
      const newValues = {...prev};
      delete newValues[key];
      return newValues;
    });
  }, []);

  const _renderHeader = (section, _, isActive) => {
    return (
      <View
        style={[
          styles.header,
          {
            backgroundColor: isActive
              ? themeStyles.alarmsBackground
              : 'transparent',
          },
        ]}>
        <View style={styles.leftContent}>
          {isActive ? (
            <DownArrowSvg color={'#FF7F02'} style={{marginTop: '2%'}} />
          ) : (
            <RightArrowSvg color={'#96999E'} style={{marginTop: '1%'}} />
          )}
          <Text
            style={[
              styles.key,
              {
                color: isActive ? '#FF7F02' : '#96999E',
                flex: 1, // Allow text to take remaining space
              },
            ]}
            numberOfLines={2} // Allow up to 2 lines
          >
            {section.key}
          </Text>
        </View>
      </View>
    );
  };

  const isValueInRange = (value, mapping, datatype) => {
    // If mappingtype is CATEGORIES, always return true
    if (mappingtype === 'CATEGORIES') return true;

    if (mapping === null || mapping === undefined) return true;

    // If mapping is not an array, return true
    if (!Array.isArray(mapping)) return true;

    // For INTEGER type, validate against range
    if (configurations?.datatype === 'INTEGER') {
      try {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) return false;

        const isInRange = mapping.some(range => {
          if (Array.isArray(range) && range.length === 2) {
            const [min, max] = range;
            return numValue >= min && numValue <= max;
          }
          return false;
        });

        return isInRange;
      } catch (error) {
        console.error('Error checking range:', error);
        return false;
      }
    }

    return true;
  };

  const dropdownOptions = useMemo(() => {
    if (!valuemapping || !Array.isArray(valuemapping)) return [];

    return valuemapping.map(item => ({
      label: item.toString(),
      value: item.toString(),
    }));
  }, [valuemapping]);

  const renderEditableField = (section, isEditing, currentValue) => {
    if (isFieldLoading) {
      return (
        <View style={[styles.contentItemText, styles.loadingContainer]}>
          <ActivityIndicator color={themeStyles.textColor} />
        </View>
      );
    }

    const parsedValueMapping =
      typeof valuemapping === 'string'
        ? JSON.parse(valuemapping)
        : valuemapping;

    const isValidValue =
      mappingtype !== 'CATEGORIES'
        ? isValueInRange(editValue, parsedValueMapping)
        : true;

    // For CATEGORIES type, show dropdown
    if (mappingtype === 'CATEGORIES' && isEditing) {
      return (
        <Dropdown
          style={[
            styles.dropdown,
            {
              backgroundColor: '#FFFFFF1A',
              borderColor: '#50555E',
            },
          ]}
          placeholderStyle={[
            styles.placeholderStyle,
            {color: themeStyles.textColor},
          ]}
          selectedTextStyle={[
            styles.selectedTextStyle,
            {color: themeStyles.textColor},
          ]}
          inputSearchStyle={styles.inputSearchStyle}
          iconStyle={styles.iconStyle}
          data={dropdownOptions}
          maxHeight={300}
          labelField="label"
          valueField="value"
          value={editValue || currentValue}
          itemContainerStyle={styles.dropdownContainer}
          placeholder="Select value"
          onChange={item => {
            setEditValue(item.value);
          }}
          containerStyle={[styles.dropdownContainer]}
          renderSelectedItem={item => (
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.selectedDropdownItem}>
              {item.label}
            </Text>
          )}
        />
      );
    }

    // For numeric input, show numeric keyboard
    const isModified = savedValues[section.key] !== undefined;

    return (
      <TextInput
        value={isEditing ? editValue : displayValue(currentValue)}
        onChangeText={setEditValue}
        editable={isEditing}
        keyboardType={
          configurations?.datatype === 'INTEGER' ? 'numeric' : 'default'
        }
        style={[
          styles.contentItemText,
          {
            color: themeStyles.textColor,
            marginLeft: '20%',
            width: '30%',
            borderRadius: 6,
            borderWidth: 0.5,
            borderColor: isModified
              ? '#FF0000'
              : isEditing
              ? isValidValue
                ? '#FF7F02'
                : '#FF0000'
              : '#50555E',
            height: '90%',
            paddingLeft: 6,
            backgroundColor: '#0C121D',
          },
        ]}
        placeholderTextColor={themeStyles.dropDownTextColor}
      />
    );
  };

  const formatRangeText = mapping => {
    if (!mapping || !Array.isArray(mapping)) return null;

    try {
      const ranges = mapping
        .map(range => {
          if (Array.isArray(range) && range.length === 2) {
            const [min, max] = range;
            return `${min}-${max}`;
          }
          return null;
        })
        .filter(Boolean);

      return ranges.length > 0 ? ranges[0] : null;
    } catch (error) {
      console.error('Error formatting range:', error);
      return null;
    }
  };

  const _renderContent = section => {
    const isActive =
      activeSections.length > 0 &&
      siteLoad[activeSections[0]].key === section.key;
    const isEditing = editingKey === section.key && isActive;
    const currentValue =
      savedValues[section.key] !== undefined
        ? savedValues[section.key]
        : section.value;

    const parsedValueMapping =
      typeof valuemapping === 'string'
        ? JSON.parse(valuemapping)
        : valuemapping;

    // Modified validation logic to handle all types
    const isValidValue = isValueInRange(
      editValue,
      parsedValueMapping,
      configurations?.datatype,
    );

    return (
      <View style={styles.content}>
        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Value
          </Text>

          {renderEditableField(section, isEditing, currentValue)}

          {!isEditing ? (
            <TouchableOpacity
              style={[styles.editButton, {marginLeft: '3%'}]}
              onPress={() => handleEditClick(section.key, currentValue)}>
              <EditSvg />
            </TouchableOpacity>
          ) : (
            <>
              <TouchableOpacity
                style={[
                  styles.editButton,
                  {marginLeft: '3%'},
                  !isValidValue && styles.disabledButton,
                ]}
                disabled={!isValidValue}
                onPress={() => handleSaveClick(section.key)}>
                <SaveSvg color={isValidValue ? '#FF7F02' : '#666666'} />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.editButton, {marginLeft: '3%'}]}
                onPress={() => handleDenyClick(section.key)}>
                <DenySvg />
              </TouchableOpacity>
            </>
          )}
        </View>
        {isEditing &&
          mappingtype !== 'CATEGORIES' &&
          !isFieldLoading &&
          (rangeText = formatRangeText(parsedValueMapping)) && (
            <Text
              style={{
                height: '17%',
                width: '50%',
                marginLeft: '33%',
                textAlign: 'left',
                fontSize: 12,
                fontStyle: 'italic',
                fontFamily: Fonts.BaiJamjuree_Medium,
                color: '#FFC400',
              }}>
              * Input range: {rangeText}
            </Text>
          )}

        <View style={styles.contentItemContainer}>
          <Text style={[styles.contentItemText, {color: '#96999E'}]}>
            Time Stamp
          </Text>
          <Text
            style={[
              styles.contentItemText,
              {color: themeStyles.textColor, marginLeft: '6%', width: '64%'},
            ]}>
            {timestamp}
          </Text>
        </View>
      </View>
    );
  };

  const _updateSections = useCallback(activeSections => {
    setActiveSections(activeSections);
    setEditingKey(null);
    setEditValue('');
  }, []);

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        <Accordion
          sections={siteLoad || []}
          activeSections={activeSections}
          renderHeader={_renderHeader}
          renderContent={_renderContent}
          onChange={_updateSections}
          underlayColor="transparent"
          sectionContainerStyle={styles.sectionContainer}
          style={{backgroundColor: themeStyles.alarmsBackground}}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  sectionContainer: {},
  scrollContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '2%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10, // Increased padding
    borderRadius: 4,
    width: '100%',
    minHeight: 50,
    // Add minimum height to accommodate 2 lines
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'flex-start', // Changed from 'center' to 'flex-start'
    flex: 1,
    marginLeft: '2%',

    paddingRight: 10, // Add right padding
  },
  key: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    marginLeft: '4.6%',
    lineHeight: 20, // Add line height for better text spacing
  },
  content: {
    padding: 12,
    paddingTop: 12, // Added top padding
    marginLeft: '2%',
    marginTop: '-3%', // Add margin to separate from header
  },
  contentItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '2%',
  },
  contentItemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  editButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdown: {
    marginLeft: '20%',
    width: '30%',
    height: '85%',
    borderRadius: 6,
    borderWidth: 0.5,
    paddingHorizontal: 8,
  },
  placeholderStyle: {
    fontSize: 14,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  selectedTextStyle: {
    fontSize: 14,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 14,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  loadingContainer: {
    marginLeft: '20%',
    width: '30%',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  dropdownContainer: {
    borderRadius: 8,
    marginTop: '2%',
  },
});

export default ConfigurationAccordion;
