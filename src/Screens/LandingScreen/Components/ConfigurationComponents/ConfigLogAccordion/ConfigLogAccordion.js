import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {GET_SITE_ALARMS, Request_Types} from '../../../../api/uri';

import Accordion from 'react-native-collapsible/Accordion';

import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {Fonts} from '../../../../../styles/fonts';
import DownArrowSvg from '../../../../../assets/svgs/DownArrowSvg';
import RightArrowSvg from '../../../../../assets/svgs/RightArrowSvg';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import VerticalAlarmsLineSvg from '../../../../../assets/svgs/VerticalAlarmsLineSvg';

const ConfigLogAccordion = ({siteLoad}) => {
  const [activeItemId, setActiveItemId] = useState(null);
  const themeStyles = useThemeStyles();
  const usedAlarms = new Set();

  const getFilteredAlarms = (filter, usedAlarms) => {
    if (!siteLoad) {
      return [];
    }

    const filteredSiteLoad = siteLoad.filter(item => {
      const occurTime = new Date(item.createdat);
      const now = new Date();

      switch (filter) {
        case 'Last 30 Mins':
          return occurTime >= new Date(now.getTime() - 30 * 60 * 1000);

        case 'Yesterday':
          const yesterday = new Date();
          yesterday.setDate(now.getDate() - 1);
          return (
            occurTime.getFullYear() === yesterday.getFullYear() &&
            occurTime.getMonth() === yesterday.getMonth() &&
            occurTime.getDate() === yesterday.getDate()
          );

        case 'Last 7 days':
          return occurTime >= new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        case 'Last Month':
          return (
            occurTime.getFullYear() === now.getFullYear() &&
            occurTime.getMonth() === now.getMonth() - 1
          );

        case 'Last 6 Months':
          return (
            occurTime >=
            new Date(now.getFullYear(), now.getMonth() - 6, now.getDate())
          );

        case 'Last Year':
          return occurTime.getFullYear() === now.getFullYear() - 1;

        default: // Recent Changes (today)
          return (
            occurTime.getFullYear() === now.getFullYear() &&
            occurTime.getMonth() === now.getMonth() &&
            occurTime.getDate() === now.getDate()
          );
      }
    });

    const sectionsData = Object.values(
      filteredSiteLoad.reduce((acc, item) => {
        const uniqueKey = `${item.createdat}`;
        if (!acc[uniqueKey]) {
          acc[uniqueKey] = {
            ...item,
            key: uniqueKey,
          };
        }
        return acc;
      }, {}),
    );

    const filteredSections = sectionsData.filter(
      section => !usedAlarms.has(section.key),
    );

    filteredSections.forEach(section => usedAlarms.add(section.key));

    return filteredSections.sort(
      (a, b) => new Date(b.createdat) - new Date(a.createdat),
    );
  };

  const renderHeadingAndList = (heading, data) => {
    if (!data || data.length === 0) return null;

    return (
      <View style={styles.section}>
        <View
          style={{
            flexDirection: 'row',
            width: '90%',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text style={[styles.sectionHeader, {color: themeStyles.textColor}]}>
            {heading}
          </Text>
          <VerticalAlarmsLineSvg
            color={themeStyles.splitterColor}
            style={{marginTop: '4%'}}
          />
        </View>

        {data.map((item, index) => (
          <ConfigAccordionItem
            key={`${item.createdat}-${index}`}
            data={item}
            activeItemId={activeItemId}
            setActiveItemId={setActiveItemId}
          />
        ))}
      </View>
    );
  };

  // New function to group alarms by month and year for older alarms
  const groupAlarmsByMonthYear = alarms => {
    const grouped = {};
    const now = new Date();
    const lastYear = now.getFullYear() - 1;

    alarms.forEach(item => {
      const occurTime = new Date(item.createdat);
      if (occurTime.getFullYear() >= lastYear) return; // Skip if not older than last year

      const monthYearKey = `${occurTime.toLocaleString('default', {
        month: 'long',
      })} ${occurTime.getFullYear()}`;

      if (!grouped[monthYearKey]) {
        grouped[monthYearKey] = [];
      }
      grouped[monthYearKey].push(item);
    });

    return grouped;
  };

  // Get alarms older than last year
  const olderThanLastYearAlarms =
    siteLoad?.filter(item => {
      const occurTime = new Date(item.createdat);
      const now = new Date();
      return occurTime < new Date(now.getFullYear() - 1, 0, 1);
    }) || [];

  // Group them by month and year
  const groupedOlderAlarms = groupAlarmsByMonthYear(olderThanLastYearAlarms);

  return (
    <ScrollView style={styles.container}>
      {renderHeadingAndList(
        'Recent Changes',
        getFilteredAlarms('', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last 30 Mins',
        getFilteredAlarms('Last 30 Mins', usedAlarms),
      )}
      {renderHeadingAndList(
        'Yesterday',
        getFilteredAlarms('Yesterday', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last 7 days',
        getFilteredAlarms('Last 7 days', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last Month',
        getFilteredAlarms('Last Month', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last 6 Months',
        getFilteredAlarms('Last 6 Months', usedAlarms),
      )}
      {renderHeadingAndList(
        'Last Year',
        getFilteredAlarms('Last Year', usedAlarms),
      )}
      {Object.entries(groupedOlderAlarms).map(([monthYear, alarms]) =>
        renderHeadingAndList(monthYear, alarms),
      )}
    </ScrollView>
  );
};

const ConfigAccordionItem = ({data, activeItemId, setActiveItemId}) => {
  const isExpanded = activeItemId === data.createdat;
  const themeStyles = useThemeStyles();

  const toggleAccordion = () => {
    setActiveItemId(isExpanded ? null : data.createdat);
  };

  const renderHeader = () => {
    if (!data || !data.name) {
      return null;
    }

    const getStatusColor = status => {
      if (status === null) return '#FFA500'; // Orange color for Pending
      return status ? '#00EE5D' : '#FF0000'; // Green for Success, Red for Fail
    };

    const getStatusText = status => {
      if (status === null) return 'Pending';
      return status ? 'Successful' : 'Fail';
    };

    return (
      <View
        style={[
          styles.header,
          {
            backgroundColor: isExpanded
              ? themeStyles.alarmsBackground
              : 'transparent',
          },
        ]}>
        {isExpanded ? (
          <DownArrowSvg color={'#FF7F02'} style={{marginLeft: '2%'}} />
        ) : (
          <RightArrowSvg color={'#96999E'} style={{marginLeft: '2%'}} />
        )}

        <Text
          style={[
            styles.headerText,
            {color: isExpanded ? '#FF7F02' : '#96999E'},
          ]}
          numberOfLines={2}>
          {data.name}
        </Text>
        <Text style={[styles.headerText]}>|</Text>
        <Text
          style={[
            styles.headerText,
            {
              color: getStatusColor(data.issuccess),
            },
          ]}>
          {getStatusText(data.issuccess)}
        </Text>
      </View>
    );
  };

  const renderContent = () => {
    if (!data || !data.params || !data.params.parameters) {
      return null;
    }

    return (
      <View style={styles.content}>
        <View style={styles.contentRow}>
          <Text style={[styles.label, {color: '#96999E'}]}>User Email</Text>
          <Text style={[styles.value, {color: themeStyles.textColor}]}>
            {data.email}
          </Text>
        </View>

        <View style={styles.contentRow}>
          <Text style={[styles.label, {color: '#96999E'}]}>Creation Time</Text>
          <Text style={[styles.value, {color: themeStyles.textColor}]}>
            {formatTimestamp(data.createdat)}
          </Text>
        </View>

        <View style={styles.contentRow}>
          <Text style={[styles.label, {color: '#96999E'}]}>Updated Time</Text>
          <Text style={[styles.value, {color: themeStyles.textColor}]}>
            {formatTimestamp(data.updatedat)}
          </Text>
        </View>
        <View style={styles.contentRow}>
          <Text style={[styles.label, {color: '#96999E'}]}>
            Response Message
          </Text>
          <Text style={[styles.value, {color: themeStyles.textColor}]}>
            {data.response}
          </Text>
        </View>
        <View style={styles.splitterContainer}>
          <Text style={[styles.label, {color: '#616161', width: '100%'}]}>
            Configuration Data
          </Text>
          <VerticalAlarmsLineSvg
            color={'#616161'}
            style={{marginTop: '9%', marginLeft: '-30%'}}
          />
        </View>

        <NestedAccordion parameters={data.params.parameters} />
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={toggleAccordion}>
      {renderHeader()}
      {isExpanded && renderContent()}
    </TouchableOpacity>
  );
};

const NestedAccordion = ({parameters}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSubSection, setActiveSubSection] = useState(null);
  const themeStyles = useThemeStyles();

  if (!parameters) return null;

  // Group parameters by category
  const groupParameters = () => {
    const groups = {};

    Object.entries(parameters).forEach(([key, value]) => {
      // Check if the key contains a number (like "#1", "#2")
      const hasNumber = /\d/.test(key);

      if (hasNumber) {
        // For numbered parameters, use the full key as category
        const category = key;
        if (!groups[category]) {
          groups[category] = {};
        }
        groups[category][key] = value;
      } else {
        // For non-numbered parameters, extract category as before
        const category = key.split(' ').slice(0, -1).join(' ');
        if (!groups[category]) {
          groups[category] = {};
        }
        groups[category][key] = value;
      }
    });

    return groups;
  };

  const renderSubAccordion = (title, params) => {
    const isSubOpen = activeSubSection === title;

    if (Object.keys(params).length === 0) return null;

    return (
      <View key={title} style={styles.subAccordion}>
        <TouchableOpacity
          style={[
            styles.subHeader,
            {
              backgroundColor: isSubOpen
                ? themeStyles.background
                : 'transparent',
            },
          ]}
          onPress={() => setActiveSubSection(isSubOpen ? null : title)}>
          {isSubOpen ? (
            <DownArrowSvg color={'#FFC400'} style={{marginLeft: '4%'}} />
          ) : (
            <RightArrowSvg color={'#96999E'} style={{marginLeft: '4%'}} />
          )}
          <Text
            style={[
              styles.subHeaderText,
              {color: isSubOpen ? '#FFC400' : '#96999E'},
            ]}>
            {title}
          </Text>
        </TouchableOpacity>

        {isSubOpen && (
          <View
            style={[
              styles.nestedContent,
              {backgroundColor: themeStyles.background},
            ]}>
            {Object.entries(params).map(([key, value], index) => (
              <View key={index} style={styles.parameterRow}>
                <View style={styles.parameterValueContainer}>
                  <Text style={[styles.parameterLabel, {color: '#96999E'}]}>
                    Previous Value
                  </Text>
                  <Text
                    style={[
                      styles.parameterValue,
                      {color: themeStyles.textColor},
                    ]}>
                    {value.previous}
                  </Text>
                </View>
                <View style={styles.parameterValueContainer}>
                  <Text style={[styles.parameterLabel, {color: '#96999E'}]}>
                    Current Value
                  </Text>
                  <Text
                    style={[
                      styles.parameterValue,
                      {color: themeStyles.textColor},
                    ]}>
                    {value.updated}
                  </Text>
                </View>
                <View style={styles.parameterValueContainer}>
                  <Text style={[styles.parameterLabel, {color: '#96999E'}]}>
                    Log
                  </Text>
                  <Text
                    style={[
                      styles.parameterValue,
                      {color: themeStyles.textColor},
                    ]}>
                    ---
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.nestedAccordionContainer}>
      <View style={styles.nestedContent}>
        {Object.entries(groupParameters()).map(([title, params]) =>
          renderSubAccordion(title, params),
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '96%',
  },
  section: {
    marginBottom: 20,
    width: '100%',
  },
  sectionHeader: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    marginTop: '3%',
    marginLeft: '1%',
  },
  header: {
    padding: 10,
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    width: '100%',
  },
  headerText: {
    fontSize: 15,

    fontWeight: '500',
    marginLeft: '4%',
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  timeText: {
    fontSize: 12,
    marginLeft: 10,
  },
  content: {
    padding: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    marginTop: '-0.001%',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
  },
  contentRow: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    width: '40%',
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  value: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    width: '50%',
    marginLeft: '5%',
  },
  parameterSection: {
    marginVertical: 10,
  },

  paramName: {
    fontSize: 14,
    marginRight: 10,
  },
  paramValue: {
    fontSize: 14,
  },
  responseContainer: {
    width: '53%',
    flexDirection: 'row',
  },
  splitterContainer: {
    width: '50%',
    flexDirection: 'row',
  },
  nestedAccordionContainer: {
    marginTop: 10,
  },
  nestedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: 'transparent',
    borderRadius: 4,
  },
  nestedHeaderText: {
    fontSize: 15,
    marginLeft: 10,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  nestedContent: {
    padding: 10,
    paddingLeft: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.02)',
    borderRadius: 4,
  },
  parameterRow: {
    paddingVertical: 5,
    alignItems: 'center',
  },
  parameterKey: {
    width: '40%',
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  parameterValue: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    width: '60%',
    alignItems: 'center',
  },
  subHeader: {
    padding: 7,
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    width: '100%',
  },
  subHeaderText: {
    fontSize: 15,
    marginLeft: '4%',
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  parameterValueContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default ConfigLogAccordion;
