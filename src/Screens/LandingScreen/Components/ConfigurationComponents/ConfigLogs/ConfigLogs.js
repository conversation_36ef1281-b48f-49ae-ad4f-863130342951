import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import ConfigurationHeader from '../Header/ConfigurationHeader';
import ParametersScreenHeader from '../../ParametersComponent.js/Common/ParametersScreenHeader';
import {GetsiteID} from '../../../../../redux/Slices/SiteIdSlice';
import {useDispatch, useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DatePicker from '../../../../Components/DatePicker/DatePicker';
import DatePickerModal from '../../../../Components/DatePicker/DatePickerModal';
import {DayFilter, DayFilter_7} from '../../../../../Constants/DateFilter';
import dayjs from 'dayjs';
import ParametersFilter from '../../ParametersComponent.js/Common/ParametersFilter';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import {determineValueType} from '../../../../../Constants/DetermineValueType';
import {Fonts} from '../../../../../styles/fonts';
import DownloadsSvg from '../../../../../assets/svgs/DownloadsSvg';
import ConfigLogAccordion from '../ConfigLogAccordion/ConfigLogAccordion';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import {
  GET_CONFIG_LOGS,
  GET_CONFIGURATIONS,
  Request_Types,
} from '../../../../../api/uri';
import NoDataFoundMessageComponent from '../../ParametersComponent.js/Common/NoDataFoundMessageComponent';

const ConfigLogs = () => {
  const [siteId, setSiteId] = useState('');
  const [siteLoad, setSiteLoad] = useState([]);
  const [selectedType, setSelectedType] = useState('Config');
  const [startDate, setStartDate] = useState(DayFilter_7());
  const [endDate, setEndDate] = useState(DayFilter());
  const [closeModal, setCloseModal] = useState(false);
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [selectedItem, setSelectedItem] = useState({
    id: '1',
    title: 'General',
    name: 'General',
  });
  const dispatch = useDispatch();
  const [isTypeChanging, setIsTypeChanging] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState(true); // true for Active, false for History
  const [isActive, setIsActive] = useState(true); // Set default to true for Active
  const [isHistory, setIsHistory] = useState(false); // Set default to false for History
  const handleDateChange = ({startDate, endDate}) => {
    if (startDate && endDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');

      setStartDate(formattedStartDate);
      setEndDate(formattedEndDate);

      // Fetch data with new dates
    }
  };

  const fetchConfigLogs = useCallback(
    async id => {
      if (!id) return;

      try {
        setLoading(true);
        setIsTypeChanging(true);
        const token = await AsyncStorage.getItem('Authorization');

        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_CONFIG_LOGS(id, selectedFilter, startDate, endDate)}`,
          headers: {Authorization: token},
        });

        const dataObject = response.data.data;
        setSiteLoad(dataObject);
      } catch (error) {
        console.error('Site Configuration Fetch Error:', error);
      } finally {
        setLoading(false);
        setIsTypeChanging(false);
      }
    },
    [selectedFilter, startDate, endDate],
  );

  useEffect(() => {
    if (isSiteID && isSiteID !== siteId) {
      setSiteId(isSiteID);
      fetchConfigLogs(isSiteID);
    }
  }, [isSiteID, fetchConfigLogs]);

  useEffect(() => {
    if (siteId) {
      fetchConfigLogs(siteId);
    }
  }, [siteId, fetchConfigLogs]);
  const handleSiteIdChange = useCallback(
    async newSiteId => {
      setSiteId(newSiteId);
      dispatch(GetsiteID(newSiteId));
      try {
        await AsyncStorage.setItem('SelectedSiteId', newSiteId);
      } catch (error) {
        console.error('Error saving site ID:', error);
      }
    },
    [dispatch],
  );
  const toggleSearch = useCallback(() => {
    setShowSearch(prev => !prev);
  }, []);

  const themeStyles = useThemeStyles();
  const getStatusText = status => {
    if (status === null) {
      return 'Pending';
    }
    return status ? 'Successful' : 'Failed';
  };

  const filteredLogs = useMemo(() => {
    if (!searchQuery.trim() || !siteLoad) return siteLoad;

    const query = searchQuery.toLowerCase();
    return siteLoad.filter(log => {
      const statusText = getStatusText(log.issuccess).toLowerCase();
      const statusMatch = statusText.includes(query);
      const responseMatch = log.response?.toLowerCase().includes(query);
      const createdAtMatch = log.createdat?.toLowerCase().includes(query);
      const updatedAtMatch = log.updatedat?.toLowerCase().includes(query);
      const nameMatch = log.name?.toLowerCase().includes(query);
      const emailMatch = log.email?.toLowerCase().includes(query);
      const siteCodeMatch = log.sitecode?.toLowerCase().includes(query);

      // Search through parameters
      const parametersMatch = Object.entries(log.params?.parameters || {}).some(
        ([key, value]) => {
          const keyMatch = key.toLowerCase().includes(query);
          const updatedMatch = String(value.updated)
            .toLowerCase()
            .includes(query);
          const previousMatch = String(value.previous)
            .toLowerCase()
            .includes(query);
          return keyMatch || updatedMatch || previousMatch;
        },
      );

      return (
        statusMatch ||
        responseMatch ||
        createdAtMatch ||
        updatedAtMatch ||
        nameMatch ||
        emailMatch ||
        siteCodeMatch ||
        parametersMatch
      );
    });
  }, [siteLoad, searchQuery]);

  const handleRefresh = useCallback(() => {
    if (siteId) {
      fetchConfigLogs(siteId);
    }
  }, [siteId, fetchConfigLogs]);

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <ConfigurationHeader arrowIcon={false} onRefresh={handleRefresh} />
      <ParametersScreenHeader
        siteId={siteId}
        onSiteIdChange={handleSiteIdChange}
        showSearch={showSearch}
        toggleSearch={toggleSearch}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedItem={selectedItem}
        setSelectedItem={setSelectedItem}
        parameterCount={isTypeChanging ? 0 : filteredLogs?.length}
        selectedType={selectedType}
        showCrossIcon={showSearch}
        showCount={false}
        showCalender={true}
        setModal={setCloseModal}
        startDate={startDate}
        endDate={endDate}
      />
      {closeModal && (
        <DatePickerModal
          setCloseModal={setCloseModal}
          setEndDate={setEndDate}
          setStartDate={setStartDate}
        />
      )}
      <View style={styles.selectionContainer}>
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[
              styles.selectedFilter,
              {backgroundColor: isActive ? '#FF7F02' : 'transparent'},
            ]}
            onPress={() => {
              setIsActive(true);
              setIsHistory(false);
              setSelectedFilter(true);
            }}>
            <Text
              style={{
                color: isActive ? '#0E121A' : '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                fontSize: 15,
              }}>
              Active
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.selectedFilter,
              {backgroundColor: isHistory ? '#FF7F02' : 'transparent'},
            ]}
            onPress={() => {
              setIsActive(false);
              setIsHistory(true);
              setSelectedFilter(false);
            }}>
            <Text
              style={{
                color: isHistory ? '#0E121A' : '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                fontSize: 15,
              }}>
              History
            </Text>
          </TouchableOpacity>
        </View>
        <View style={styles.downloadIcon}>
          <DownloadsSvg />
        </View>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            animating={isLoading}
            size="large"
            color="#FF7F02"
          />
        </View>
      ) : !filteredLogs || filteredLogs.length === 0 ? (
        <NoDataFoundMessageComponent searchQuery={searchQuery} />
      ) : (
        <ConfigLogAccordion siteLoad={filteredLogs} />
      )}
    </View>
  );
};

export default ConfigLogs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  selectedFilter: {
    borderRadius: 6,
    width: '50%',
    height: '90%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  selectionContainer: {
    width: '96%',
    height: '5.5%',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '1%',
    flexDirection: 'row',

    borderColor: '#50555E',

    alignSelf: 'center', // Add this to ensure center alignment
  },
  filterContainer: {
    width: '85%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 6,
    borderWidth: 0.5,
    flexDirection: 'row',

    paddingHorizontal: '1%',

    borderColor: '#50555E',

    alignSelf: 'center',
  },
  downloadIcon: {
    height: 46,
    width: '13%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF1A',
    borderRadius: 6,
    marginRight: 0,
  },
});
