import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {
  APPLY_CHANGES_CONFIG,
  GET_CONFIGURATIONS,
  GET_USER_DEFINED_CONFIGS,
  GET_USER_DEFINED_PARAMS,
  Request_Types,
} from '../../../../../api/uri';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import {determineValueType} from '../../../../../Constants/DetermineValueType';
import {Fonts} from '../../../../../styles/fonts';
import ConfigurationAccordion from '../ConfigurationAccordion/ConfigurationAccordion';
import NoDataFoundMessageComponent from '../../ParametersComponent.js/Common/NoDataFoundMessageComponent';
import Toast from 'react-native-toast-message';

const FetchUserDefinedConfigs = ({
  selectedType,
  siteId,
  data,
  setShowFetchedData,
  onClose,
  searchQuery,
  category,
}) => {
  const themeStyles = useThemeStyles();
  const [siteLoad, setSiteLoad] = useState([]);
  const [loading, setLoading] = useState(false);
  const [timestamp, setTimestamp] = useState(null);
  const [shouldFetch, setShouldFetch] = useState(true);
  const prevSiteIdRef = useRef(siteId);
  const prevSelectedTypeRef = useRef(selectedType);
  const [resetTrigger, setResetTrigger] = useState(false);
  const [hasModifiedValues, setHasModifiedValues] = useState(false);
  const [updatedValuesJson, setUpdatedValuesJson] = useState({});
  const fetchSiteConfigs = useCallback(async id => {
    if (!id) return;

    try {
      setLoading(true);

      const token = await AsyncStorage.getItem('Authorization');

      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_USER_DEFINED_CONFIGS(id, data)}`,
        headers: {Authorization: token},
      });

      const dataObject = response.data.data[0].result?.[id];
      const timestamp = response.data.data[0].result?.[id].ts;

      const formattedData = Object.entries(dataObject || {})
        .filter(([key]) => key !== 'ts')
        .map(([key, value]) => {
          const {value: convertedValue, type} = determineValueType(value);
          return {
            key,
            value: convertedValue,
            dataType: type,
          };
        });

      setSiteLoad(formattedData);
      const formattedTimestamp = formatTimestamp(timestamp);
      setTimestamp(formattedTimestamp);
    } catch (error) {
      console.error('Site Parameters Fetch Error:', error);
    } finally {
      setLoading(false);
    }
  });
  const handleValueModification = isModified => {
    setHasModifiedValues(isModified);
  };
  // Remove the first useEffect that depends only on selectedType
  // useEffect(() => {
  //   if (siteId) {
  //     setShouldFetch(true);
  //     fetchSiteConfigs(siteId);
  //   }
  // }, [selectedType]);

  // Modify the main fetch effect
  useEffect(() => {
    if (siteId && shouldFetch) {
      fetchSiteConfigs(siteId);
      setShouldFetch(false); // Disable fetching after initial load

      // Set up interval for periodic updates
      const interval = setInterval(() => {
        fetchSiteConfigs(siteId);
      }, 5 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [siteId, shouldFetch, fetchSiteConfigs]);

  // Add effect to handle type changes
  useEffect(() => {
    if (
      siteId &&
      (prevSelectedTypeRef.current !== selectedType ||
        prevSiteIdRef.current !== siteId)
    ) {
      setShouldFetch(true);
      prevSelectedTypeRef.current = selectedType;
      prevSiteIdRef.current = siteId;
    }
  }, [selectedType, siteId]);

  const filteredParameters = useMemo(() => {
    if (!searchQuery.trim() || !siteLoad) return siteLoad;

    const query = searchQuery.toLowerCase();
    return siteLoad.filter(param => {
      const description = String(param.description || '').toLowerCase();
      const key = String(param.key || '').toLowerCase();
      return description.includes(query) || key.includes(query);
    });
  }, [siteLoad, searchQuery]);

  const handleClose = useCallback(() => {
    setShouldFetch(false);
    onClose();
  }, [onClose]);

  const handleChangeParams = useCallback(() => {
    setShouldFetch(false);
    setShowFetchedData(false);
  }, [setShowFetchedData]);
  const handleResetAll = () => {
    setResetTrigger(true); // Trigger reset in child component
    setHasModifiedValues(false);
  };

  const handleEditConfiguration = async () => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('Authorization');

      const response = await ApiCaller({
        method: Request_Types.POST,
        url: `${APPLY_CHANGES_CONFIG}`,
        headers: {Authorization: token},
        data: updatedValuesJson,
      });

      const dataObject = response.data.success;
      if (dataObject) {
        Toast.show({
          type: 'info',
          text1: 'Info',
          text2: 'Updated Successfully',
        });
      }
      // Convert values based on their types
    } catch (error) {
      console.error('Site Parameters Fetch Error:', error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator animating={loading} size="large" color="#FF7F02" />
        </View>
      ) : siteLoad.length > 0 ? (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContentContainer}
          showsVerticalScrollIndicator={false}>
          <ConfigurationAccordion
            siteLoad={filteredParameters}
            alarmsType={selectedType}
            timestamp={timestamp}
            category={category}
            isUser={true}
            onValueModification={handleValueModification}
            resetTrigger={resetTrigger}
            setResetTrigger={setResetTrigger}
            setUpdatedValuesJson={setUpdatedValuesJson}
          />
        </ScrollView>
      ) : (
        <NoDataFoundMessageComponent searchQuery={searchQuery} />
      )}
      {hasModifiedValues ? (
        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              styles.backButton,
              {borderWidth: 1, borderColor: '#FF2322'},
            ]}
            onPress={handleResetAll}>
            <Text style={styles.clearButtonText}>Reset All</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.backButton, {backgroundColor: '#FF7F02'}]}
            onPress={handleEditConfiguration}>
            <Text style={styles.changeParamsText}>Update All</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              styles.backButton,
              {borderWidth: 1, borderColor: '#FF2322'},
            ]}
            onPress={handleClose}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.backButton, {backgroundColor: '#FF7F02'}]}
            onPress={handleChangeParams}>
            <Text style={styles.changeParamsText}>Change Params</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default FetchUserDefinedConfigs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  scrollView: {
    flex: 1,
    width: '100%',
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingVertical: 10,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    width: '90%',
    height: '9.5%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '1%',
  },
  backButton: {
    height: '90%',
    width: '48%',

    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FF2322',
  },
  changeParamsText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#0E121A',
  },
});
