import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import LeftArrowSvg from '../../../../assets/svgs/DownArrowSvg';
import RightArrowSvg from '../RightArrowSvg';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {AutocompleteDropdown} from 'react-native-autocomplete-dropdown';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';

const PSUFilter = ({setDate, isLandscape, modalHeight, setSelectedItem}) => {
  const istheme = useSelector(state => state.theme.theme);

  const [isDark, setIsDark] = useState(false);
  const themeStyles = useThemeStyles();

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');

        if (storedTheme !== null) {
          const parsedTheme = JSON.parse(storedTheme);
          setIsDark(parsedTheme);
        } else {
          // Use Redux theme as fallback if no theme is in AsyncStorage
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching/parsing theme:', error);
        setIsDark(istheme === 'dark'); // Fallback to Redux in case of error
      }
    };

    fetchTheme();
  }, [istheme]);

  const background = isDark ? '#FFFFFFE5' : '#252A34B2';
  const siteInfoButton = isDark ? '#FFFFFF4D' : '#432A1180';
  const siteInfoButtonColor = isDark ? '#FF7E00' : '#FF7F02';
  const siteIdColor = !isDark ? '#FFFFFF' : '#0E121A';
  const locationtext = isDark ? '#737476' : '#96999E';

  const [isFocus1D, setFocused1D] = useState(false);
  const [isFocus2D, setFocused2D] = useState(true);
  const [isFocusMTd, setFocusedMTD] = useState(false);
  const [isFocusYTD, setFocusedYTD] = useState(false);
  const buttonBackground1D = isFocus1D ? '#FF7F02' : siteIdColor;
  const buttonBackground2D = isFocus2D ? '#FF7F02' : siteIdColor;
  const buttonBackgroundMTD = isFocusMTd ? '#FF7F02' : siteIdColor;
  const buttonBackgroundYTD = isFocusYTD ? '#FF7F02' : siteIdColor;
  const data = [
    {
      id: '1',
      title: 'Daily',
      name: 'Daily',
    },
    {
      id: '2',
      title: 'Weekly',
      name: 'Weekly',
    },

    {
      id: '3',
      title: 'Monthly',
      name: 'Monthly',
    },
    {
      id: '4',
      title: 'Yearly',
      name: 'Yearly',
    },
  ];

  return (
    <View
      style={[styles.dayFilter, {marginLeft: !isLandscape ? '7%' : '155%'}]}>
      <AutocompleteDropdown
        dataSet={data}
        forcePopupIcon={false}
        showClear={false}
        clearOnFocus={false}
        initialValue={{id: '2'}}
        editable={false}
        onSelectItem={setSelectedItem}
        textInputProps={{
          style: {
            color: themeStyles.textColor,
            fontFamily: Fonts.BaiJamjuree_Bold,
            fontSize: 15,
            textAlign: 'center',
            textAlignVertical: 'center',
            backgroundColor: 'transparent',
            overflow: 'hidden',
            width: '100%',
            height: 70,
          },
        }}
        renderItem={item => (
          <Text
            style={{
              color: themeStyles.textColor,
              fontSize: 15,
              fontFamily: Fonts.BaiJamjuree_Medium,
              height: 50,

              textAlignVertical: 'center',
              padding: 10,
              backgroundColor: 'transparent',
            }}>
            {item.name}
          </Text>
        )}
        rightIcon={null}
        clearIcon={null}
        containerStyle={{
          paddingRight: 0,
          backgroundColor: 'transparent',

          width: '100%',
          height: isLandscape ? '110%' : modalHeight * 0.11,
          marginLeft: '-35%',
          alignContent: 'center',
          justifyContent: 'center',
          marginTop: '6%',
          overflow: 'visible',
        }}
        inputContainerStyle={{
          backgroundColor: 'transparent',
          height: 45,
          width: '110%',
          borderWidth: 0,
          marginLeft: '4%',
          borderBottomWidth: 0,
        }}
        suggestionsListContainerStyle={{
          backgroundColor: 'white',
          maxHeight: modalHeight * 0.3,
          zIndex: 1000000000,
        }}
        renderRightIcon={() => null}
        renderClearIcon={() => null}
      />
    </View>
  );
};

export default PSUFilter;

const styles = StyleSheet.create({
  dayFilter: {
    height: '50%',
    width: '50%',
    backgroundColor: '#FFFFFF08',

    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '4%',
    justifyContent: 'center',
    marginLeft: '9%',
    borderRadius: 6,
  },
  days: {
    height: '70%',
    width: '22%',

    marginLeft: '1.2%',

    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  months: {
    height: '70%',
    width: '16%',

    marginLeft: '1%',

    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,

    marginLeft: '15%',
    marginRight: '15%',
  },
});
