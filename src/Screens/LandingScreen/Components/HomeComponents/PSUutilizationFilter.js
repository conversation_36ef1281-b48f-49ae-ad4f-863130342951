import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';

const PSUutilizationFilter = ({setUtilization, isLandscape}) => {
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(false);
  const themeStyles = useThemeStyles();

  // Initialize with Utilization selected
  const [isAvailability, setAvailability] = useState(false);
  const [isUtilization, setPowerUtilization] = useState(true);

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');

        if (storedTheme !== null) {
          const parsedTheme = JSON.parse(storedTheme);
          setIsDark(parsedTheme);
        } else {
          // Use Redux theme as fallback if no theme is in AsyncStorage
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching/parsing theme:', error);
        setIsDark(istheme === 'dark'); // Fallback to Redux in case of error
      }
    };

    fetchTheme();
  }, [istheme]);

  // Handle toggle for Availability
  const handleAvailabilityToggle = () => {
    if (!isAvailability) {
      setAvailability(true);
      setPowerUtilization(false);
      setUtilization('Availability');
    }
  };

  // Handle toggle for Utilization
  const handleUtilizationToggle = () => {
    if (!isUtilization) {
      setAvailability(false);
      setPowerUtilization(true);
      setUtilization('Utilization');
    }
  };

  // Calculate colors based on theme and selection state
  const AvailabilityColor = isAvailability
    ? isDark
      ? '#FFFFFF'
      : '#0E121A'
    : isDark
    ? '#0E121A'
    : '#FFFFFF';
  const UtilizationColor = isUtilization
    ? isDark
      ? '#FFFFFF'
      : '#0E121A'
    : isDark
    ? '#0E121A'
    : '#FFFFFF';
  const buttonBackgroundAvailability = isAvailability ? '#FF7F02' : '#FFFFFF01';
  const buttonBackgroundUtilization = isUtilization ? '#FF7F02' : '#FFFFFF01';

  return (
    <View style={[styles.hrFilter, isLandscape && styles.landscapedesign]}>
      <TouchableOpacity
        style={[styles.hrFilterButton]}
        onPress={handleAvailabilityToggle}>
        <Text
          style={[
            styles.buttonText,
            {
              backgroundColor: buttonBackgroundAvailability,
              color: AvailabilityColor,
            },
          ]}>
          Availability
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.hrFilterButton]}
        onPress={handleUtilizationToggle}>
        <Text
          style={[
            styles.buttonText,
            {
              backgroundColor: buttonBackgroundUtilization,
              color: UtilizationColor,
            },
          ]}>
          Utilization
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default PSUutilizationFilter;

const styles = StyleSheet.create({
  hrFilter: {
    height: '50%',
    width: '80%',
    backgroundColor: '#FFFFFF05',
    borderRadius: 6,
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#FF7F0233',
    zIndex: 1000,
  },
  landscapedesign: {
    height: '100%',
    width: '28%',
    marginTop: '0%',
  },
  hrFilterButton: {
    height: '70%',
    width: '46%',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    height: '110%',
    textAlign: 'center',
    width: '100%',
    borderRadius: 5,
  },
});
