import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

const HourKwh = ({setUtilization, isLandscape}) => {
  const istheme = useSelector(state => state.theme.theme);

  const [isDark, setIsDark] = useState(false);
  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');

        if (storedTheme !== null) {
          const parsedTheme = JSON.parse(storedTheme);
          setIsDark(parsedTheme);
        } else {
          // Use Redux theme as fallback if no theme is in AsyncStorage
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching/parsing theme:', error);
        setIsDark(istheme === 'dark'); // Fallback to Redux in case of error
      }
    };

    fetchTheme();
  }, [istheme]);

  const [isFocusKWH, setFocusedKWH] = useState(false);

  const [isFocusHours, setFocusedHours] = useState(true);

  const KwhColor = isFocusKWH
    ? isDark
      ? '#FFFFFF'
      : '#0E121A'
    : isDark
    ? '#0E121A'
    : '#FFFFFF';

  const hrColor = isFocusHours
    ? isDark
      ? '#FFFFFF'
      : '#0E121A'
    : isDark
    ? '#0E121A'
    : '#FFFFFF';

  const buttonBackgroundKWH = isFocusKWH ? '#FF7F02' : '#FFFFFF01';
  const buttonBackgroundHours = isFocusHours ? '#FF7F02' : '#FFFFFF01';

  return (
    <View style={[styles.hrFilter, isLandscape && styles.landscapedesign]}>
      <TouchableOpacity
        style={[styles.hrFilterButton]}
        onPress={() => {
          setFocusedKWH(true);
          setFocusedHours(false);
          setUtilization('kwh');
        }}>
        <Text
          style={[
            {
              fontSize: 15,
              fontFamily: Fonts.BaiJamjuree_Medium,
              height: '110%',

              textAlign: 'center',
              width: '100%',
              borderRadius: 5,
            },
            {backgroundColor: buttonBackgroundKWH, color: KwhColor},
          ]}>
          kWh
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.hrFilterButton]}
        onPress={() => {
          setFocusedKWH(false);
          setFocusedHours(true);
          setUtilization('hrs');
        }}>
        <Text
          style={[
            {
              fontSize: 15,
              fontFamily: Fonts.BaiJamjuree_Medium,
              height: '110%',

              textAlign: 'center',
              width: '100%',
              borderRadius: 5,
            },
            {backgroundColor: buttonBackgroundHours, color: hrColor},
          ]}>
          Hours
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default HourKwh;

const styles = StyleSheet.create({
  hrFilter: {
    height: '50%',
    width: '50%',
    marginTop: '4%',
    backgroundColor: '#FFFFFF05',
    borderRadius: 6,
    borderWidth: 0.5,

    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: '0%',
    borderColor: '#FF7F0233',
  },
  landscapedesign: {
    height: '74%',
    width: '20%',
    marginTop: '0%',
    marginLeft: '2%',
  },
  hrFilterButton: {
    height: '70%',
    width: '46%',

    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
