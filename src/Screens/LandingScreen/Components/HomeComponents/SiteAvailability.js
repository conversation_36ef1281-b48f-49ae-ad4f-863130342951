import {StyleSheet, Text, View} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import MenuSvg from '../../../../assets/svgs/MenuSvg';
import Nar<PERSON>hart from '../../../Components/Charts/NarChart';
import <PERSON>r<PERSON><PERSON> from '../../../Components/Charts/ParChart';
import Tch<PERSON><PERSON> from '../../../Components/Charts/TchChart';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {GET_SITE_AVAILABILITY, Request_Types} from '../../../../api/uri';
import {DayFilter, DayFilter_7} from '../../../../Constants/DateFilter';
import {NAR} from '../../../../Constants/SourceColors';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';

const SiteAvailability = () => {
  const [siteId, setSiteId] = useState('AAB801');
  const [siteAvailability, setSiteAvailability] = useState();
  const [nar, setNar] = useState();
  const [par, setPar] = useState();
  const [tch, setTch] = useState();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (isSiteID) {
          setSiteId(isSiteID);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };

    fetchSiteId();
  }, [isSiteID]);

  const fetchAvailability = useCallback(async () => {
    try {
      if (isSiteID) {
        const token = await AsyncStorage.getItem('Authorization');
        const header = {Authorization: token};
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_SITE_AVAILABILITY(isSiteID, DayFilter_7(), DayFilter())}`,
          headers: header,
        });

        setNar(Number(response.data?.data?.nar?.value));
        setPar(Number(response.data?.data?.par?.value));
        setTch(Number(response.data?.data?.tch?.value));

        //  if (data) {
        //    setSiteAvailability(data?.sourceTag);
        //  }
      }
    } catch (error) {
      console.error('Fetch Error:', error);
    }
  }, [isSiteID]);

  useEffect(() => {
    fetchAvailability();
    const interval = setInterval(() => {
      fetchAvailability();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [isSiteID, fetchAvailability]);

  return (
    <View
      style={[
        styles.siteAvailabilityContainer,
        {backgroundColor: themeStyles.ssvBackground},
      ]}>
      <View style={styles.siteAvailabilityHeader}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: Fonts.BaiJamjuree_Medium,
            color: '#96999E',
            marginLeft: '4%',
            marginTop: '4%',
          }}>
          Site Availability
        </Text>
        <MenuSvg style={{marginTop: '5%', marginLeft: '55%'}} />
      </View>
      <View style={styles.availabiltyChart}>
        <View style={styles.narChart}>
          <NarChart Nar={nar} />
          <Text
            style={{
              fontSize: 16,
              fontFamily: Fonts.BaiJamjuree_Medium,
              color: themeStyles.textColor,

              marginTop: '-5%',
            }}>
            {nar ? nar.toFixed(1) : '0'}%
          </Text>
        </View>
        <View style={styles.parChart}>
          <ParChart Par={par} />
          <Text
            style={{
              fontSize: 16,
              fontFamily: Fonts.BaiJamjuree_Medium,
              color: themeStyles.textColor,

              marginTop: '-5%',
            }}>
            {par ? par.toFixed(1) : '0'}%
          </Text>
        </View>
        <View style={styles.tchChart}>
          <TchChart Tch={tch} />
          <Text
            style={{
              fontSize: 16,
              fontFamily: Fonts.BaiJamjuree_Medium,
              color: themeStyles.textColor,

              marginTop: '-5%',
            }}>
            {tch ? tch.toFixed(1) : '0'}%
          </Text>
        </View>
      </View>
    </View>
  );
};

export default SiteAvailability;

const styles = StyleSheet.create({
  siteAvailabilityContainer: {
    height: '13%',

    backgroundColor: '#252A34B2',
    borderRadius: 6,
    width: '95%',

    marginTop: '2%',
    alignItems: 'center',
  },
  availabiltyChart: {
    height: '65%',
    width: '90%',

    marginTop: '3%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  siteAvailabilityHeader: {
    height: '20%',
    width: '100%',
    flexDirection: 'row',
  },
  narChart: {
    height: '100%',

    width: '21%',

    alignItems: 'center',
  },
  tchChart: {
    height: '100%',

    width: '21%',
    marginLeft: '14%',

    alignItems: 'center',
  },
  parChart: {
    height: '100%',

    width: '21%',
    marginLeft: '14%',

    alignItems: 'center',
  },
});
