import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import SunsetCardSvg from '../../../../../assets/svgs/SunsetCardSvg';
import {Fonts} from '../../../../../styles/fonts';
import WindSvg from '../../../../../assets/svgs/WindSvg';
import {formatDate} from '../../../../../Constants/FormateDate';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import {Extract_Time_In_12_HourFormat} from '../../../../../Constants/Extract_Time_In_12_HourFormat';

const SunsetSunriseCard = ({weatherInfo}) => {
  const sunrise = Extract_Time_In_12_HourFormat(weatherInfo.daily?.sunrise[0]);
  const sunset = Extract_Time_In_12_HourFormat(weatherInfo.daily?.sunset[0]);
  return (
    <View style={styles.container}>
      <SunsetCardSvg
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          top: '40%',
          left: '0%',
        }}
      />

      <View style={styles.content}>
        <View style={styles.header}>
          <WindSvg />
          <Text style={styles.title}>Sunrise & Set</Text>
        </View>
        <Text style={styles.percentage}>
          {/* {weatherInfo?.current?.wind_speed_10m}{' '}
          {weatherInfo?.current_units?.wind_speed_10m} */}
        </Text>
        <View style={styles.footer}>
          <Text
            style={{
              height: '100%',
              color: '#96999E',
              textAlign: 'left',
              fontSize: 12,
              fontFamily: Fonts.BaiJamjuree_Regular,
            }}>
            {sunrise?.time12 && sunset?.time12
              ? `${sunrise.time12} - ${sunset.time12}`
              : '0'}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default SunsetSunriseCard;

const styles = StyleSheet.create({
  container: {
    width: 170,
    height: 170,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#252A3480',
    justifyContent: 'center',
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    // backgroundColor: '#FFFFFF1A',
  },
  fillContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    borderRadius: 20,
  },
  gradient: {
    flex: 1,
    opacity: 0.9,
  },
  content: {
    padding: 10,

    flexDirection: 'column',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    height: '20%',
    marginTop: '10%',
    width: '100%',
    alignSelf: 'flex-start',
  },
  icon: {
    fontSize: 16,
    marginRight: 8,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    fontFamily: Fonts.BaiJamjuree_Medium,
    width: 'auto',
  },
  percentage: {
    color: '#FFFFFF',
    fontSize: 30,
    height: '35%',
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    textAlign: 'left',
    marginLeft: '6%',
  },
  footer: {
    width: '90%',
    height: '25%',
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',

    marginTop: '16%',
    // backgroundColor: 'white',
  },
  compassContainer: {},
});
