import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import PrecipitationIcon from '../../../../../assets/svgs/PrecipitationIcon';
import FontFamily from '../../../../../assets/styles/FontFamily';
import {Fonts} from '../../../../../styles/fonts';
import CloudsSvg from '../../../../../assets/svgs/CloudsSvg';

const PrecipitationCard = ({weatherInfo}) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <PrecipitationIcon />
          <Text style={styles.title}>Precipitation</Text>
        </View>
        <View
          style={{
            height: '33%',
            flexDirection: 'row',
            width: '100%',

            justifyContent: 'space-between',
            // backgroundColor: 'white',
          }}>
          <Text style={styles.percentage}>
            {weatherInfo !== undefined && weatherInfo !== null
              ? `${Number(weatherInfo).toFixed(1)}%`
              : '0'}
          </Text>
          <CloudsSvg />
        </View>

        <Text
          style={{
            width: '87%',
            height: '100%',
            color: '#96999E',
            fontFamily: Fonts.BaiJamjuree_Regular,
            fontSize: 12,
            alignSelf: 'center',
          }}>
          Total rain for the day
        </Text>
      </View>
    </View>
  );
};

export default PrecipitationCard;

const styles = StyleSheet.create({
  container: {
    width: '47%',
    height: '100%',
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#1A366E80',
    justifyContent: 'center',
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    // backgroundColor: '#FFFFFF1A',
  },
  fillContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    borderRadius: 20,
  },
  gradient: {
    flex: 1,
    opacity: 0.9,
  },
  content: {
    padding: 6,

    flexDirection: 'column',
    marginTop: '23%',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    height: '30%',
    width: '100%',

    // backgroundColor: 'white',
  },

  title: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  percentage: {
    color: '#FFFFFF',
    fontSize: 35,
    height: '100%',
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    textAlign: 'left',
    marginLeft: '6%',
    width: 'auto',
    // backgroundColor: 'white',
  },
  footer: {
    width: '100%',
    height: '35%',
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
});
