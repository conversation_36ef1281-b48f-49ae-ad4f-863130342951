import {Image, ScrollView, StyleSheet, Text, View} from 'react-native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {Fonts} from '../../../../../styles/fonts';
import {WeatherMaping} from '../../../../../Constants/WeatherMaping';
import {weather_icons} from '../../../../../Constants/WeatherIconMaping';
import HumidityCard from './HumidityCard';
import PrecipitationCard from './PrecipitationCard';
import BottomSheet, {BottomSheetView} from '@gorhom/bottom-sheet';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  GET_SITE_INFO,
  GET_WEATHER_DETAILS,
  Request_Types,
} from '../../../../../api/uri';
import {ApiCaller} from '../../../../../middleWare/ApiCaller';
import WindDetailsCard from './WindDetailsCard';
import SunsetSunriseCard from './SunsetSunriseCard';

const WeatherInformationCard = ({setWeatherVisible}) => {
  const themeStyles = useThemeStyles();
  const snapPoints = useMemo(() => ['80%'], []);
  const [siteInfo, setSiteInfo] = useState([]);
  const [lattitude, setLattitude] = useState();
  const [longitude, setLongitude] = useState();
  const [location, setLocation] = useState('');
  const [weather, setWeather] = useState(0);
  const [weatherCode, setWeatherCode] = useState(0);
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [weatherInfo, setWeatherInfo] = useState([]);
  const fetchSiteInfoData = useCallback(async id => {
    if (!id) return;
    try {
      const token = await AsyncStorage.getItem('Authorization');
      const header = {Authorization: token};
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_SITE_INFO(id)}`,
        headers: header,
      });

      setSiteInfo(response.data?.data || []);
    } catch (error) {
      console.error('Site Load Fetch Error:', error);
    } finally {
    }
  }, []);
  useEffect(() => {
    if (lattitude && longitude) {
      fetchCurrentWeather();
    }
  }, [lattitude, longitude]);
  const fetchCurrentWeather = useCallback(async () => {
    try {
      // const cachedWeather = await AsyncStorage.getItem('cachedWeather');
      // const cachedTime = await AsyncStorage.getItem('cachedTime');

      // if (
      //   cachedWeather &&
      //   cachedTime &&
      //   Date.now() - parseInt(cachedTime) < 5 * 60 * 1000
      // ) {
      //   // Use cached data if it's less than 5 minutes old
      //   const parsedWeather = JSON.parse(cachedWeather);
      //   setWeather(parsedWeather.temperature);
      //   setWeatherCode(parsedWeather.weatherCode);
      //   return;
      // }

      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_WEATHER_DETAILS(lattitude, longitude)}`,
      });

      setWeatherInfo(response.data);

      if (response.data?.current?.temperature_2m != null) {
        const temperature = String(
          Math.round(response.data?.current?.temperature_2m) || 0,
        );

        const weatherCode = response.data?.current?.weather_code || 0;

        setWeather(temperature);
        setWeatherCode(weatherCode);

        // Cache the data
        await AsyncStorage.setItem(
          'cachedWeather',
          JSON.stringify({temperature, weatherCode}),
        );
        await AsyncStorage.setItem('cachedTime', Date.now().toString());
      }
    } catch (error) {
      console.error('Weather Fetch Error:', error);
    }
  }, [lattitude, longitude]);

  useEffect(() => {
    if (isSiteID) {
      fetchSiteInfoData(isSiteID);
    }
  }, [isSiteID]);
  useEffect(() => {
    if (siteInfo.length > 0) {
      const firstSite = siteInfo[0];
      if (firstSite) {
        setLattitude(parseFloat(firstSite.latitude) || 0);
        setLongitude(parseFloat(firstSite.longitude) || 0);
        setLocation(firstSite.Belt);
      }
    }
  }, [siteInfo]);
  useEffect(() => {
    if (siteInfo.length > 0 && lattitude && longitude) {
      fetchCurrentWeather();

      const interval = setInterval(() => {
        // Fetch in background before cache expires
        const fetchBackground = async () => {
          try {
            const cachedTime = await AsyncStorage.getItem('cachedTime');

            if (
              cachedTime &&
              Date.now() - parseInt(cachedTime) >= 4 * 60 * 1000
            ) {
              // If cache is about to expire, fetch in background
              await fetchCurrentWeather();
            }
          } catch (error) {
            console.error('Background fetch error: ', error);
          }
        };
        fetchBackground();
      }, 60 * 1000); // Check every minute

      return () => clearInterval(interval);
    }
  }, [fetchCurrentWeather, siteInfo, lattitude, longitude]);

  return (
    <BottomSheet
      snapPoints={snapPoints}
      enablePanDownToClose={true}
      backgroundStyle={{backgroundColor: themeStyles.background}}
      style={[styles.mainContainer, {borderColor: themeStyles.borderColor}]}
      handleIndicatorStyle={{backgroundColor: themeStyles.barBackground}}
      index={1}
      onClose={() => {
        setWeatherVisible(false);
      }}>
      <BottomSheetView style={styles.contentContainer}>
        <ScrollView contentContainerStyle={{paddingBottom: 40}}>
          <View style={styles.tooltip}>
            <View style={styles.heading}>
              <Text
                style={{
                  color: 'white',
                  fontSize: 20,
                  height: 'auto',
                  width: 'auto',
                  fontFamily: Fonts.BaiJamjuree_Bold,
                }}>
                {location || 'N/A'}
              </Text>
              <View style={styles.leftContent}>
                <Text
                  style={{
                    fontSize: 18,
                    fontFamily: Fonts.BaiJamjuree_SemiBold,
                    color: '#96999E',
                    textAlign: 'center',
                    width: 'auto',
                  }}>
                  {WeatherMaping[weatherCode] || 'N/A'}
                </Text>
              </View>
            </View>
            <View style={styles.temperatureContainer}>
              <Text
                style={[
                  styles.tooltipText,
                  {
                    color: themeStyles.textColor,
                    width: 'auto',
                    fontSize: 66,
                    fontFamily: Fonts.BaiJamjuree_Bold,
                    alignSelf: 'center',
                    marginLeft: '8%',
                  },
                ]}>
                {weather || '0'}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <Text
                  style={{
                    color: '#FFFFFF',
                    fontSize: 20,
                    fontFamily: Fonts.BaiJamjuree_Bold,
                  }}>
                  °C
                </Text>
                <Image
                  source={weather_icons[weatherCode]}
                  style={{
                    height: 40,
                    width: 56,
                    alignSelf: 'flex-end',
                    marginRight: '0%',
                    marginLeft: '3%',
                  }}></Image>
              </View>
            </View>

            <View style={styles.textContainer}>
              <PrecipitationCard
                weatherInfo={weatherInfo?.current?.precipitation}
              />
              <HumidityCard
                weatherInfo={weatherInfo?.current?.relative_humidity_2m || '0'}
              />
            </View>
            <View style={styles.textContainer}>
              <WindDetailsCard weatherInfo={weatherInfo} />
              <SunsetSunriseCard weatherInfo={weatherInfo} />
            </View>
          </View>
        </ScrollView>
      </BottomSheetView>
    </BottomSheet>
  );
};

export default WeatherInformationCard;

const styles = StyleSheet.create({
  tooltipWrapper: {
    position: 'absolute',
    top: '50%',
    left: '19%',
    transform: [{translateX: -75}, {translateY: -10}], // Adjust these values as needed
    zIndex: 1000,
    height: '500%',
    width: '100%',
    alignItems: 'center',
    marginTop: '4%',
  },
  heading: {
    height: '18%',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    width: '90%',
    // backgroundColor: 'white',
  },
  tooltip: {
    backgroundColor: '#0C121D',
    padding: 12,
    borderRadius: 8,
    minWidth: 230,
    height: '85%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    width: '100%',
    alignItems: 'center',
  },
  tooltipText: {
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
    paddingVertical: 2,
  },

  textContainer: {
    height: '30%',
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '12%',
  },
  leftContent: {
    justifyContent: 'center',
    marginTop: '5%',
    height: '30%',
  },

  temperatureContainer: {
    height: '20%',
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: '-5%',

    // backgroundColor: 'white',
  },
  background: {
    backgroundColor: '',
  },
  Header: {
    alignItems: 'center',
  },

  title: {
    fontSize: 16,

    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },

  headerIndicator: {
    height: 5,
    width: 44,
    marginTop: 8,
  },
  container: {
    height: 217,
    width: '100%',
  },
  map: {
    borderRadius: 10,
  },
  siteLoadContainer: {
    height: '70%',
    width: '100%',

    marginTop: '10%',
  },
  siteLoad: {
    height: '8%',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '-9',
    justifyContent: 'space-between', // Align items to both ends
    paddingHorizontal: 20, // Add padding for spacing
  },
  siteLoadLabel: {
    fontSize: 14,
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_Medium,
    flex: 1, // Allow flexible width
    height: '80%',
  },

  mainContainer: {
    flex: 1,
    marginHorizontal: '2.5%',
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 15,
  },
  contentContainer: {
    height: '100%',
  },
});
