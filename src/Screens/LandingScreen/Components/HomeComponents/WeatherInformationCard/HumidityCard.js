import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Svg, {Path} from 'react-native-svg';
import {Fonts} from '../../../../../styles/fonts';
import FillingContainer from '../../../../../assets/svgs/FillingContainer';
import HumidityIcon from '../../../../../assets/svgs/HumidityIcon';

const HumidityCard = ({weatherInfo}) => {
  const fillHeight = (weatherInfo / 100) * 160;

  return (
    <View style={styles.container}>
      <View style={styles.background} />

      <View style={[styles.fillContainer, {height: fillHeight}]}>
        <View style={styles.svgContainer}>
          <FillingContainer
            height={fillHeight}
            width={170} // Fixed width matching container
          />
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.header}>
          <HumidityIcon />
          <Text style={styles.title}>Humidity</Text>
        </View>

        <Text style={styles.percentage}>{weatherInfo}%</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 170,
    height: 160,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#252A3480',
    alignItems: 'center',
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: '#252A3480',
  },
  fillContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    borderRadius: 20,
    alignItems: 'center',
  },
  gradient: {
    flex: 1,
    opacity: 0.9,
  },
  content: {
    padding: 6,
    justifyContent: 'space-between',
    zIndex: 1,
    width: '100%',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',

    width: '100%',

    height: '30%',
    marginTop: '10%',
  },
  icon: {
    fontSize: 16,
    marginRight: 8
  },
  title: {
    color: '#FFFFFF',
    fontSize: 16,

    fontFamily: Fonts.BaiJamjuree_SemiBold,
    marginLeft: '7%',
  },
  percentage: {
    color: '#FFFFFF',
    fontSize: 35,
    
    textAlign: 'left',
    fontFamily: Fonts.BaiJamjuree_Bold,
    marginTop: '2%',
    width: '90%',
  },
  dewPoint: {
    color: '#FFFFFF',
    fontSize: 14,
    opacity: 0.8,
  },
  svgContainer: {
    width: '100%',
    height: '100%',
  },
});

export default HumidityCard;
