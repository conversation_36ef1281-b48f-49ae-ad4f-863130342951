import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Fonts} from '../../../../../styles/fonts';
import WindSvg from '../../../../../assets/svgs/WindSvg';
import WindDirectionSvg from '../../../../../assets/svgs/WindDirectionSvg';

const WindDetailsCard = ({weatherInfo}) => {
  const getWindDirectionName = degree => {
    if (degree == null || isNaN(degree)) return 'Unknown';

    const directions = [
      'N',
      'NNE',
      'NE',
      'ENE',
      'E',
      'ESE',
      'SE',
      'SSE',
      'S',
      'SSW',
      'SW',
      'WSW',
      'W',
      'WNW',
      'NW',
      'NNW',
    ];

    const index = Math.round(degree / 22.5) % 16;
    return directions[index];
  };
  return (
    <View style={styles.container}>
      <WindDirectionSvg
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          top: '15%',
          left: '17%',
          transform: [
            {rotate: `${weatherInfo?.current?.wind_direction_10m ?? 0}deg`},
          ],
        }}
      />

      <View style={styles.content}>
        <View style={styles.header}>
          <WindSvg />
          <Text style={styles.title}>Wind</Text>
        </View>
        <Text style={styles.percentage}>
          {weatherInfo?.current?.wind_speed_10m} mph
        </Text>
        <View style={styles.footer}>
          <Text
            style={{
              height: '100%',
              color: '#96999E',
              fontFamily: Fonts.BaiJamjuree_Regular,
              fontSize: 12,
            }}>
            From{' '}
            {getWindDirectionName(weatherInfo?.current?.wind_direction_10m)}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default WindDetailsCard;

const styles = StyleSheet.create({
  container: {
    width: 170,
    height: 170,
    borderRadius: 90,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#252A3480',
    justifyContent: 'center',
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    // backgroundColor: '#FFFFFF1A',
  },
  fillContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    borderRadius: 20,
  },
  gradient: {
    flex: 1,
    opacity: 0.9,
  },
  content: {
    padding: 10,

    flexDirection: 'column',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    height: '20%',
    marginTop: '10%',
    width: '50%',
  },
  icon: {
    fontSize: 16,
    marginRight: 8,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  percentage: {
    color: '#FFFFFF',
    fontSize: 30,
    height: '35%',
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    textAlign: 'left',
    marginLeft: '6%',
    width: 'auto',
  },
  footer: {
    width: '100%',
    height: '25%',
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginTop: '16%',
  },
  compassContainer: {},
});
