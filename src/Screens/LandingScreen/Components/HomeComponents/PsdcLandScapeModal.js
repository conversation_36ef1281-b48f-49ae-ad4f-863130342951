import {
  BackHandler,
  Dimensions,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  StatusBar,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import PowerCycleChart from '../../../Components/Charts/PowerCycleChart';
import CrossSvg from '../../../../assets/svgs/CrossSvg';
import {Fonts} from '../../../../styles/fonts';
import MenuSvg from '../../../../assets/svgs/MenuSvg';
import HourKwh from './HourKwh';
import DataFilter from './DataFilter';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {useNavigation} from '@react-navigation/native';
import Orientation from 'react-native-orientation-locker';

const PsdcLandScapeModal = ({setPsdcModal}) => {
  const {width: screenWidth, height: screenHeight} = Dimensions.get('window');
  const [utilization, setUtilization] = useState('hrs');
  const navigation = useNavigation();
  const [selectedItem, setSelectedItem] = useState({
    id: '2',
    title: 'Weekly',
    name: 'Weekly',
  });
  const themeStyles = useThemeStyles();

  useEffect(() => {
    // Lock to landscape orientation when modal opens
    Orientation.lockToLandscape();
    StatusBar.setHidden(true);

    return () => {
      // Reset to portrait orientation when modal closes
      Orientation.lockToPortrait();
      StatusBar.setHidden(false);
    };
  }, []);

  useEffect(() => {
    const handleBackButtonPress = () => {
      setPsdcModal(false);
      return true;
    };

    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        event.preventDefault();
        setPsdcModal(false);
      },
    );

    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation, setPsdcModal]);

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={true}
      onRequestClose={() => setPsdcModal(false)}
      supportedOrientations={['landscape']}>
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.modalContainer,
            {backgroundColor: themeStyles.background},
          ]}>
          <View style={styles.contentContainer}>
            <View style={styles.headerContainer}>
              <Text style={styles.headerText}>Sources Utilization</Text>
              <TouchableOpacity
                onPress={() => setPsdcModal(false)}
                style={[
                  styles.closeButton,
                  {backgroundColor: themeStyles.cardBackground},
                ]}>
                <CrossSvg color={themeStyles.iconColor} />
              </TouchableOpacity>
              <MenuSvg style={styles.menuIcon} />
            </View>

            <View style={styles.controlsContainer}>
              <HourKwh setUtilization={setUtilization} isLandscape={true} />
              <DataFilter
                setSelectedItem={setSelectedItem}
                isLandscape={true}
                modalHeight={screenHeight}
              />
            </View>

            <View style={styles.chartContainer}>
              <PowerCycleChart
                selectedItem={selectedItem}
                utilization={utilization}
                isLandscape={true}
              />
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default PsdcLandScapeModal;

const styles = StyleSheet.create({
  modalOverlay: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 25,
    marginLeft: '5%',
    height: '98%',
    width: '90%',
  },
  modalContainer: {
    width: '110%',
    height: '100%',
    borderRadius: 25,
    padding: 16,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 60,
  },
  headerText: {
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },
  closeButton: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 'auto',

    position: 'absolute',
    right: '52.5%',
    bottom: '60%',
  },
  menuIcon: {
    marginRight: 8,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',

    height: '20%',
    marginTop: '-2%',
  },
  chartContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
});
