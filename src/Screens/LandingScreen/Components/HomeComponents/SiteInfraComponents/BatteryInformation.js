import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import BatteryInfraSvg from '../../../../../assets/svgs/BatteryInfraSvg';
import FontFamily from '../../../../../assets/styles/FontFamily';
import InfraSvg from '../../../../../assets/svgs/InfraSvg';
import GreenDotSvg from '../../../../../assets/svgs/GreenDotSvg';
import VerticalSplitter from '../../../../../assets/svgs/VerticalSplitter';
import Splitter from '../../../../../assets/svgs/Splitter';
import {Fonts} from '../../../../../styles/fonts';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BatteriesWhiteSvg from '../../../../../assets/svgs/BatteriesWhiteSvg';
import {formatDate} from '../../../../../Constants/FormateDate';
import {formatNumber} from '../../../../../Constants/FomateNumber';
import {processAndGroupData} from '../../../../../Constants/PowerFlowMaping';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {ScrollView} from 'react-native-gesture-handler';

const BatteryInformation = ({data, powerFlow}) => {
  const themeStyles = useThemeStyles();

  const bt_V =
    powerFlow &&
    powerFlow.length > 0 &&
    powerFlow[0]?.batteryvol !== null &&
    !isNaN(powerFlow[0]?.batteryvol)
      ? parseFloat(powerFlow[0].batteryvol)
      : null;

  const bt_C =
    powerFlow &&
    powerFlow.length > 0 &&
    powerFlow[0]?.batterycurr !== null &&
    !isNaN(powerFlow[0]?.batterycurr) &&
    powerFlow[0].batterycurr >= -1000
      ? parseFloat(powerFlow[0].batterycurr)
      : null;
  const voltageValues = useMemo(() => {
    if (!data || !powerFlow || !powerFlow[0]) return null;

    if (
      powerFlow &&
      powerFlow[0] &&
      powerFlow[0]?.batteryvoltages &&
      powerFlow[0]?.batteryvoltages !== null
    ) {
      return processAndGroupData(powerFlow[0].batteryvoltages, 4);
    } else {
      return ['---'];
    }
  }, [powerFlow]);

  const currentValues = useMemo(() => {
    if (!data || !powerFlow || !powerFlow[0]) return null;

    if (
      powerFlow &&
      powerFlow[0] &&
      powerFlow[0]?.otherparam &&
      powerFlow[0]?.otherparam?.battery_currents
    ) {
      return processAndGroupData(powerFlow[0].otherparam.battery_currents, 4);
    } else {
      return ['---'];
    }
  }, [powerFlow]);

  const socValues = useMemo(() => {
    if (!data || !powerFlow || !powerFlow[0]) return null;

    if (
      powerFlow &&
      powerFlow[0] &&
      powerFlow[0]?.otherparam &&
      powerFlow[0]?.otherparam?.battsoc_info
    ) {
      return processAndGroupData(powerFlow[0].otherparam.battsoc_info, 4);
    } else {
      return ['---'];
    }
  }, [powerFlow]);

  const formatDecimal = value => {
    if (!value && value !== 0) return '0';
    const num = Number(value);
    // Check if the number has a decimal part
    return Number.isInteger(num) ? num.toFixed(0) : num.toFixed(1);
  };
  return (
    <View style={{alignItems: 'center'}}>
      <View style={styles.Header}>
        <Text style={[styles.title, FontFamily.textMedium]}>
          Battery Information
        </Text>
      </View>
      {!themeStyles.svgsTheme ? (
        <BatteryInfraSvg style={{marginTop: '3%'}} />
      ) : (
        <BatteriesWhiteSvg style={{marginTop: '3%'}} />
      )}

      <View style={styles.powerContainer}>
        <ScrollView
          style={{width: '98%', maxHeight: 130}}
          contentContainerStyle={{paddingVertical: 5}}
          showsVerticalScrollIndicator={true}
          bounces={true}
          alwaysBounceVertical={true}
          persistentScrollbar={true}
          nestedScrollEnabled={true}>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              {bt_C >= -5 && bt_C <= 5
                ? 'Charged'
                : bt_C > 5
                ? 'Charging'
                : bt_C < -5 && bt_C > -1000
                ? 'Discharging'
                : 'Load'}
              (kW)
            </Text>
            <Text
              style={[
                styles.powerValueText,
                FontFamily.textMedium,
                {color: themeStyles.textColor},
              ]}>
              {powerFlow[0]?.Battery > 0
                ? powerFlow[0]?.Battery?.toFixed(2)
                : bt_C == null ||
                  bt_V == null ||
                  Math.abs(((bt_C ?? 0) * (bt_V ?? 0)) / 1000).toFixed(2) ===
                    '0.00'
                ? '---'
                : Math.abs(((bt_C ?? 0) * (bt_V ?? 0)) / 1000).toFixed(2)}
            </Text>
          </View>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              Current (A)
            </Text>

            {currentValues.map((line, index) => (
              <Text
                key={index}
                style={[
                  styles.powerValueText,
                  FontFamily.textMedium,
                  {color: themeStyles.textColor},
                ]}>
                {line}
              </Text>
            ))}
          </View>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              Voltage (V)
            </Text>
            {voltageValues.map((line, index) => (
              <Text
                key={index}
                style={[
                  styles.powerValueText,
                  FontFamily.textMedium,
                  {color: themeStyles.textColor},
                ]}>
                {line}
              </Text>
            ))}
          </View>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              SOC (%)
            </Text>
            {socValues.map((line, index) => (
              <Text
                key={index}
                style={[
                  styles.powerValueText,
                  FontFamily.textMedium,
                  {color: themeStyles.textColor},
                ]}>
                {line}
              </Text>
            ))}
          </View>
        </ScrollView>
      </View>
      <Splitter color={themeStyles.splitterColor} style={{marginTop: '-4%'}} />
      <View style={styles.infraContainer}>
        <View style={styles.infraStatus}>
          <View style={styles.InfraStatusLogo}>
            <InfraSvg style={{marginLeft: '5%'}} />
            <Text style={[styles.infraStatusText, FontFamily.textMedium]}>
              Infra Status
            </Text>
          </View>
          <View style={styles.infraSize}>
            <GreenDotSvg
              color={data.color}
              style={{
                marginLeft: 'auto', // Push to the right

                alignItems: 'center',
              }}
            />
            <Text
              style={[
                styles.infraSizeText,
                FontFamily.textMedium,
                {color: data.color},
              ]}>
              {data.size}
            </Text>
          </View>
        </View>
        <View style={[styles.infoContainer]}>
          <View
            style={[
              styles.assetInformation,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={[
                styles.assetInfoText,
                FontFamily.textMedium,
                {color: themeStyles.textColor},
              ]}>
              {data.batterybrand}
            </Text>
            <View style={styles.rated}>
              <View style={styles.ratedView}>
                <Text style={styles.ratedText}>Rated</Text>
                <View style={styles.ratedValue}>
                  <Text
                    style={{
                      fontSize: 25,
                      fontFamily: Fonts.BaiJamjuree_Bold,
                      color: themeStyles.textColor,
                    }}>
                    {formatDecimal(data.load)}
                  </Text>
                  <Text
                    style={{
                      fontSize: 15,
                      fontFamily: Fonts.BaiJamjuree_Medium,
                      color: themeStyles.textColor,
                      marginLeft: '5%',
                      marginTop: '8%',
                    }}>
                    {data.unit}
                  </Text>
                </View>
              </View>
              <VerticalSplitter
                color={themeStyles.splitterColor}
                style={{marginLeft: '4%'}}
              />
              <View style={[styles.ratedView, {marginLeft: '7%'}]}>
                <Text style={styles.ratedText}>Effective</Text>
                <View style={styles.ratedValue}>
                  <Text
                    style={{
                      fontSize: 25,
                      fontFamily: Fonts.BaiJamjuree_Bold,
                      color: themeStyles.textColor,
                    }}>
                    {formatDecimal(data.batteryeffectivecapacity)}
                  </Text>
                  <Text
                    style={{
                      fontSize: 15,
                      fontFamily: Fonts.BaiJamjuree_Medium,
                      color: themeStyles.textColor,
                      marginLeft: '5%',
                      marginTop: '8%',
                    }}>
                    {data.unit}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.installationDateView}>
              <Text
                style={{
                  fontSize: 12,
                  color: '#96999E',
                  fontFamily: Fonts.BaiJamjuree_Medium,
                }}>
                Installed on:
              </Text>
              <Text
                style={{
                  fontSize: 12,
                  color: '#96999E',
                  fontFamily: Fonts.BaiJamjuree_Medium,

                  marginLeft: '3%',
                }}>
                {formatDate(data.installationDate)}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.footer}>
          <View
            style={[
              styles.footer1,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={{
                fontSize: 15,
                color: '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: '5%',
              }}>
              SOH (%)
            </Text>
            <Text
              style={{
                fontSize: 15,
                color: themeStyles.textColor,
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: 'auto',
                marginRight: '8%',
              }}>
              {Number(data.soh).toFixed(0)}
            </Text>
          </View>
          <View
            style={[
              styles.footer2,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={{
                fontSize: 15,
                color: '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: '5%',
              }}>
              Cycles
            </Text>
            <Text
              style={{
                fontSize: 15,
                color: themeStyles.textColor,
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: 'auto',
                marginRight: '8%',
              }}>
              {formatNumber(data.cycle)}
              <Text>/6000</Text>
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default BatteryInformation;

const styles = StyleSheet.create({
  Header: {
    alignItems: 'center',
    marginTop: '2%',
  },

  title: {
    fontSize: 16,
    color: '#96999E',
  },

  headerIndicator: {
    height: 5,
    width: 44,
  },
  powerContainer: {
    height: '30%',
    marginTop: '1%',
    width: '98%',
    alignItems: 'center',
    zIndex: 100,
  },
  generating: {
    minHeight: '2%',
    width: '98%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: '-2%',
    flexWrap: 'wrap',
    paddingVertical: 5,
  },
  powerText: {
    fontSize: 15,
    color: '#96999E',
    textAlign: 'left',
  },
  powerValueText: {
    fontSize: 15,
    color: '#FFFFFF',
    textAlign: 'right',
    width: '70%',
    flexShrink: 1,
    flexWrap: 'wrap',
    flexGrow: 1,
    alignSelf: 'flex-start',
  },
  infraContainer: {
    width: '98%',
    height: '22%',
    marginTop: '1%',
  },
  infraStatus: {
    height: '24%',
    width: '98%',

    marginTop: '5%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infraStatusText: {
    fontSize: 16,
    color: '#96999E',
    marginLeft: '4%',
  },
  InfraStatusLogo: {
    height: '100%',
    width: '50%',

    alignItems: 'center',
    flexDirection: 'row',
  },
  infraSize: {
    height: '100%',
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end', // Align to the end
  },
  greenDotStyle: {
    alignSelf: 'center', // Vertically center
    marginLeft: 5, // Add a small margin to separate text and dot
  },
  infraSizeText: {
    fontSize: 16,
    textAlign: 'right',
    width: 'auto', // Adjust to content width
    marginRight: 0, // Remove right margin
  },
  infoContainer: {
    width: '99%',
    height: '210%',
    marginTop: '3%',

    alignItems: 'center',
  },
  assetInformation: {
    height: '63%',

    width: '100%',
    backgroundColor: '#252A3480',
    borderRadius: 4,
  },
  assetInfoText: {
    fontSize: 15,

    color: '#FFFFFF',
    marginTop: '2%',
    marginLeft: '5%',
  },
  rated: {
    height: '50%',
    width: '100%',
    marginTop: '1%',

    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: '5.3%',
  },
  ratedView: {
    height: '90%',
    width: '40%',
  },
  ratedText: {
    fontSize: 15,
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  ratedValue: {
    height: '70%',
    width: '98%',

    flexDirection: 'row',
  },
  installationDateView: {
    height: '15%',
    width: '80%',

    marginLeft: '5%',
    flexDirection: 'row',
  },
  footer: {
    height: '57%',
    width: '100%',

    marginTop: '-22%',
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  footer1: {
    height: '100%',

    width: '47%',
    backgroundColor: '#252A3480',
    borderRadius: 4,
    alignItems: 'center',
    flexDirection: 'row',
  },
  footer2: {
    height: '100%',

    width: '47%',
    backgroundColor: '#252A3480',

    borderRadius: 4,
    alignItems: 'center',

    flexDirection: 'row',
    marginLeft: '3%',
  },
});
