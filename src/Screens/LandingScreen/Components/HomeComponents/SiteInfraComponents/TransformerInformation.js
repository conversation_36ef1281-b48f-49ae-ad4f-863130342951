import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import BatteryInfraSvg from '../../../../../assets/svgs/BatteryInfraSvg';
import FontFamily from '../../../../../assets/styles/FontFamily';
import InfraSvg from '../../../../../assets/svgs/InfraSvg';
import GreenDotSvg from '../../../../../assets/svgs/GreenDotSvg';
import VerticalSplitter from '../../../../../assets/svgs/VerticalSplitter';
import Splitter from '../../../../../assets/svgs/Splitter';
import {Fonts} from '../../../../../styles/fonts';
import TransformerInfoSvg from '../../../../../assets/svgs/TransformerInfoSvg';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TransformerWhiteSvg from '../../../../../assets/svgs/TransformerWhiteSvg';
import {color} from 'echarts';
import {formatDate} from '../../../../../Constants/FormateDate';
import {processAndGroupDataForPu} from '../../../../../Constants/PowerFlowMaping';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';

const TransformerInformation = ({data, powerFlow}) => {
  const themeStyles = useThemeStyles();

  const acVoltages = useMemo(() => {
    if (powerFlow && powerFlow[0] && powerFlow[0].sourceTag === 'Main') {
      const voltages = [
        powerFlow[0]['AC VOLT P1'],
        powerFlow[0]['AC VOLT P2'],
        powerFlow[0]['AC VOLT P3'],
      ];
      return voltages.map(v =>
        v !== undefined && Number(v) >= 0 ? Number(v).toFixed(2) : '---',
      );
    }
    return ['---'];
  }, [powerFlow]);

  const groupedACVoltages = useMemo(() => {
    if (acVoltages && acVoltages[0] !== '---') {
      return processAndGroupDataForPu({'AC VOLT': acVoltages}, 4);
    } else {
      return ['---'];
    }
  }, [acVoltages]);

  const acCurrents = useMemo(() => {
    if (powerFlow && powerFlow[0] && powerFlow[0].sourceTag === 'Main') {
      const current = [
        powerFlow[0]['AC Curr P1'],
        powerFlow[0]['AC Curr P2'],
        powerFlow[0]['AC Curr P3'],
      ];

      return current.map(a =>
        a !== undefined && Number(a) >= 0 ? Number(a).toFixed(2) : '---',
      );
    }
  }, [powerFlow]);

  const groupedAcCurrents = useMemo(() => {
    if (acCurrents && acCurrents[0] !== '---') {
      return processAndGroupDataForPu({'AC Curr': acCurrents}, 4);
    } else {
      return ['---'];
    }
  }, [acCurrents]);
  const formatDecimal = value => {
    if (!value && value !== 0) return '0';
    const num = Number(value);
    // Check if the number has a decimal part
    return Number.isInteger(num) ? num.toFixed(0) : num.toFixed(1);
  };
  return (
    <View style={{alignItems: 'center'}}>
      <View style={styles.Header}>
        <Text style={[styles.title, FontFamily.textMedium]}>
          Transformer Information
        </Text>
      </View>
      {!themeStyles.svgsTheme ? (
        <TransformerInfoSvg style={{marginTop: '3%'}} />
      ) : (
        <TransformerWhiteSvg style={{marginTop: '3%'}} />
      )}

      <View style={styles.powerContainer}>
        <View style={styles.generating}>
          <Text style={[styles.powerText, FontFamily.textMedium]}>Load</Text>
          <Text
            style={[
              styles.powerValueText,
              FontFamily.textMedium,
              {color: themeStyles.textColor},
            ]}>
            {powerFlow && powerFlow[0]?.Main !== undefined
              ? Number(powerFlow[0]?.Main).toFixed(2)
              : '---'}
          </Text>
        </View>
        <View style={styles.generating}>
          <Text style={[styles.powerText, FontFamily.textMedium]}>
            Current (A)
          </Text>
          {groupedAcCurrents.map((line, index) => (
            <Text
              key={index}
              style={[
                styles.powerValueText,
                FontFamily.textMedium,
                {color: themeStyles.textColor},
              ]}>
              {line}
              {'\n'}
            </Text>
          ))}
        </View>
        <View style={styles.generating}>
          <Text style={[styles.powerText, FontFamily.textMedium]}>
            Voltage (V)
          </Text>
          {groupedACVoltages.map((line, index) => (
            <Text
              key={index}
              style={[
                styles.powerValueText,
                FontFamily.textMedium,
                {
                  color: themeStyles.textColor,
                  flexWrap: 'wrap',
                  width: '70%',
                  textAlign: 'right',
                  alignSelf: 'flex-end',
                },
              ]}>
              {line}
            </Text>
          ))}
        </View>
      </View>
      <Splitter
        color={themeStyles.splitterColor}
        style={{marginTop: '-3.5%'}}
      />
      <View style={styles.infraContainer}>
        <View style={styles.infraStatus}>
          <View style={styles.InfraStatusLogo}>
            <InfraSvg style={{marginLeft: '5%'}} />
            <Text style={[styles.infraStatusText, FontFamily.textMedium]}>
              Infra Status
            </Text>
          </View>
          <View style={styles.infraSize}>
            <GreenDotSvg
              color={data.color}
              style={{
                marginLeft: 'auto', // Push to the right

                alignItems: 'center',
              }}
            />

            <Text
              style={[
                styles.infraSizeText,
                FontFamily.textMedium,
                {color: themeStyles.textColor},
              ]}>
              {data.size}
            </Text>
          </View>
        </View>
        <View style={[styles.infoContainer]}>
          <View
            style={[
              styles.assetInformation,
              ,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={[
                styles.assetInfoText,
                FontFamily.textMedium,
                {color: themeStyles.textColor},
              ]}>
              {data.tfcompany}
            </Text>
            <View style={styles.rated}>
              <View style={styles.ratedView}>
                <Text style={styles.ratedText}>Rated</Text>
                <View style={styles.ratedValue}>
                  <Text
                    style={{
                      fontSize: 25,
                      fontFamily: Fonts.BaiJamjuree_Bold,
                      color: themeStyles.textColor,
                    }}>
                    {formatDecimal(data.load)}
                  </Text>
                  <Text
                    style={{
                      fontSize: 15,
                      fontFamily: Fonts.BaiJamjuree_Medium,
                      color: themeStyles.textColor,
                      marginLeft: '5%',
                      marginTop: '8%',
                    }}>
                    {data.unit}
                  </Text>
                </View>
              </View>
              <VerticalSplitter
                color={themeStyles.splitterColor}
                style={{marginLeft: '4%'}}
              />
              <View style={[styles.ratedView, {marginLeft: '7%'}]}>
                <Text style={styles.ratedText}>Effective</Text>
                <View style={styles.ratedValue}>
                  <Text
                    style={{
                      fontSize: 25,
                      fontFamily: Fonts.BaiJamjuree_Bold,
                      color: themeStyles.textColor,
                    }}>
                    ---
                  </Text>
                  <Text
                    style={{
                      fontSize: 15,
                      fontFamily: Fonts.BaiJamjuree_Medium,
                      color: themeStyles.textColor,
                      marginLeft: '5%',
                      marginTop: '8%',
                    }}>
                    {data.unit}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.installationDateView}>
              <Text
                style={{
                  fontSize: 12,
                  color: '#96999E',
                  fontFamily: Fonts.BaiJamjuree_Medium,
                }}>
                Installed on:
              </Text>
              <Text
                style={{
                  fontSize: 12,
                  color: '#96999E',
                  fontFamily: Fonts.BaiJamjuree_Medium,
                  marginLeft: '3%',
                }}>
                {formatDate(data.installationDate)}
              </Text>
            </View>
          </View>
        </View>
        <View style={[styles.footer]}>
          <View
            style={[
              styles.footer1,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={{
                fontSize: 15,
                color: '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: '5%',
              }}>
              Loading (%)
            </Text>
            <Text
              style={{
                fontSize: 15,
                color: themeStyles.textColor,
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: 'auto',
                marginRight: '8%',
              }}>
              {Number(data.tfloading).toFixed(0)}
            </Text>
          </View>
          <View
            style={[
              styles.footer2,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={{
                fontSize: 15,
                color: '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: '5%',
              }}>
              Phase
            </Text>
            <Text
              style={{
                fontSize: 15,
                color: themeStyles.textColor,
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: 'auto',
                marginRight: '8%',
              }}>
              {Number(data.phaseworknum).toFixed(0)}/3
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default TransformerInformation;

const styles = StyleSheet.create({
  Header: {
    alignItems: 'center',
    marginTop: '2%',
  },

  title: {
    fontSize: 16,

    color: '#96999E',
  },

  headerIndicator: {
    height: 5,
    width: 44,
  },
  powerContainer: {
    height: '30%',
    marginTop: '1%',

    width: '98%',
    alignItems: 'center',
    // backgroundColor: 'white',
  },
  generating: {
    minHeight: '2%',
    width: '98%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: '-2%',
    flexWrap: 'wrap',
    paddingVertical: 5,
  },
  powerText: {
    fontSize: 15,

    color: '#96999E',
    textAlign: 'left',
  },
  powerValueText: {
    fontSize: 15,
    color: '#FFFFFF',
    textAlign: 'right',
    width: '70%',
    flexShrink: 1,
    flexWrap: 'wrap',
    flexGrow: 1,
    alignSelf: 'flex-start',
  },
  infraContainer: {
    width: '98%',
    height: '22%',
    marginTop: '1%',
  },
  infraStatus: {
    height: '24%',
    width: '98%',

    marginTop: '5%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infraStatusText: {
    fontSize: 16,
    color: '#96999E',
    marginLeft: '4%',
  },
  InfraStatusLogo: {
    height: '100%',
    width: '50%',

    alignItems: 'center',
    flexDirection: 'row',
  },
  infraSize: {
    height: '100%',
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end', // Align to the end
  },
  greenDotStyle: {
    alignSelf: 'center', // Vertically center
    marginLeft: 5, // Add a small margin to separate text and dot
  },
  infraSizeText: {
    fontSize: 16,
    textAlign: 'right',
    width: 'auto', // Adjust to content width
    marginRight: 0, // Remove right margin
  },
  infoContainer: {
    width: '98%',
    height: '210%',
    marginTop: '3%',

    alignItems: 'center',
  },
  assetInformation: {
    height: '63%',

    width: '100%',
    backgroundColor: '#252A3480',
    borderRadius: 4,
  },
  assetInfoText: {
    fontSize: 15,

    color: '#FFFFFF',
    marginTop: '2%',
    marginLeft: '5%',
  },
  rated: {
    height: '50%',
    width: '100%',
    marginTop: '1%',

    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: '5.3%',
  },
  ratedView: {
    height: '90%',
    width: '40%',
  },
  ratedText: {
    fontSize: 15,
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  ratedValue: {
    height: '70%',
    width: '98%',
    marginTop: '0%',

    flexDirection: 'row',
  },
  installationDateView: {
    height: '15%',
    width: '80%',

    marginLeft: '5%',
    flexDirection: 'row',
  },
  footer: {
    height: '57%',
    width: '100%',

    marginTop: '-23%',
    flexDirection: 'row',
  },
  footer1: {
    height: '100%',

    width: '47%',
    backgroundColor: '#252A3480',
    borderRadius: 4,
    alignItems: 'center',
    flexDirection: 'row',
  },
  footer2: {
    height: '100%',

    width: '47%',
    backgroundColor: '#252A3480',

    borderRadius: 4,
    alignItems: 'center',

    flexDirection: 'row',
    marginLeft: '3%',
  },
});
