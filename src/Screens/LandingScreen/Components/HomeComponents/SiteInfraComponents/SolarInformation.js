import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import BatteryInfraSvg from '../../../../../assets/svgs/BatteryInfraSvg';
import FontFamily from '../../../../../assets/styles/FontFamily';
import InfraSvg from '../../../../../assets/svgs/InfraSvg';
import GreenDotSvg from '../../../../../assets/svgs/GreenDotSvg';
import VerticalSplitter from '../../../../../assets/svgs/VerticalSplitter';
import Splitter from '../../../../../assets/svgs/Splitter';
import {Fonts} from '../../../../../styles/fonts';
import SolarBacSvg from '../../../../../assets/svgs/SolarBacSvg';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SolarWhiteSvg from '../../../../../assets/svgs/SolarWhiteSvg';
import {formatDate} from '../../../../../Constants/FormateDate';
import {processAndGroupData} from '../../../../../Constants/PowerFlowMaping';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import {ScrollView} from 'react-native-gesture-handler';

const SolarInformation = ({data = {}, powerFlow = []}) => {
  const themeStyles = useThemeStyles();

  const currentValues = useMemo(() => {
    try {
      if (powerFlow && powerFlow[0] && powerFlow[0].otherparam) {
        return (
          processAndGroupData(powerFlow[0].otherparam.puoutcurr || [], 4) || [
            '---',
          ]
        );
      }
      return ['---'];
    } catch (error) {
      return ['---'];
    }
  }, [powerFlow]);

  const inputVoltagesValues = useMemo(() => {
    try {
      if (powerFlow && powerFlow[0] && powerFlow[0].otherparam) {
        return (
          processAndGroupData(powerFlow[0].otherparam.puinputvolt || [], 4) || [
            '---',
          ]
        );
      }
      return ['---'];
    } catch (error) {
      return ['---'];
    }
  }, [powerFlow]);

  const outputVoltagesValues = useMemo(() => {
    try {
      if (powerFlow && powerFlow[0] && powerFlow[0].otherparam) {
        return (
          processAndGroupData(
            powerFlow[0].otherparam.puoutputvolt || [],
            4,
          ) || ['---']
        );
      }
      return ['---'];
    } catch (error) {
      return ['---'];
    }
  }, [powerFlow]);
  const formatDecimal = value => {
    if (!value && value !== 0) return '0';
    const num = Number(value);
    // Check if the number has a decimal part
    return Number.isInteger(num) ? num.toFixed(0) : num.toFixed(1);
  };
  return (
    <View style={{alignItems: 'center'}}>
      <View style={styles.Header}>
        <Text style={[styles.title, FontFamily.textMedium]}>
          Solar Information
        </Text>
      </View>
      {!themeStyles.svgsTheme ? (
        <SolarBacSvg style={{marginTop: '3%'}} />
      ) : (
        <SolarWhiteSvg style={{marginTop: '3%'}} />
      )}

      <View style={styles.powerContainer}>
        <ScrollView
          style={{width: '98%', maxHeight: 130}}
          contentContainerStyle={{paddingVertical: 5}}
          showsVerticalScrollIndicator={true}
          bounces={true}
          alwaysBounceVertical={true}
          persistentScrollbar={true}
          nestedScrollEnabled={true}>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              Generating (kW)
            </Text>
            <Text
              style={[
                styles.powerValueText,
                FontFamily.textMedium,
                {color: themeStyles.textColor},
              ]}>
              {powerFlow && powerFlow[0]?.Solar !== undefined
                ? Number(powerFlow[0]?.Solar).toFixed(2)
                : '---'}
            </Text>
          </View>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              Current (A)
            </Text>
            {currentValues.map((line, index) => (
              <Text
                key={index}
                style={[
                  styles.powerValueText,
                  FontFamily.textMedium,
                  {color: themeStyles.textColor},
                ]}>
                {line}
                {'\n'}
              </Text>
            ))}
          </View>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              Out Voltage (V)
            </Text>
            {outputVoltagesValues.map((line, index) => (
              <Text
                key={index}
                style={[
                  styles.powerValueText,
                  FontFamily.textMedium,
                  {color: themeStyles.textColor},
                ]}>
                {line}
                {'\n'}
              </Text>
            ))}
          </View>
          <View style={styles.generating}>
            <Text style={[styles.powerText, FontFamily.textMedium]}>
              {' '}
              In Voltage (V)
            </Text>
            {inputVoltagesValues.map((line, index) => (
              <Text
                key={index}
                style={[
                  styles.powerValueText,
                  FontFamily.textMedium,
                  {color: themeStyles.textColor},
                ]}>
                {line}
                {'\n'}
              </Text>
            ))}
          </View>
        </ScrollView>
      </View>
      <Splitter color={themeStyles.splitterColor} style={{marginTop: '-4%'}} />
      <View style={styles.infraContainer}>
        <View style={styles.infraStatus}>
          <View style={styles.InfraStatusLogo}>
            <InfraSvg style={{marginLeft: '5%'}} />
            <Text style={[styles.infraStatusText, FontFamily.textMedium]}>
              Infra Status
            </Text>
          </View>
          <View style={styles.infraSize}>
            <GreenDotSvg
              color={data.color}
              style={{
                marginLeft: 'auto', // Push to the right

                alignItems: 'center',
              }}
            />
            <Text
              style={[
                styles.infraSizeText,
                FontFamily.textMedium,
                {color: data.color},
              ]}>
              {data.size}
            </Text>
          </View>
        </View>
        <View style={styles.infoContainer}>
          <View
            style={[
              styles.assetInformation,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={[
                styles.assetInfoText,
                FontFamily.textMedium,
                {color: themeStyles.textColor},
              ]}>
              {data.solarchargecontroller}
            </Text>
            <View style={styles.rated}>
              <View style={styles.ratedView}>
                <Text style={styles.ratedText}>Rated</Text>
                <View style={styles.ratedValue}>
                  <Text
                    style={{
                      fontSize: 25,
                      fontFamily: Fonts.BaiJamjuree_Bold,
                      color: themeStyles.textColor,
                    }}>
                    {formatDecimal(data.load)}
                  </Text>
                  <Text
                    style={{
                      fontSize: 15,
                      fontFamily: Fonts.BaiJamjuree_Medium,
                      color: themeStyles.textColor,
                      marginLeft: '5%',
                      marginTop: '8%',
                    }}>
                    {data.unit}
                  </Text>
                </View>
              </View>
              <VerticalSplitter
                color={themeStyles.splitterColor}
                style={{marginLeft: '4%'}}
              />
              <View style={[styles.ratedView, {marginLeft: '7%'}]}>
                <Text style={styles.ratedText}>Effective</Text>
                <View style={styles.ratedValue}>
                  <Text
                    style={{
                      fontSize: 25,
                      fontFamily: Fonts.BaiJamjuree_Bold,
                      color: themeStyles.textColor,
                    }}>
                    {formatDecimal(data.solareffectivecapacity)}
                  </Text>
                  <Text
                    style={{
                      fontSize: 15,
                      fontFamily: Fonts.BaiJamjuree_Medium,
                      color: themeStyles.textColor,
                      marginLeft: '5%',
                      marginTop: '8%',
                    }}>
                    {data.unit}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.installationDateView}>
              <Text
                style={{
                  fontSize: 12,
                  color: '#96999E',
                  fontFamily: Fonts.BaiJamjuree_Medium,
                }}>
                Installed on:
              </Text>
              <Text
                style={{
                  fontSize: 12,
                  color: '#96999E',
                  fontFamily: Fonts.BaiJamjuree_Medium,

                  marginLeft: '3%',
                }}>
                {formatDate(data.installationDate)}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.footer}>
          <View
            style={[
              styles.footer1,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={{
                fontSize: 15,
                color: '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: '5%',
              }}>
              Modules
            </Text>
            <Text
              style={{
                fontSize: 15,
                color: themeStyles.textColor,
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: 'auto',
                marginRight: '8%',
              }}>
              {Math.floor(data.solar.Total)}
            </Text>
          </View>
          <View
            style={[
              styles.footer2,
              {backgroundColor: themeStyles.cardBackground},
            ]}>
            <Text
              style={{
                fontSize: 15,
                color: '#96999E',
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: '5%',
              }}>
              Stand By
            </Text>
            <Text
              style={{
                fontSize: 15,
                color: themeStyles.textColor,
                fontFamily: Fonts.BaiJamjuree_Medium,
                marginLeft: 'auto',
                marginRight: '8%',
              }}>
              {data.solar.Faulty}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default SolarInformation;

const styles = StyleSheet.create({
  Header: {
    alignItems: 'center',
    marginTop: '2%',
  },

  title: {
    fontSize: 16,

    color: '#96999E',
  },

  headerIndicator: {
    height: 5,
    width: 44,
  },
  powerContainer: {
    height: '30%',
    marginTop: '1%',

    width: '98%',
    alignItems: 'center',
  },
  generating: {
    height: '20%',
    width: '98%',

    flexDirection: 'row',
    textAlignVertical: 'center',
    justifyContent: 'space-between',
    marginTop: '0%',
  },
  powerText: {
    fontSize: 15,

    color: '#96999E',
    textAlign: 'left',
  },
  powerValueText: {
    fontSize: 15,

    color: '#FFFFFF',
    textAlign: 'right',
  },
  infraContainer: {
    width: '98%',
    height: '22%',
    marginTop: '1%',
  },
  infraStatus: {
    height: '24%',
    width: '98%',

    marginTop: '5%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infraStatusText: {
    fontSize: 16,
    color: '#96999E',
    marginLeft: '4%',
  },
  InfraStatusLogo: {
    height: '100%',
    width: '50%',

    alignItems: 'center',
    flexDirection: 'row',
  },
  infraSize: {
    height: '100%',
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end', // Align to the end
  },
  greenDotStyle: {
    alignSelf: 'center', // Vertically center
    marginLeft: 5, // Add a small margin to separate text and dot
  },
  infraSizeText: {
    fontSize: 16,
    textAlign: 'right',
    width: 'auto', // Adjust to content width
    marginRight: 0, // Remove right margin
  },
  infoContainer: {
    width: '98%',
    height: '210%',
    marginTop: '3%',

    alignItems: 'center',
  },
  assetInformation: {
    height: '63%',

    width: '100%',
    backgroundColor: '#252A3480',
    borderRadius: 4,
  },
  assetInfoText: {
    fontSize: 15,

    color: '#FFFFFF',
    marginTop: '2%',
    marginLeft: '5%',
  },
  rated: {
    height: '50%',
    width: '100%',
    marginTop: '1%',

    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: '5.3%',
  },
  ratedView: {
    height: '90%',
    width: '40%',
  },
  ratedText: {
    fontSize: 15,
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  ratedValue: {
    height: '70%',
    width: '98%',
    marginTop: '0%',

    flexDirection: 'row',
  },
  installationDateView: {
    height: '15%',
    width: '80%',

    marginLeft: '5%',
    flexDirection: 'row',
  },
  footer: {
    height: '57%',
    width: '100%',

    marginTop: '-23%',
    flexDirection: 'row',
  },
  footer1: {
    height: '100%',

    width: '47%',
    backgroundColor: '#252A3480',
    borderRadius: 4,
    alignItems: 'center',
    flexDirection: 'row',
  },
  footer2: {
    height: '100%',

    width: '47%',
    backgroundColor: '#252A3480',

    borderRadius: 4,
    alignItems: 'center',

    flexDirection: 'row',
    marginLeft: '3%',
  },
});
