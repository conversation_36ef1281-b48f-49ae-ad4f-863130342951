import {
  ActivityIndicator,
  BackHandler,
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import BottomSheet, {BottomSheetView} from '@gorhom/bottom-sheet';
import Carousel from 'react-native-reanimated-carousel';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {
  GET_INFRA_DETAILS,
  GET_POWERFLOW,
  Request_Types,
} from '../../../../api/uri';
import BatteryInformation from './SiteInfraComponents/BatteryInformation';
import SolarInformation from './SiteInfraComponents/SolarInformation';
import TransformerInformation from './SiteInfraComponents/TransformerInformation';
import DGInformation from './SiteInfraComponents/DGInformation';
import RectifierInformation from './SiteInfraComponents/RectifierInformation';
import {useNavigation} from '@react-navigation/native';

const {width: viewportWidth} = Dimensions.get('window');

const SiteInfraBottomSheet = ({setInfraVisible, selectedAsset}) => {
  const carouselRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [entries, setEntries] = useState([]);
  const themeStyles = useThemeStyles();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [siteId, setSiteId] = useState('');
  const [infraDetails, setInfraDetails] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [powerFlow, setPowerFlow] = useState([]);
  const entriesUpdated = useRef(false);
  const [cachedData, setCachedData] = useState({});
  const navigation = useNavigation();
  const isAssetVisible = (name, asset) => {
    const size = asset?.size?.toLowerCase() || '';

    // Define what counts as useful data for each asset
    const hasUsefulData = {
      Solar: asset?.solar?.Total > 0,
      Generator: !!asset?.dgrunhours,
      Rectifier: asset?.rectifier?.Total > 0,
      Battery: !!asset?.Battery?.batteryeffectivecapacity,
      Transformer: !!asset?.phaseworknum,
    };

    // If size says "no [asset]" and no useful data => don't show
    if (size.startsWith('no') && !hasUsefulData[name]) {
      return false;
    }

    // Otherwise show (even if size is bad but data exists)
    return true;
  };
  const fetchInfraDetails = useCallback(
    async id => {
      if (!id) return;
      if (
        cachedData[id] &&
        Date.now() - cachedData[id].timestamp < 5 * 60 * 1000
      ) {
        setInfraDetails(cachedData[id].data);
        return;
      }

      try {
        setIsLoading(true);
        const token = await AsyncStorage.getItem('Authorization');
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_INFRA_DETAILS(id)}`,
          headers: {Authorization: token},
        });
        const data = response.data?.data || [];
        setInfraDetails(data);
        const powerFlowResponse = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_POWERFLOW(id)}`,
          headers: {Authorization: token},
        });
        const powerFlowData = powerFlowResponse.data?.data;
        const batteryData = data.find(item => item.name === 'Battery') || {};
        const solarData = data.find(item => item.name === 'Solar') || {};
        const transformerData =
          data.find(item => item.name === 'Transformer') || {};
        const generatorData =
          data.find(item => item.name === 'Generator') || {};
        const rectifierData =
          data.find(item => item.name === 'Rectifier') || {};

        const assetTypes = [
          {
            name: 'Battery',
            label: 'Battery',
            data: batteryData,
            component: BatteryInformation,
          },
          {
            name: 'Solar',
            label: 'Solar',
            data: solarData,
            component: SolarInformation,
          },
          {
            name: 'Transformer',
            label: 'Transformer',
            data: transformerData,
            component: TransformerInformation,
          },
          {
            name: 'Generator',
            label: 'DG',
            data: generatorData,
            component: DGInformation,
          },
          {
            name: 'Rectifier',
            label: 'Rectifier',
            data: rectifierData,
            component: RectifierInformation,
          },
        ];

        const filteredEntries = assetTypes
          .filter(
            asset =>
              asset.data.size !== `No ${asset.name}` ||
              isAssetVisible(asset.name, asset.data),
          )
          .map(asset => ({
            title: `${asset.label} Information`,
            content: (
              <asset.component data={asset.data} powerFlow={powerFlowData} />
            ),
          }));
        setPowerFlow(powerFlowData);

        setEntries(filteredEntries);
        setCachedData(prev => ({
          ...prev,
          [id]: {
            data,
            timestamp: Date.now(),
          },
        }));
      } catch (error) {
        console.error('Infra Details Fetch Error:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [isSiteID, cachedData],
  );

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        if (isSiteID) {
          setSiteId(isSiteID);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };

    fetchSiteId();
  }, [isSiteID, fetchInfraDetails]);
  useEffect(() => {
    const handleBackButtonPress = () => {
      setInfraVisible(false);
      return true;
    };

    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        event.preventDefault();
        setPsuModal(false);
      },
    );

    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation, setInfraVisible]);
  useEffect(() => {
    if (siteId) {
      fetchInfraDetails(siteId);

      const interval = setInterval(() => {
        fetchInfraDetails(siteId);
      }, 5 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [siteId]);

  useEffect(() => {
    if (selectedAsset && entries.length > 0) {
      const selectedIndex = entries.findIndex(item => {
        return item.title === selectedAsset;
      });

      if (selectedIndex > -1) {
        const tempNewEntries = [
          entries[selectedIndex],
          ...entries.filter((_, index) => index !== selectedIndex),
        ];

        // Compare new and old arrays
        const arraysAreEqual =
          JSON.stringify(entries) === JSON.stringify(tempNewEntries);

        if (!arraysAreEqual) {
          setEntries(tempNewEntries);
          setActiveIndex(0);
          setTimeout(() => {
            carouselRef.current?.scrollTo?.(0, false);
          }, 0);
        }
      }
    }
  }, [selectedAsset, entries]);

  useEffect(() => {
    if (entriesUpdated.current) {
      entriesUpdated.current = false;
    }
  }, [entries]);

  const renderItem = ({item}) => (
    <View style={styles.slide}>{item.content}</View>
  );

  const snapPoints = useMemo(() => ['92%'], []);

  const Paginator = ({data, scrollIndex}) => {
    const activeColor = '#FF7F02';

    return (
      <View style={styles.paginatorView}>
        {data.map((_, i) => (
          <View
            key={i.toString()}
            style={[
              styles.tab,
              {
                width: 26,
                backgroundColor:
                  scrollIndex === i
                    ? activeColor
                    : themeStyles.inactiveColorPagination,
                height: 5,
                marginHorizontal: 4,
                borderRadius: 6,
              },
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <BottomSheet
      index={1}
      snapPoints={snapPoints}
      backgroundStyle={[
        styles.background,
        {
          backgroundColor: themeStyles.background,
          borderColor: themeStyles.borderColor,
          borderWidth: 2,
          borderRadius: 25,
        },
      ]}
      style={styles.mainContainer}
      enablePanDownToClose={true}
      enableContentPanningGesture={true}
      handleStyle={styles.handleStyle}
      handleIndicatorStyle={[
        styles.headerIndicator,
        {
          backgroundColor: themeStyles.barBackground,
        },
      ]}
      indicatorStyle={{
        // Add this prop
        backgroundColor: themeStyles.textColor, // or any color you want
      }}
      onClose={() => {
        setInfraVisible(false);
      }}>
      <BottomSheetView style={styles.contentContainer}>
        {isLoading ? (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color="#FF7F02" />
          </View>
        ) : (
          <View style={{flex: 1, alignItems: 'center', marginBottom: '2%'}}>
            <Carousel
              ref={carouselRef}
              data={entries}
              renderItem={renderItem}
              sliderWidth={viewportWidth * 0.91}
              itemWidth={viewportWidth * 0.91}
              width={viewportWidth * 0.91}
              height={viewportWidth * 2}
              loop={false}
              onSnapToItem={index => setActiveIndex(index)}
              style={styles.carouselStyle}
            />
            <Paginator data={entries} scrollIndex={activeIndex} />
          </View>
        )}
      </BottomSheetView>
    </BottomSheet>
  );
};

export default SiteInfraBottomSheet;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    marginHorizontal: '2.5%',
  },
  contentContainer: {
    alignItems: 'center',
    alignContent: 'center',
    flexDirection: 'column',
    justifyContent: 'space-between',
    flex: 0.98,
  },
  background: {
    backgroundColor: '',
  },
  paginatorView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  handleStyle: {
    paddingVertical: 10,
    alignItems: 'center',
    width: '100%',
  },
  headerIndicator: {
    width: 50,
    height: 4,
    borderRadius: 2,
    alignSelf: 'center',
  },
  carouselStyle: {
    flex: 1,
  },
  loaderContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
