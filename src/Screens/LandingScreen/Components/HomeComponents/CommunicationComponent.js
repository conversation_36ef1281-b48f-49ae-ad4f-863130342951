import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import React, {memo, useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';

import {useSelector} from 'react-redux';

import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {fetchSiteAlarms} from '../../../../middleWare/ApiCallerFunctions';
import CrticalSvg from '../../../../assets/svgs/CrticalSvg';

const CommunicationComponent = ({setModal}) => {
  const [siteLoad, setSiteLoad] = useState([]);

  const isSiteID = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchSiteAlarms(isSiteID);
      if (JSON.stringify(siteLoad) !== JSON.stringify(data)) {
        setSiteLoad(data);
      }
    };

    fetchData();
    const interval = setInterval(fetchData, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [isSiteID]);

  return (
    <View style={[styles.communicationContainer, {}]}>
      <TouchableOpacity
        style={[
          styles.alarmContainer,
          {
            backgroundColor: themeStyles.newSSvCardsBackground,
            borderColor: themeStyles.newSSVBorderColor,
          },
        ]}
        onPress={() => {
          setModal(true);
        }}>
        <View style={styles.alarms}>
          <Text
            style={{
              fontSize: 12,
              fontFamily: Fonts.BaiJamjuree_Medium,
              color: '#737373',
              marginLeft: 0,
            }}>
            Active Alarms
          </Text>
        </View>
        <View style={[styles.alarmsCountContainer, {marginTop: '0%'}]}>
          <Text style={[styles.alarmsCount, {color: themeStyles.textColor}]}>
            {siteLoad && siteLoad.length > 0 ? siteLoad.length : '0'}
          </Text>
          <CrticalSvg width={24} height={24} />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default memo(CommunicationComponent);

const styles = StyleSheet.create({
  communicationContainer: {
    height: '10%',
    width: '96%',
    marginTop: '3%',

    justifyContent: 'center',
    padding: 2,
    zIndex: 1000,
  },
  communication: {
    height: '100%',
    width: '36%',

    justifyContent: 'center',
  },
  powerFlowContainer: {
    height: '30%',

    flexDirection: 'row',

    // height: '40%',
  },
  alarmContainer: {
    height: '100%',
    width: '28%',

    justifyContent: 'center',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#272727',
    alignItems: 'center',
    alignSelf: 'flex-end',

    // backgroundColor: 'white',
  },
  alarms: {
    alignItems: 'center',
    flexDirection: 'row',

    justifyContent: 'flex-end',
    height: '24%',
  },
  alarmsCount: {
    fontSize: 25,
    fontFamily: Fonts.BaiJamjuree_Bold,
    color: '#FFFFFF',
    marginRight: '3%',
  },
  alarmsCountContainer: {
    height: '60%',
    width: '80%',
    flexDirection: 'row',

    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  arrowAlarm: {
    width: 18,
    height: 18,
    backgroundColor: '#FFFFFF05',
    borderRadius: 6,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: '10%',
    marginLeft: '45%',
    marginRight: '5%',
  },
  colorDot: {
    height: 12,
    width: 12,
    backgroundColor: '#00EE5D',
    borderRadius: 40,
    elevation: 2,
    marginLeft: '2%',
  },
});
