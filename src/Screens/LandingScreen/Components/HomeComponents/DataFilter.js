import {StyleSheet, Text, View} from 'react-native';
import React, {useState, useEffect} from 'react';
import {Fonts} from '../../../../styles/fonts';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {Dropdown} from 'react-native-element-dropdown';

const DataFilter = ({
  setDate,
  isLandscape,
  modalHeight,
  setSelectedItem,
  selectedItem,
}) => {
  const themeStyles = useThemeStyles();
  const [selected, setSelected] = useState(null);

  const data = [
    {
      id: '1',
      title: 'Today',
      name: 'Today',
    },
    {
      id: '2',
      title: 'Yesterday',
      name: 'Yesterday',
    },
    {
      id: '3',
      title: 'This Week',
      name: 'This Week',
    },
    {
      id: '4',
      title: 'Last Week',
      name: 'Last Week',
    },
    {
      id: '5',
      title: 'This Month',
      name: 'This Month',
    },
    {
      id: '6',
      title: 'Last Month',
      name: 'Last Month',
    },
  ];

  useEffect(() => {
    if (!selected) {
      const defaultItem = data.find(item => item.id === '1');
      setSelected(defaultItem);
      setSelectedItem?.(defaultItem);
    }
  }, []);

  const handleChange = item => {
    setSelected(item);
    setSelectedItem?.(item);
  };

  return (
    <View style={[styles.dayFilter, isLandscape && styles.landscapeFilter]}>
      <Dropdown
        style={[
          styles.dropdown,

          {
            backgroundColor: '#FFFFFF1A',
            borderColor: '#50555E',
          },
        ]}
        placeholderStyle={[
          styles.placeholderStyle,
          {color: themeStyles.textColor},
        ]}
        selectedTextStyle={[
          styles.selectedTextStyle,
          {color: themeStyles.textColor},
        ]}
        data={data}
        labelField="name"
        valueField="id"
        value={selected?.id}
        placeholder={selected?.name || 'Select'}
        onChange={handleChange}
        containerStyle={[
          styles.dropdownContainer,
          {
            backgroundColor: themeStyles.background,
          },
          isLandscape && {
            width: '19%',
            position: 'absolute',
            top: '34%',
            left: '26%',
            height: '80%',
          },
        ]}
        activeColor={'#FF7F02'}
        itemTextStyle={[styles.itemText, {color: themeStyles.textColor}]}
        renderItem={item => (
          <Text style={[styles.itemText, {color: themeStyles.textColor}]}>
            {item.name}
          </Text>
        )}
        maxHeight={200}
      />
    </View>
  );
};

export default DataFilter;

const styles = StyleSheet.create({
  dayFilter: {
    width: '45%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '4%',
    justifyContent: 'center',
    borderRadius: 6,

    zIndex: 1000,
  },
  landscapeFilter: {
    width: '20%',
    height: '10%',
    marginTop: 0,
  },
  dropdown: {
    height: 43,
    width: '100%',
    borderRadius: 6,
    borderWidth: 0,
    paddingHorizontal: 15,
  },
  placeholderStyle: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Bold,
  },
  selectedTextStyle: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Bold,
    textAlign: 'left',
  },
  itemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    padding: 10,
  },
  dropdownContainer: {
    borderRadius: 6,
    marginTop: 5,
    borderWidth: 1,

    width: '40%',
  },
  itemContainer: {
    height: 47,
    justifyContent: 'center',
  },
});
