import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useEffect, useState, useMemo} from 'react';
import FastImage from 'react-native-fast-image';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  GET_INFRA_DETAILS,
  GET_POWERFLOW,
  Request_Types,
} from '../../../../api/uri';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {Fonts} from '../../../../styles/fonts';
import {
  fetchCurrentSource,
  fetchInfraDetails,
} from '../../../../middleWare/ApiCallerFunctions';
import PowerflowSvgTransformer from '../../../../assets/svgs/PowerflowSvgTransformer';
import {colors} from '../../../../Constants/ColorsSource';
import {SourceColorMaping} from '../../../../Constants/SourceColorMaping';
import {
  DG_COLOR,
  DG_SOLAR_COLOR,
  MAIN_SOLAR_COLOR,
  SOLAR_COLOR,
} from '../../../../Constants/SourceColors';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {formatNumber} from '../../../../Constants/FomateNumber';

const AssetsComponent = ({setInfraVisible, setSelectedAsset, layoutKey}) => {
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [infraDetails, setInfraDetails] = useState([]);

  const [clickedAsset, setClickedAsset] = useState(null);
  const [sourceTag, setSourceTag] = useState('');
  const [powerFlow, setPowerFlow] = useState([]);

  // Separate position states for main power flow

  const themestyles = useThemeStyles();
  const fetchAllData = useCallback(async id => {
    if (!id) return;

    const token = await AsyncStorage.getItem('Authorization');
    try {
      const [infraResponse, powerResponse] = await Promise.all([
        ApiCaller({
          method: Request_Types.GET,
          url: GET_INFRA_DETAILS(id),
          headers: {Authorization: token},
        }),
        ApiCaller({
          method: Request_Types.GET,
          url: GET_POWERFLOW(id),
          headers: {Authorization: token},
        }),
      ]);

      setInfraDetails(infraResponse.data?.data || []);
      const powerData = powerResponse?.data?.data?.[0];
      setSourceTag(powerData?.sourceTag ?? '');
      setPowerFlow(powerData ?? []);
    } catch (err) {
      console.error('Error fetching data:', err);
      setInfraDetails([]);
      setSourceTag('');
    } finally {
    }
  }, []);

  useEffect(() => {
    if (isSiteID) {
      fetchAllData(isSiteID);

      const interval = setInterval(() => {
        fetchAllData(isSiteID);
      }, 5 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [isSiteID, fetchAllData]);
  const infraLookup = useMemo(
    () =>
      infraDetails.reduce((acc, item) => {
        if (item?.name) acc[item.name] = item;
        return acc;
      }, {}),
    [infraDetails],
  );

  const assetConfigs = [
    {
      name: 'Solar',
      label: 'Solar',
      top: '53%',
      left: '23%',
      assetKey: 'Solar Information',
      direction: 'column',
    },
    {
      name: 'Transformer',
      label: 'Transformer',
      top: '32%',
      left: '71%',
      direction: 'column',
      assetKey: 'Transformer Information',
      width: 80,
    },
    {
      name: 'Generator',
      label: 'Dg',
      top: '85.5%',
      left: '81%',
      assetKey: 'DG Information',
      direction: 'column',
    },
    {
      name: 'Rectifier',
      label: 'Rectifier',
      top: '81%',
      left: '31%',
      assetKey: 'Rectifier Information',
      direction: 'row',
      width: 80,
    },
    {
      name: 'Battery',
      label: 'BMS',
      top: '81%',
      left: '18%',
      assetKey: 'Battery Information',
      direction: 'row-reverse',
    },
  ];
  const assetsDetails = [
    {
      name: 'Solar',
      label: 'Solar',
      top: '40%',
      left: '8%',
      assetKey: 'Solar Information',
      direction: 'column',
      values: `${infraLookup['Solar']?.solar?.Faulty ?? 0}/${
        Math.floor(infraLookup['Solar']?.solar?.Total) ?? 0
      }`,

      valueLabel: 'Faulty',
      color: '#f20100',
      value2: '',
    },
    {
      name: 'Transformer',
      label: 'Transformer',
      top: '16%',
      left: '68%',
      direction: 'column',
      assetKey: 'Transformer Information',
      values: infraLookup['Transformer']?.phaseworknum || '0' || '0',
      valueLabel: 'Phase',
      color: '#00BE4A',
      value2: '',
    },
    {
      name: 'Dg',
      label: 'Dg',
      top: '68%',
      left: '65%',
      assetKey: 'DG Information',
      direction: 'column',
      values: formatNumber(infraLookup['Generator']?.dgrunhours) || '0',

      valueLabel: 'Runtime',
      color: '#00BE4A',
      width: '34%',
      value2: '',
    },
    {
      name: 'Rectifier',
      label: 'Rec',
      top: '70%',
      left: '38%',
      assetKey: 'Rectifier Information',
      direction: 'row',

      values: `${infraLookup['Rectifier']?.rectifier?.Faulty ?? 0}/${
        infraLookup['Rectifier']?.rectifier?.Total ?? 0
      }`,

      valueLabel: 'Faulty',
      color: '#f20100',
      value2: '',
    },
    {
      name: 'Battery',
      label: 'BMS',
      top: '68%',
      left: '3%',
      height: '13%',
      assetKey: 'Battery Information',
      direction: 'row-reverse',
      values: `${infraLookup['Battery']?.soh}%` ?? 0,
      valueLabel: 'SOH',
      color: '#00BE4A',
      value2: `${Number(powerFlow?.otherparam?.battsoc).toFixed(1)}` ?? 0,
    },
  ];

  const handleAssetSelection = assetKey => {
    setSelectedAsset(assetKey);
    setInfraVisible(true);
  };
  const formatDecimal = value => {
    if (!value && value !== 0) return '0';
    const num = Number(value);
    // Check if the number has a decimal part
    return Number.isInteger(num) ? num.toFixed(0) : num.toFixed(1);
  };
  // Add a function to parse the source tag and determine which power sources are active
  const parseSourceTag = useCallback(sourceTag => {
    const sources = {
      hasMain: sourceTag.includes('Main') || sourceTag.includes('Mains'),
      hasSolar: sourceTag.includes('Solar'),
      hasDG: sourceTag.includes('DG'),
      hasBattery: sourceTag.includes('Battery'),
    };
    return sources;
  }, []);

  // Replace the existing transformerPathData and transformerPathDataCombine with this approach
  const powerFlowPaths = useMemo(() => {
    if (!sourceTag) return [];

    const cornerRadius = 15;
    const sources = parseSourceTag(sourceTag);
    const paths = [];

    // Main/Mains path
    if (sources.hasMain) {
      paths.push({
        pathData: `M10 200 
                  L10 ${20 + cornerRadius} 
                  Q10 20 ${10 + cornerRadius} 20 
                  L155 20`,
        top: '32%',
        left: '34%',
        direction: 100,
        color: colors[sourceTag] || SourceColorMaping[sourceTag] || '#FFCC00',
      });
    }

    // Solar path
    if (sources.hasSolar) {
      paths.push({
        pathData: `M97 40
                  L${113 - cornerRadius} 40
                  Q113 40 113 ${40 + cornerRadius}
                  L113 140`,
        top: '48%',
        left: '5%',
        direction: -100,
        color: sources.hasMain ? MAIN_SOLAR_COLOR : SOLAR_COLOR,
      });
    }

    // DG path
    if (sources.hasDG) {
      paths.push({
        pathData: `M13 42
                  L13 52
                  Q3 70 70 65
                  Q160 70 160 50
                  Q160 40 180 40
                  L190 40`,
        top: '80%',
        left: '32%',
        direction: 100,
        color: sources.hasMain ? DG_SOLAR_COLOR : DG_COLOR,
      });
    }

    return paths;
  }, [sourceTag]);
  const isAssetVisible = (name, asset) => {
    const size = asset?.size?.toLowerCase() || '';

    // Define what counts as useful data for each asset
    const hasUsefulData = {
      Solar: asset?.solar?.Total > 0,
      Generator: !!asset?.dgrunhours,
      Rectifier: asset?.rectifier?.Total > 0,
      Battery: !!asset?.Battery?.batteryeffectivecapacity,
      Transformer: !!asset?.phaseworknum,
    };

    // If size says "no [asset]" and no useful data => don't show
    if (size.startsWith('no') && !hasUsefulData[name]) {
      return false;
    }

    // Otherwise show (even if size is bad but data exists)
    return true;
  };

  // Battery path remains separate since it depends on battery current
  const batteryPath = useMemo(() => {
    const bt_C = powerFlow?.batterycurr;

    if (bt_C !== undefined && bt_C !== null) {
      const cornerRadius = 15;
      const direction =
        parseFloat(bt_C) >= -5 && bt_C <= 5
          ? 'Charged'
          : bt_C > 5
          ? 100
          : bt_C < -5 && bt_C > -1000
          ? -100
          : 'Load';

      if (direction === 'Charged') {
        return null;
      }

      return {
        pathData: `M45 40 
                  L45 ${60 - cornerRadius} 
                  Q45 60 ${40 + cornerRadius} 60 
                  L${90 - cornerRadius} 60 
                  Q90 60 90 ${60 - cornerRadius} 
                  L90 39`,
        top: '83%',
        left: '11%',
        direction,
        color: '#FFCC00',
      };
    }

    return null;
  }, [powerFlow]);

  const renderedAssets = useMemo(
    () => (
      <View
        style={{
          height: '46%',
          width: '100%',
          flex: 1,
          marginTop: '-10%',
          position: 'relative',
        }}>
        {/* Power Flow Lines */}
        {powerFlowPaths.map((path, index) => (
          <PowerflowSvgTransformer
            key={`power-flow-${index}`}
            style={{position: 'absolute', top: path.top, left: path.left}}
            pathData={path.pathData}
            direction={path.direction}
            pathColor={path.color}
          />
        ))}

        {/* Battery Path */}
        {batteryPath && (
          <PowerflowSvgTransformer
            style={{
              position: 'absolute',
              top: batteryPath.top,
              left: batteryPath.left,
            }}
            pathData={batteryPath.pathData}
            direction={batteryPath.direction}
            pathColor={batteryPath.color}
          />
        )}

        {/* Clickable Asset Icons */}
        {assetConfigs.map((config, index) => {
          const asset = infraLookup[config.name];
          const isInvalid =
            asset?.size?.toLowerCase()?.startsWith('no') &&
            !!infraLookup[config.name];

          if (isAssetVisible(config.name, asset)) {
            return (
              <TouchableOpacity
                key={config.name}
                style={{
                  position: 'absolute',
                  top: config.top,
                  left: config.left,
                  height: 70,
                  width: config.width || 40,
                  flexDirection: config.direction,
                  alignItems: 'center',
                }}
                onPress={() => handleAssetSelection(config.assetKey)}>
                <FastImage
                  style={{
                    width: 35,
                    height: 35,
                    opacity: isInvalid ? 0.5 : 0.8,
                    tintColor: isInvalid ? 'gray' : undefined,
                  }}
                  source={require('../../../../assets/Images/LoaderYellow.gif')}
                  resizeMode={FastImage.resizeMode.contain}
                />
                <Text style={[styles.text, {color: '#FFFFFF'}]}>
                  {config.label}
                </Text>
              </TouchableOpacity>
            );
          }
          return <View key={config.name} />;
        })}

        {/* Asset Details */}
        {assetsDetails.map((config, index) => {
          const asset = infraLookup[config.name];
          if (asset?.size !== `No ${config.name}` && asset?.size !== null) {
            return (
              <View
                key={`asset-detail-${config.name}-${index}`}
                style={{
                  width: config.width || '25%',
                  backgroundColor: themestyles.newSSvCardsBackground,
                  height: config.height || '10%',
                  position: 'absolute',
                  top: config.top,
                  left: config.left,
                  borderRadius: 6,
                  alignItems: 'center',
                  justifyContent: 'space-around',
                  flexDirection: 'column',
                  borderWidth: 1,
                  borderColor: themestyles.newSSVBorderColor,
                  elevation: 4,
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-around',
                    width: '100%',
                  }}>
                  <Text
                    style={{
                      fontSize: 12,
                      fontFamily: Fonts.BaiJamjuree_Medium,
                      color: config.color,
                    }}>
                    {config.valueLabel}
                  </Text>
                  <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: Fonts.BaiJamjuree_Medium,
                        color: themestyles.textColor,
                      }}>
                      {config.values}
                    </Text>
                    {config.name === 'Transformer' && (
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: Fonts.BaiJamjuree_Medium,
                          color: themestyles.textColor,
                          textAlign: 'right',
                        }}>
                        /3
                      </Text>
                    )}
                  </View>
                </View>
                {config.name === 'Battery' && (
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-around',
                      width: '100%',
                    }}>
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: Fonts.BaiJamjuree_Medium,
                        color: themestyles.textColor,
                        color: config.color,
                      }}>
                      SOC
                    </Text>

                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: Fonts.BaiJamjuree_Medium,
                        color: themestyles.textColor,
                        textAlign: 'right',
                      }}>
                      {config.value2}%
                    </Text>
                  </View>
                )}
              </View>
            );
          }
          return <View key={config.name} />;
        })}
      </View>
    ),
    [powerFlowPaths, batteryPath, infraLookup, layoutKey],
  );
  return renderedAssets;
};

export default AssetsComponent;

const styles = StyleSheet.create({
  text: {
    color: '#FFFFFFCC',
    fontSize: 12,
    width: '100%',

    fontFamily: Fonts.BaiJamjuree_SemiBold,
    textAlignVertical: 'center',

    textAlign: 'center',
  },
});
