import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  BackHandler,
} from 'react-native';
import BottomSheet, {BottomSheetView} from '@gorhom/bottom-sheet';
import Map from './Map';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GET_SITE_INFO, Request_Types} from '../../../../api/uri';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {Fonts} from '../../../../styles/fonts';
import {useNavigation} from '@react-navigation/native';

const SiteInformationBottomSheet = ({setVisible}) => {
  const [siteId, setSiteId] = useState('AAB801');
  const [siteInfo, setSiteInfo] = useState([]);
  const [lattitude, setLattitude] = useState();
  const [longitude, setLongitude] = useState();
  const [showRoute, setShowRoute] = useState(false);
  const navigation = useNavigation();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (isSiteID) {
          setSiteId(isSiteID);
          fetchSiteInfoData(isSiteID); // Fetch API data immediately after setting siteId
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };

    fetchSiteId();
  }, [isSiteID]);

  // Fetch Site Info
  const fetchSiteInfoData = useCallback(async id => {
    try {
      const token = await AsyncStorage.getItem('Authorization');
      const header = {Authorization: token};
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_SITE_INFO(id)}`,
        headers: header,
      });

      setSiteInfo(response.data?.data || []);
    } catch (error) {
      console.error('Site Info Fetch Error:', error);
    }
  }, []);

  // Ensure API runs if siteId changes
  useEffect(() => {
    if (isSiteID) {
      fetchSiteInfoData(isSiteID);
    }
  }, [isSiteID]);

  useEffect(() => {
    if (siteInfo.length > 0) {
      const firstSite = siteInfo[0];
      setLattitude(parseFloat(firstSite.latitude));
      setLongitude(parseFloat(firstSite?.['longitude']));
    }
  }, [siteInfo]);

  const snapPoints = useMemo(() => ['78%'], []);

  useEffect(() => {
    const handleBackButtonPress = () => {
      setVisible(false);
      return true;
    };

    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        event.preventDefault();
        setPsuModal(false);
      },
    );

    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation, setVisible]);

  return (
    <BottomSheet
      snapPoints={snapPoints}
      enablePanDownToClose={true}
      backgroundStyle={[
        styles.background,
        {
          backgroundColor: themeStyles.background,
        },
      ]}
      style={[styles.mainContainer, {borderColor: themeStyles.borderColor}]}
      enableContentPanningGesture={true}
      handleIndicatorStyle={[{backgroundColor: themeStyles.barBackground}]}
      index={1}
      onClose={() => {
        setVisible(false);
      }}>
      <BottomSheetView
        style={[
          styles.contentContainer,
          {backgroundColor: themeStyles.background},
        ]}>
        <View style={styles.Header}>
          <Text style={[styles.title]}>Site Information</Text>
        </View>
        <View style={styles.container}>
          <Map
            latitude={lattitude}
            longitude={longitude}
            showRoute={showRoute}
          />
        </View>

        <View style={styles.siteLoadContainer}>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Site Type</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Site Type'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Site Category</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Site Category'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Sub Region</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Sub Region'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Cluster</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Cluster'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Belt</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Belt'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Project</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Project'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={[styles.siteLoadLabel]}>Solar Charge Controller</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Solar Charge Controller'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Guest On Ground (Power)</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Guest On Ground (Power)'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Site Status</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Site Status'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Prime Status</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Prime Status'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
          <View style={styles.siteLoad}>
            <Text style={styles.siteLoadLabel}>Geotag</Text>
            {siteInfo.length > 0 ? (
              siteInfo.map((item, index) => (
                <Text
                  key={index}
                  style={[
                    styles.siteLoadValue,
                    {color: themeStyles.textColor},
                  ]}>
                  {item?.['Geotag'] || '---'}
                </Text>
              ))
            ) : (
              <Text
                style={[styles.siteLoadValue, {color: themeStyles.textColor}]}>
                N/A
              </Text>
            )}
          </View>
        </View>
      </BottomSheetView>
    </BottomSheet>
  );
};

export default SiteInformationBottomSheet;

const styles = StyleSheet.create({
  absolute: {
    width: 100,
  },
  background: {
    backgroundColor: '',
  },
  Header: {
    alignItems: 'center',
  },

  title: {
    fontSize: 16,

    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },

  headerIndicator: {
    height: 5,
    width: 44,
    marginTop: 8,
  },
  container: {
    height: 217,
    width: '100%',
  },
  map: {
    borderRadius: 10,
  },
  siteLoadContainer: {
    height: '70%',
    width: '100%',

    marginTop: '10%',
  },
  siteLoad: {
    height: 'auto',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '-5',
    justifyContent: 'space-between', // Align items to both ends
    paddingHorizontal: 20, // Add padding for spacing
  },
  siteLoadLabel: {
    fontSize: 14,
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_Medium,
    flex: 1, // Allow flexible width
    height: 'auto',
  },
  siteLoadValue: {
    fontSize: 14,
    color: '#FFFFFF',
    fontFamily: Fonts.BaiJamjuree_Medium,
    textAlign: 'right', // Align text to right
    flex: 1, // Allow flexible width
  },
  routeButton: {
    padding: 10,
    borderRadius: 8,
    marginHorizontal: 20,
    marginTop: 10,
    alignItems: 'center',
  },
  routeButtonText: {
    fontWeight: '600',
  },
  mainContainer: {
    flex: 1,
    marginHorizontal: '2.5%',
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 15,
  },
  contentContainer: {
    alignItems: 'center',
    alignContent: 'center',
    flexDirection: 'column',
    justifyContent: 'space-between',
    flex: 0.98,
  },
});
