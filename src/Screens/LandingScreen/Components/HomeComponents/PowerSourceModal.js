import {
  BackHandler,
  Dimensions,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  StatusBar,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import CrossSvg from '../../../../assets/svgs/CrossSvg';
import MenuSvg from '../../../../assets/svgs/MenuSvg';
import PSUutilizationFilter from './PSUutilizationFilter';
import DataFilter from './DataFilter';
import PowerSourceUtilizationChart from '../../../Components/Charts/PowerSourceUtilizationChart';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {useNavigation} from '@react-navigation/native';
import Orientation from 'react-native-orientation-locker';

const PowerSourceModal = ({setPsuModal}) => {
  const themeStyles = useThemeStyles();
  const navigation = useNavigation();
  const [utilization, setUtilization] = useState('Utilization');
  const [selectedItem, setSelectedItem] = useState({
    id: '2',
    title: 'Weekly',
    name: 'Weekly',
  });

  useEffect(() => {
    // Lock to landscape orientation when modal opens
    Orientation.lockToLandscape();
    StatusBar.setHidden(true);

    return () => {
      // Reset to portrait orientation when modal closes
      Orientation.lockToPortrait();
      StatusBar.setHidden(false);
    };
  }, []);

  useEffect(() => {
    const handleBackButtonPress = () => {
      setPsuModal(false);
      return true;
    };

    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        event.preventDefault();
        setPsuModal(false);
      },
    );

    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove();
    };
  }, [navigation, setPsuModal]);

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={true}
      onRequestClose={() => setPsuModal(false)}
      supportedOrientations={['landscape']}>
      <View style={styles.modalOverlay}>
        <View
          style={[
            styles.modalContainer,
            {backgroundColor: themeStyles.background},
          ]}>
          <View style={styles.contentContainer}>
            <View style={styles.headerContainer}>
              <Text style={[styles.headerText, {color: themeStyles.textColor}]}>
                Power Sources Utilization
              </Text>
              <TouchableOpacity
                onPress={() => setPsuModal(false)}
                style={[
                  styles.closeButton,
                  {backgroundColor: themeStyles.cardBackground},
                ]}>
                <CrossSvg color={themeStyles.iconColor} />
              </TouchableOpacity>
              <MenuSvg style={styles.menuIcon} />
            </View>

            <View style={styles.controlsContainer}>
              <PSUutilizationFilter
                isLandscape={true}
                setUtilization={setUtilization}
              />
              <DataFilter
                isLandscape={true}
                setSelectedItem={setSelectedItem}
              />
            </View>

            <View style={styles.chartContainer}>
              <PowerSourceUtilizationChart
                isExpanded={true}
                isLandscape={true}
                toggleAvailability={utilization}
                selectedItem={selectedItem}
              />
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 25,
    marginLeft: '5%',
    height: '96%',
    width: '90%',
    marginTop: '1%',
  },
  modalContainer: {
    width: '110%',
    height: '100%',
    borderRadius: 25,
    padding: 16,
  },
  contentContainer: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerText: {
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_Medium,
    flex: 1,
  },
  closeButton: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    right: '48%',
    bottom: '50%',
  },
  menuIcon: {
    marginRight: 8,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: '13%',
    width: '100%',
    marginTop: '-1%',
  },
  chartContainer: {
    marginTop: '11%',
  },
});

export default PowerSourceModal;
