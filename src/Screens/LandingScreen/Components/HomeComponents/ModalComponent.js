import {
  <PERSON><PERSON><PERSON><PERSON>,
  FlatList,
  KeyboardAvoidingView,
  Modal,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Platform,
  Keyboard,
} from 'react-native';
import React, {
  useCallback,
  useEffect,
  useState,
  memo,
  useMemo,
  useRef,
} from 'react';
import CrossSvg from '../../../../assets/svgs/CrossSvg';
import {Fonts} from '../../../../styles/fonts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {GET_SITE_ALARMS, Request_Types} from '../../../../api/uri';
import {AlarmsColorMaping} from '../../../../Constants/AlarmsColorMaping';
import {useSelector} from 'react-redux';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import Accordion from 'react-native-collapsible/Accordion';
import GreenDotSvg from '../../../../assets/svgs/GreenDotSvg';
import SearchIconDark from '../../../../assets/svgs/SearchIconDark';
import {ArrowAnimated} from '@ant-design/react-native/lib/collapse/collapse';
import ArrowSvg from '../../../../assets/svgs/ArrowSvg';
import LeftArrowSvg from '../../../../assets/svgs/DownArrowSvg';
import RightArrowSvg from '../../../../assets/svgs/RightArrowSvg';
import DownArrowSvg from '../../../../assets/svgs/DownArrowSvg';
import AlarmsComponent from '../AlarmsComponents/AlarmsComponent';
import {useNavigation} from '@react-navigation/native';
import {fetchSiteAlarms} from '../../../../middleWare/ApiCallerFunctions';

const ModalComponent = memo(({setModal}) => {
  const [siteLoad, setSiteLoad] = useState([]);
  const [siteId, setSiteId] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const istheme = useSelector(state => state.theme.theme);
  const themeStyles = useThemeStyles();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const navigation = useNavigation();
  const searchInputRef = useRef(null);

  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Filter alarms based on search query
  const filteredAlarms = useMemo(() => {
    if (!searchQuery.trim() || !siteLoad) return siteLoad;

    const query = searchQuery.toLowerCase();
    return siteLoad.filter(
      alarm =>
        alarm.name?.toLowerCase().includes(query) ||
        alarm.fullname?.toLowerCase().includes(query) ||
        alarm.description?.toLowerCase().includes(query) ||
        alarm.level?.toString().includes(query),
    );
  }, [siteLoad, searchQuery]);

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchSiteAlarms(isSiteID); // ✅ Pass siteId here

      setSiteLoad(data);
    };

    fetchData();
    const interval = setInterval(() => fetchData(isSiteID), 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [isSiteID]);

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (site) {
          setSiteId(site);
          fetchSiteAlarms(site);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };
    fetchSiteId();
  }, [fetchSiteAlarms]);

  useEffect(() => {
    if (siteId) {
      fetchSiteAlarms(siteId);
      const interval = setInterval(() => {
        fetchSiteAlarms(siteId);
      }, 5 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [siteId, fetchSiteAlarms]);

  useEffect(() => {
    const handleBackButtonPress = () => {
      setModal(false);
      return true;
    };

    const unsubscribeBeforeRemove = navigation.addListener(
      'beforeRemove',
      event => {
        event.preventDefault(); // Prevent default navigation
        setModal(false);
      },
    );

    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
      unsubscribeBeforeRemove(); // Cleanup the listener
    };
  }, [navigation, setModal]);

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={true}
      onRequestClose={() => setModal(false)}>
      <View style={styles.modalOverlay}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={10}
          style={[
            styles.modalContainer,
            {backgroundColor: themeStyles.background},
          ]}>
          <TouchableOpacity
            style={[
              styles.crossIconContainer,
              {backgroundColor: themeStyles.iconBackgrounColor},
            ]}
            onPress={() => setModal(false)}>
            <CrossSvg color={themeStyles.iconColor} />
          </TouchableOpacity>

          <View style={styles.modalHeader}>
            <Text
              style={{
                fontSize: 16,
                textAlign: 'center',
                fontFamily: Fonts.BaiJamjuree_SemiBold,
                color: '#96999E',
              }}>
              Site Active Alarms (
              {filteredAlarms && filteredAlarms.length > 0
                ? filteredAlarms.length
                : 0}
              )
            </Text>
          </View>
          <View
            style={[
              styles.searchContainer,
              {backgroundColor: themeStyles.textFieldColor, minHeight: 50},
            ]}>
            <TextInput
              ref={searchInputRef}
              placeholder="Search"
              placeholderTextColor={themeStyles.dropDownTextColor}
              style={{
                fontSize: 15,
                marginLeft: '3%',
                width: '83%',
                color: themeStyles.dropDownTextColor,
              }}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="none"
              onFocus={() => {}}
              onBlur={() => {}}
              blurOnSubmit={false}
              returnKeyType="search"
            />
            <SearchIconDark color={themeStyles.iconColor} />
          </View>

          {/* Wrap AlarmsComponent in a View with flex: 1 to prevent collapsing */}
          <View style={{flex: 1, width: '100%'}}>
            <AlarmsComponent siteLoad={filteredAlarms} />
          </View>

          <TouchableOpacity
            style={[styles.buttonContainer, {minHeight: 40}]}
            onPress={() => {
              setModal(false);
              navigation.navigate('Alarms');
            }}>
            <Text
              style={{
                color: '#96999E',
                fontFamily: Fonts.BaiJamjuree_SemiBold,
              }}>
              See All Alarms
            </Text>
            <View style={styles.arrowContainer}>
              <ArrowSvg color={'#96999E'} />
            </View>
          </TouchableOpacity>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
});

export default ModalComponent;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '95%',
    height: '90%',
    backgroundColor: 'white',
    borderRadius: 25,
    padding: 16,
    alignItems: 'center',
  },
  crossIconContainer: {
    alignSelf: 'flex-end',
    marginBottom: 1,
    height: 40,
    width: 40,

    position: 'absolute',
    right: '50%',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    bottom: '101%',
  },
  header: {
    padding: 10,
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
  },
  content: {
    padding: 2,
  },
  contentItem: {
    paddingVertical: 2,
  },
  contentItemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
  },
  contentItemContainer: {
    flexDirection: 'row',
    marginTop: '3%',
    marginLeft: '3%',
  },
  modalHeader: {
    height: '10%',
    width: '100%',

    alignItems: 'center',
    justifyContent: 'center',
  },
  searchContainer: {
    height: '7%',
    padding: 1,
    flexDirection: 'row',

    width: '100%',

    backgroundColor: '#FFFFFF1A',
    justifyContent: 'center',
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: '4%',
  },
  buttonContainer: {
    // height: 40,
    height: '6.5%',
    // width: 313,
    width: '100%',
    backgroundColor: '#191E27',
    marginTop: '3%',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#404346',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowContainer: {
    height: 24,
    width: 24,
    backgroundColor: '#FFFFFF05',
    marginLeft: '5%',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
