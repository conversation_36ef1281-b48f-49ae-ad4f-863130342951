import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import TowerSvg from '../../../../assets/svgs/TowerSvg';
import {Fonts} from '../../../../styles/fonts';
import PowerSourceCharts from '../../../Components/Charts/PowerSourceCharts';
import SiteLoadChart from '../../../Components/Charts/SiteLoadChart';
import {useSelector} from 'react-redux';
import {
  GET_LATEST_PACKET,
  GET_POWERFLOW,
  GET_SITE_LOAD_TREND,
  GET_SITES_LIST,
  Request_Types,
} from '../../../../api/uri';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {SourceColorMaping} from '../../../../Constants/SourceColorMaping';
import {PowerSourceSvgsMaping} from '../../../../Constants/PowerSourceSvgsMaping';
import {DayFilter, DayFilter_7} from '../../../../Constants/DateFilter';
import ArrowSvg from '../../../../assets/svgs/ArrowSvg';

import {useThemeStyles} from '../../../../Constants/useThemeStyles';

const PowerComponent = ({setPsuModal}) => {
  const [siteId, setSiteId] = useState('AAB801');
  const [truncatedPowerSource, setTruncatedPowerSource] = useState(''); // New state
  const [siteLoad, setSiteLoad] = useState([]);
  const [dotBackground, setDotBackground] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [lastPacket, setLastPacket] = useState();
  const [powerSource, setPowerSource] = useState('Undetermined');
  const [currentLoad, setCurrentLoad] = useState(); // State for power source
  const isSiteID = useSelector(state => state.siteId.siteId);
  const [percentage, setPercentage] = useState();

  const themeStyles = useThemeStyles();

  useEffect(() => {
    const fetchSiteId = async () => {
      try {
        if (isSiteID) {
          setSiteId(isSiteID);
        }
      } catch (error) {
        console.error('Error fetching site ID:', error);
      }
    };

    fetchSiteId();
  }, [isSiteID]);
  const fetchSiteLoadTrend = useCallback(async () => {
    try {
      if (isSiteID) {
        const token = await AsyncStorage.getItem('Authorization');
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayFormatted = yesterday.toISOString().split('T')[0];

        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_SITE_LOAD_TREND(
            isSiteID,
            yesterdayFormatted,
            DayFilter(),
          )}`,
          headers: {Authorization: token},
        });

        const data = response.data?.data || [];
        setSiteLoad(data);

        // Calculate percentage based on the latest data
        const latestData = data.length > 0 ? data[data.length - 1] : null;
        const current = latestData ? parseFloat(latestData.dcanaloadcurr) : 0;
        setCurrentLoad(current);
        const demandLoad = latestData ? parseFloat(latestData.demandload) : 0;
        const per =
          demandLoad !== 0 ? ((demandLoad - current) / demandLoad) * 100 : 0;

        setPercentage(per);
        try {
          setIsLoading(true);
          const token = await AsyncStorage.getItem('Authorization');
          const response = await ApiCaller({
            method: Request_Types.GET,
            url: `${GET_LATEST_PACKET(isSiteID)}`,
            headers: {Authorization: token},
          });
          setLastPacket(response.data);
        } catch (error) {
          console.error('Power Source Fetch Error:', error);
        } finally {
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.error('Power Source Fetch Error:', error);
    }
  }, [isSiteID]);

  const fetchCurrentSource = useCallback(async () => {
    try {
      if (isSiteID) {
        const token = await AsyncStorage.getItem('Authorization');
        const header = {Authorization: token};
        const response = await ApiCaller({
          method: Request_Types.GET,
          url: `${GET_POWERFLOW(isSiteID)}`,
          headers: header,
        });

        const data = response.data?.data?.[0];

        if (data && data.sourceTag && data.sourceTag.trim() !== '') {
          setPowerSource(data.sourceTag);

          // Truncate if necessary
          const maxWidth = 100; // Adjust as needed
          const textWidth = data.sourceTag.length * 10; // Approximate width (adjust multiplier if needed)

          if (textWidth > maxWidth) {
            let truncated =
              data.sourceTag.substring(0, Math.floor(maxWidth / 10) - 3) +
              '...';
            setTruncatedPowerSource(truncated);
          } else {
            setTruncatedPowerSource(data.sourceTag);
          }
        } else {
          // If sourceTag is empty or undefined, set to "Undetermined"
          setPowerSource('Undetermined');
          setTruncatedPowerSource('Undetermined');
        }
      }
    } catch (error) {
      console.error('Fetch Error:', error);
    }
  }, [isSiteID]);

  useEffect(() => {
    setIsLoading(true);
    fetchCurrentSource();

    fetchSiteLoadTrend();
    setIsLoading(false);
    const interval = setInterval(() => {
      fetchCurrentSource();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [isSiteID, fetchCurrentSource]); // Add siteId as a dependency

  useEffect(() => {
    setDotBackground(SourceColorMaping[powerSource]);
  }, [powerSource]);

  const formatDateTime = (dateString, time) => {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    const date = new Date(dateString);
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    const dayOfWeek = daysOfWeek[date.getDay()];

    const [hours, minutes] = time.split(':').map(Number);
    const formattedHours = hours % 12 || 12;
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    return ` ${formattedHours}:${formattedMinutes} ${ampm}, ${day} ${month}, ${year}`;
  };

  return (
    <View
      style={[
        styles.powerContainer,
        {
          backgroundColor: themeStyles.newSSvCardsBackground,
          borderColor: themeStyles.newSSVBorderColor,
        },
      ]}>
      <View style={styles.sourceContainer}>
        <View style={styles.source}>
          {PowerSourceSvgsMaping(powerSource)}
          <View style={styles.sourceDescriptionContainer}>
            <View style={styles.sourceDescription}>
              <View
                style={[
                  styles.colorDot,
                  {backgroundColor: dotBackground},
                ]}></View>
              <Text
                style={{
                  fontSize: 20,
                  fontFamily: Fonts.BaiJamjuree_SemiBold,
                  color: themeStyles.textColor,
                  marginLeft: '3%',
                }}>
                {truncatedPowerSource}
              </Text>
            </View>
            <Text
              style={{
                fontSize: 12,
                fontFamily: Fonts.BaiJamjuree_Medium,

                marginLeft: '3%',
                color: '#737476',
              }}>
              {lastPacket && lastPacket.data?.length > 0
                ? formatDateTime(
                    lastPacket.data[0].lastpacketdate,
                    lastPacket.data[0].lastpackettime,
                  )
                : 'No Data Available'}
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.chart}>
          <PowerSourceCharts
            style={{marginTop: '-20%'}}
            setPsuModal={setPsuModal}
          />
        </TouchableOpacity>
      </View>
      <View style={styles.loadContainer}>
        {/* <Text
          style={{
            fontSize: 12,
            fontFamily: Fonts.BaiJamjuree_Medium,
            color: '#FFFFFF',
            marginLeft: '3%',
            color: locationtext,
            marginTop: '15%',
          }}>
          Site Load
        </Text> */}

        {/*  */}
        <View style={styles.loadData}>
          <Text
            style={{
              fontSize: 12,
              fontFamily: Fonts.BaiJamjuree_Medium,

              marginTop: '6%',

              color: themeStyles.greyTextColor,
            }}>
            Load
          </Text>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                animating={isLoading}
                size="large"
                color="black"
              />
            </View>
          ) : (
            <Text
              style={{
                fontSize: 20,
                fontFamily: Fonts.BaiJamjuree_SemiBold,
                color: '#FFFFFF',
                marginLeft: '4%',
                marginTop: '0%',
                color: themeStyles.textColor,
              }}>
              {currentLoad ? currentLoad.toFixed(1) : '0.0'}
            </Text>
          )}

          <Text
            style={{
              fontSize: 16,
              fontFamily: Fonts.BaiJamjuree_SemiBold,
              color: themeStyles.textColor,
              marginLeft: '3%',
              marginTop: '3%',
            }}>
            A
          </Text>
        </View>
        <View style={styles.siteLoadChart}>
          <SiteLoadChart siteLoad={siteLoad} />
        </View>
      </View>
    </View>
  );
};

export default PowerComponent;

const styles = StyleSheet.create({
  powerContainer: {
    marginTop: '3%',
    height: '17%',
    width: '96%',
    backgroundColor: '#0E121AB2',
    borderRadius: 6,
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#272727',
  },
  sourceContainer: {
    height: '90%',
    width: '45%',
    marginLeft: '4%',
  },
  source: {
    height: '50%',
    marginLeft: '0%',
    flexDirection: 'row',
    marginTop: '8%',
    width: '145%',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 0.4,
  },
  sourceDescriptionContainer: {
    height: '100%',
    width: '100%',
    marginTop: '-3%',
  },
  sourceDescription: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    width: '100%',
    marginLeft: '-27',
    height: '60%',
    alignItems: 'center',

    marginLeft: '4%',
  },
  chartContainer: {
    height: '90%',
    marginTop: '-90',
    marginLeft: '4%',
    width: '85%',
    // backgroundColor: 'white',
  },
  loadContainer: {
    height: '100%',
    width: '34%',
    marginLeft: '13%',
    marginTop: '1%',
    // borderWidth: 1,
    borderRadius: 4,
    borderColor: '#96999E',
  },
  loadData: {
    flexDirection: 'row',
    height: '20%',
    marginTop: '8%',
    alignSelf: 'flex-end',
    alignItems: 'center',
  },
  siteLoadChart: {
    width: '70%',
    marginTop: '20%',
    height: '50%',
    marginLeft: '14%',
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    overflow: 'visible',
    alignSelf: 'flex-end',
  },
  colorDot: {
    height: 12,
    width: 12,
    backgroundColor: '#00EE5D',
    borderRadius: 40,
    elevation: 2,
    marginLeft: '-1%',
  },
  chart: {
    height: '100%',
    flex: 1,

    width: '100%',
  },
  arrow: {
    width: 18,
    position: 'absolute',
    height: 18,
    backgroundColor: '#FFFFFF05',
    borderRadius: 6,
    alignContent: 'center',
    top: '52%',

    bottom: 120,
    left: '91%',
  },
  siteloadPerText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginLeft: '9%',
    marginTop: '45%',
  },
});
