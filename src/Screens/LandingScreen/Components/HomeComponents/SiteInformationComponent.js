import {
  ActivityIndicator,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import {Skeleton, Stack} from '@rneui/themed';
import LocationPin from '../../../../assets/svgs/LocationPin';

import ArrowSvg from '../../../../assets/svgs/ArrowSvg';
import {useSelector, useDispatch} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  GET_SITE_INFO,
  GET_WEATHER_DETAILS,
  Request_Types,
} from '../../../../api/uri';
import {ApiCaller} from '../../../../middleWare/ApiCaller';
import {
  Weather_maping,
  weather_maping,
  WeatherMaping,
} from '../../../../Constants/WeatherMaping';
import {weather_icons} from '../../../../Constants/WeatherIconMaping';
import DropDown from '../SearchScreen/DropDown';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import {GetsiteID} from '../../../../redux/Slices/SiteIdSlice';
import WeatherInformationCard from './WeatherInformationCard/WeatherInformationCard';

const SiteInformationComponent = ({setVisible, setWeatherVisible}) => {
  const [siteId, setSiteId] = useState('');
  const [siteInfo, setSiteInfo] = useState([]);
  const [lattitude, setLattitude] = useState();
  const [longitude, setLongitude] = useState();
  const [location, setLocation] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [lineColor, setLineColor] = useState(false);
  const [weather, setWeather] = useState(0);
  const [weatherCode, setWeatherCode] = useState(0);
  const [weatherInfo, setWeatherInfo] = useState([]);
  const isSiteID = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();
  const dispatch = useDispatch();
  const [tooltip, showToolTip] = useState(false);
  // Listen to Redux state changes
  useEffect(() => {
    if (isSiteID && isSiteID !== siteId) {
      setSiteId(isSiteID);
      fetchSiteInfoData(isSiteID);
    }
  }, [isSiteID]);

  const fetchSiteInfoData = useCallback(async id => {
    if (!id) return;
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('Authorization');
      const header = {Authorization: token};
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_SITE_INFO(id)}`,
        headers: header,
      });

      setSiteInfo(response.data?.data || []);
    } catch (error) {
      console.error('Site Load Fetch Error:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);
  useEffect(() => {
    if (lattitude && longitude) {
      fetchCurrentWeather();
    }
  }, [lattitude, longitude]);
  const fetchCurrentWeather = useCallback(async () => {
    try {
      // const cachedWeather = await AsyncStorage.getItem('cachedWeather');
      // const cachedTime = await AsyncStorage.getItem('cachedTime');

      // if (
      //   cachedWeather &&
      //   cachedTime &&
      //   Date.now() - parseInt(cachedTime) < 5 * 60 * 1000
      // ) {
      //   // Use cached data if it's less than 5 minutes old
      //   const parsedWeather = JSON.parse(cachedWeather);
      //   setWeather(parsedWeather.temperature);
      //   setWeatherCode(parsedWeather.weatherCode);
      //   return;
      // }

      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_WEATHER_DETAILS(lattitude, longitude)}`,
      });

      setWeatherInfo(response.data);

      if (response.data?.current?.temperature_2m != null) {
        const temperature = String(
          Math.round(response.data?.current?.temperature_2m) || 0,
        );

        const weatherCode = response.data?.current?.weather_code || 0;

        setWeather(temperature);
        setWeatherCode(weatherCode);

        // Cache the data
        await AsyncStorage.setItem(
          'cachedWeather',
          JSON.stringify({temperature, weatherCode}),
        );
        await AsyncStorage.setItem('cachedTime', Date.now().toString());
      }
    } catch (error) {
      console.error('Weather Fetch Error:', error);
    }
  }, [lattitude, longitude]);

  useEffect(() => {
    if (siteId) {
      fetchSiteInfoData(siteId);
    }
  }, [siteId]);
  useEffect(() => {
    if (siteInfo.length > 0) {
      const firstSite = siteInfo[0];
      if (firstSite) {
        setLattitude(parseFloat(firstSite.latitude) || 0);
        setLongitude(parseFloat(firstSite.longitude) || 0);
        setLocation(firstSite.Belt);
      }
    }
  }, [siteInfo]);
  useEffect(() => {
    if (siteInfo.length > 0 && lattitude && longitude) {
      fetchCurrentWeather();

      const interval = setInterval(() => {
        // Fetch in background before cache expires
        const fetchBackground = async () => {
          try {
            const cachedTime = await AsyncStorage.getItem('cachedTime');

            if (
              cachedTime &&
              Date.now() - parseInt(cachedTime) >= 4 * 60 * 1000
            ) {
              // If cache is about to expire, fetch in background
              await fetchCurrentWeather();
            }
          } catch (error) {
            console.error('Background fetch error: ', error);
          }
        };
        fetchBackground();
      }, 60 * 1000); // Check every minute

      return () => clearInterval(interval);
    }
  }, [siteId, fetchCurrentWeather, siteInfo, lattitude, longitude]);

  useEffect(() => {
    if (lattitude != null && longitude != null) {
      setIsLoading(false);
    }
  }, [lattitude, longitude]);

  useEffect(() => {
    const fetchInitialSiteId = async () => {
      try {
        const site = await AsyncStorage.getItem('SelectedSiteId');
        if (site) {
          setSiteId(site);
        }
      } catch (error) {
        console.error('Error getting initial siteId:', error);
      }
    };

    fetchInitialSiteId();
  }, []);

  const handleSiteIdChange = async newSiteId => {
    setSiteId(newSiteId);
    dispatch(GetsiteID(newSiteId)); // Ensure we update Redux
    try {
      await AsyncStorage.setItem('SelectedSiteId', newSiteId);
    } catch (error) {
      console.error('Error saving site ID:', error);
    }
  };
  const handleSetVisible = useCallback(() => {
    setVisible(true);
  }, []);

  return (
    <View
      style={[
        styles.topContainer,
        {
          backgroundColor: themeStyles.newSSvCardsBackground,
          borderColor: themeStyles.newSSVBorderColor,
        },
      ]}>
      <View style={[styles.siteInformationContainer]}>
        <View
          style={[
            styles.siteIdContainer,
            {
              // borderBottomWidth: 2,
              // borderColor: !lineColor ? '#96999E' : '#FF7F02',
            },
          ]}>
          <View style={styles.colorDot}></View>
          <TouchableOpacity
            onPress={() => {
              setLineColor(true);
            }}>
            <DropDown
              onSiteIdChange={handleSiteIdChange}
              setLineColor={setLineColor}
              isIndependent={false}
            />
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={[
            styles.siteInformation,
            {
              backgroundColor: themeStyles.siteInfoButton,
              borderColor: themeStyles.siteInfoButtonColor,
            },
          ]}
          onPress={handleSetVisible}>
          <Text
            style={[
              styles.siteInformtaionText,
              {color: themeStyles.textColor},
            ]}>
            Site Information
          </Text>
          <View style={styles.arrow}>
            <ArrowSvg />
          </View>
        </TouchableOpacity>
      </View>
      <TouchableOpacity
        style={[styles.weatherContainer]}
        onPress={() => {
          setWeatherVisible(true);
        }}>
        <View
          style={[
            styles.locationContainer,
            {marginTop: '5%', alignSelf: 'flex-end'},
          ]}>
          <LocationPin style={{}} />
          <Text
            style={[styles.location, {color: themeStyles.greyTextColor}]}
            numberOfLines={1} // Limit to one line
            ellipsizeMode="tail">
            {location}
          </Text>
        </View>
        <View style={styles.conditionContainer}>
          <View style={styles.weatherConditionContainer}>
            <View
              style={[
                styles.locationContainer,
                {marginTop: '1%', marginLeft: '3%', height: '42%'},
              ]}>
              <Text style={[styles.weather, {color: themeStyles.textColor}]}>
                {weather}
              </Text>

              <Text
                style={[
                  {
                    fontSize: 16,
                    fontFamily: Fonts.BaiJamjuree_SemiBold,
                    marginTop: '2%',

                    color: themeStyles.textColor,
                    marginLeft: '3%',
                  },
                ]}>
                °C
              </Text>
            </View>
            <View
              style={[
                styles.locationContainer,
                {
                  marginTop: '-3%',
                  width: 'auto',
                  height: '50%',
                },
              ]}>
              <Text
                style={[styles.weatherText, {color: '#737373'}]}
                numberOfLines={1} // Limit to one line
                ellipsizeMode="tail">
                {WeatherMaping[weatherCode]}
              </Text>
            </View>
          </View>
          <View
            style={{
              alignSelf: 'flex-end',
              justifyContent: 'center',
              width: '40%',

              height: '100%',
              alignSelf: 'flex-end',
            }}>
            <Image
              source={weather_icons[weatherCode]}
              style={{
                height: 37,
                width: 43,
                alignSelf: 'flex-end',
                marginRight: '8%',
              }}></Image>
          </View>
        </View>
      </TouchableOpacity>
      {/* {tooltip && (
        <WeatherInformationCard
          location={location}
          weatherCode={weatherCode}
          weather={weather}
          weatherInfo={weatherInfo}
        />
      )} */}
    </View>
  );
};

export default React.memo(SiteInformationComponent);

const styles = StyleSheet.create({
  topContainer: {
    height: '15%',

    marginTop: '23%',
    width: '96%',
    flexDirection: 'row',
    backgroundColor: '#0E121AB2',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#272727',
  },
  siteInformationContainer: {
    height: '90%',
    backgroundColor: 'transparent',
    width: '50%',
    marginLeft: '3%',
    alignContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    marginTop: '3%',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',

    height: 20,
    width: 20,
    backgroundColor: 'white',
    marginLeft: '5%',
  },
  siteIdContainer: {
    height: '35%',
    width: '88%',

    flexDirection: 'row',
    alignContent: 'center',

    overflow: 'visible',
    marginTop: '3%',

    alignItems: 'center',
    marginRight: '3%',
  },
  colorDot: {
    height: 12,
    width: 12,
    backgroundColor: '#00EE5D',
    borderRadius: 40,
    marginTop: '2%',

    // shadowColor: '#00BE4A',
    // shadowOffset: {width: 0, height: 2},
    // shadowOpacity: 0.5,
    // shadowRadius: 4,
    // borderWidth: 1,
    // borderColor: '#00EE5D',

    elevation: 2,
    marginLeft: '0%',
    justifyContent: 'flex-end',
  },
  siteId: {
    color: '#96999E',
    fontSize: 30,
    fontFamily: Fonts.BaiJamjuree_Bold,
    marginLeft: '11%',
  },
  siteInformation: {
    backgroundColor: '#432A1180',
    height: '32%',

    width: '90%',
    marginTop: '7%',
    alignSelf: 'center',
    borderRadius: 6,
    alignItems: 'center',
    alignContent: 'center',
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderWidth: 0.5,
  },
  siteInformtaionText: {
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_SemiBold,

    textAlign: 'center',
  },
  weatherContainer: {
    height: '90%',

    width: '40%',
    marginLeft: '4.5%',
    marginTop: '2%',
    borderRadius: 6,

    alignContent: 'center',
    alignItems: 'center',
  },
  locationContainer: {
    height: '25%',

    textAlign: 'right',
    width: 'auto',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: '2%',

    alignSelf: 'flex-end',
  },
  location: {
    fontSize: 12,
    color: '#96999E',
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    marginLeft: '5%',
  },
  weather: {
    fontSize: 20,
    fontFamily: Fonts.BaiJamjuree_Bold,
    color: '#FFFFFF',
    marginLeft: '4%',
    width: 'auto',
  },
  arrow: {
    width: 18,
    height: 18,
    backgroundColor: '#FFFFFF05',
    borderRadius: 6,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: '7%',
  },
  weatherText: {
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    textAlign: 'right',

    color: '#FFFFFF',
  },
  conditionContainer: {
    height: '70%',

    width: '90%',
    alignSelf: 'flex-end',

    flexDirection: 'row',
  },
  weatherConditionContainer: {
    height: '100%',
    width: '60%',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
