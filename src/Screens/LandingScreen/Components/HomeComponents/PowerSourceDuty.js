import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Fonts} from '../../../../styles/fonts';
import MenuSvg from '../../../../assets/svgs/MenuSvg';
import DownloadsSvg from '../../../../assets/svgs/DownloadsSvg';
import LeftArrowSvg from '../../../../assets/svgs/DownArrowSvg';
import RightArrowSvg from '../RightArrowSvg';
import PowerSourceChart from '../../../Components/Charts/PowerCycleChart';
import PowerCycleChart from '../../../Components/Charts/PowerCycleChart';
import HourKwh from './HourKwh';
import DataFilter from './DataFilter';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import RotateSvg from '../../../../assets/svgs/RotateSvg';
import {useThemeStyles} from '../../../../Constants/useThemeStyles';
import PsdcLandScapeModal from './PsdcLandScapeModal';
const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

const PowerSourceDuty = ({setPsdcModal}) => {
  const istheme = useSelector(state => state.theme.theme);
  const [date, setDate] = useState('');
  const [isDark, setIsDark] = useState(false);
  const [utilization, setUtilization] = useState('hrs');
  const [isLandscape, setIsLandscape] = useState(false);
  const [selectedItem, setSelectedItem] = useState(); // Initialize with Daily
  const themeStyles = useThemeStyles();

  return (
    <View
      style={[
        styles.powerSourceContainer,
        {backgroundColor: themeStyles.ssvBackground},
      ]}>
      <View style={styles.powerSourceHeader}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: Fonts.BaiJamjuree_Medium,
            color: '#96999E',
            marginLeft: '4%',
            marginTop: '4%',
          }}>
          Sources Utilization
        </Text>
        <TouchableOpacity
          onPress={() => {
            setPsdcModal(true);
            setIsLandscape(true);
          }}
          style={{marginTop: '5%', marginLeft: '30%'}}>
          <RotateSvg />
        </TouchableOpacity>

        <MenuSvg style={{marginTop: '5%', marginLeft: '10%'}} />
      </View>
      <View style={styles.MenuContainer}>
        <HourKwh setUtilization={setUtilization} />
        <DataFilter
          setSelectedItem={setSelectedItem}
          isLandscape={isLandscape}
        />
      </View>

      <PowerCycleChart
        selectedItem={selectedItem}
        utilization={utilization}
        isLandscape={false}
      />
    </View>
  );
};

export default PowerSourceDuty;

const styles = StyleSheet.create({
  powerSourceContainer: {
    height: screenHeight * 0.5,

    backgroundColor: '#252A34B2',
    borderRadius: 6,
    width: '95%',
    marginBottom: '20%',
    marginTop: '7%',
    alignItems: 'center',
  },
  powerSourceHeader: {
    height: '20%',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '-5%',
  },
  MenuContainer: {
    flexDirection: 'row',
    height: '20%',
    width: '93%',

    alignItems: 'center',

    justifyContent: 'space-between',

    transform: [{translateY: -20}],
  },
});
