import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import Mapbox, {
  MapView,
  Camera,
  PointAnnotation,
  ShapeSource,
  LineLayer,
} from '@rnmapbox/maps';
import MapPin from '../../../../assets/svgs/MapPin';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

Mapbox.setAccessToken(
  '*********************************************************************************************',
);

const styles = StyleSheet.create({
  page: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    marginTop: '9%',
  },
  container: {
    width: '90%',
    height: 217,
    borderRadius: 10,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  errorText: {
    color: 'red',
    position: 'absolute',
    bottom: 10,
    left: 10,
    backgroundColor: 'rgba(255,255,255,0.7)',
    padding: 5,
    borderRadius: 5,
    fontSize: 10,
  },
});

const Map = ({latitude, longitude, showRoute = false}) => {
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(false);
  const [routeGeoJSON, setRouteGeoJSON] = useState(null);
  const [routeError, setRouteError] = useState(null);

  // Hardcoded current location (Islamabad coordinates as an example)
  const hardcodedCurrentLocation = {
    latitude: 33.6844,
    longitude: 73.0479,
  };

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');

        if (storedTheme !== null) {
          const parsedTheme = JSON.parse(storedTheme);
          setIsDark(parsedTheme);
        } else {
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching/parsing theme:', error);
        setIsDark(istheme === 'dark');
      }
    };

    fetchTheme();
    Mapbox.setTelemetryEnabled(false);
  }, [istheme]);

  // Reset route data when showRoute changes to false
  useEffect(() => {
    if (!showRoute) {
      setRouteGeoJSON(null);
      setRouteError(null);
    }
  }, [showRoute]);

  // Fetch route directions when showRoute is true and destination location is available
  useEffect(() => {
    const fetchRoute = async () => {
      if (showRoute && latitude && longitude) {
        try {
          setRouteError(null);

          // Ensure coordinates are valid numbers
          const destLng = parseFloat(longitude);
          const destLat = parseFloat(latitude);
          const currLng = hardcodedCurrentLocation.longitude;
          const currLat = hardcodedCurrentLocation.latitude;

          if (
            isNaN(destLng) ||
            isNaN(destLat) ||
            isNaN(currLng) ||
            isNaN(currLat)
          ) {
            setRouteError('Invalid coordinates');
            return;
          }

          // Format coordinates as longitude,latitude (Mapbox API format)
          const start = `${currLng},${currLat}`;
          const end = `${destLng},${destLat}`;

          // Make request to Mapbox Directions API
          const response = await fetch(
            `https://api.mapbox.com/directions/v5/mapbox/driving/${start};${end}?alternatives=false&geometries=geojson&overview=full&steps=false&access_token=${Mapbox.getAccessToken()}`,
          );

          const data = await response.json();

          if (!response.ok) {
            setRouteError(`API error: ${data.message || 'Unknown error'}`);
            console.error('Mapbox API error:', data);
            return;
          }

          if (!data.routes || data.routes.length === 0) {
            setRouteError('No route found');
            console.error('No routes returned from API');
            return;
          }

          // Create GeoJSON from the route
          const routeGeoJSON = {
            type: 'FeatureCollection',
            features: [
              {
                type: 'Feature',
                properties: {},
                geometry: {
                  type: 'LineString',
                  coordinates: data.routes[0].geometry.coordinates,
                },
              },
            ],
          };

          setRouteGeoJSON(routeGeoJSON);
        } catch (error) {
          console.error('Error fetching route:', error);
          setRouteError(`Error: ${error.message}`);
        }
      }
    };

    fetchRoute();
  }, [showRoute, latitude, longitude]);

  const background = !isDark ? Mapbox.StyleURL.Dark : Mapbox.StyleURL.Street;

  // Calculate bounds to fit both points
  const getBoundsForPoints = () => {
    if (!latitude || !longitude) return null;

    const destLng = parseFloat(longitude);
    const destLat = parseFloat(latitude);
    const currLng = hardcodedCurrentLocation.longitude;
    const currLat = hardcodedCurrentLocation.latitude;

    // Add some padding
    const padding = 0.1;

    return [
      [
        Math.min(currLng, destLng) - padding,
        Math.min(currLat, destLat) - padding,
      ],
      [
        Math.max(currLng, destLng) + padding,
        Math.max(currLat, destLat) + padding,
      ],
    ];
  };

  return (
    <View style={styles.page}>
      <View style={styles.container}>
        <MapView
          style={styles.map}
          styleURL={background}
          zoomEnabled={true}
          scrollEnabled={true}
          rotateEnabled={true}
          pitchEnabled={true}
          attributionEnabled={false}
          logoEnabled={false}
          scaleBarEnabled={false}
          compassEnabled={false}>
          {/* Camera to set the view */}
          {showRoute ? (
            <Camera
              bounds={getBoundsForPoints()}
              animationMode="flyTo"
              animationDuration={1500}
              padding={50}
            />
          ) : (
            <Camera
              centerCoordinate={[longitude || 73.0479, latitude || 33.6844]}
              zoomLevel={2.5}
              animationMode="none"
            />
          )}

          {/* Destination marker */}
          <PointAnnotation
            id="destination"
            coordinate={[longitude || 0, latitude || 0]}>
            <MapPin />
          </PointAnnotation>

          {/* Current location marker (hardcoded) */}
          {showRoute && (
            <PointAnnotation
              id="currentLocation"
              coordinate={[
                hardcodedCurrentLocation.longitude,
                hardcodedCurrentLocation.latitude,
              ]}>
              <View
                style={{
                  height: 20,
                  width: 20,
                  backgroundColor: '#4285F4',
                  borderRadius: 10,
                  borderWidth: 2,
                  borderColor: 'white',
                }}
              />
            </PointAnnotation>
          )}

          {/* Route line (if available) */}
          {routeGeoJSON && (
            <ShapeSource id="routeSource" shape={routeGeoJSON}>
              <LineLayer
                id="routeLayer"
                style={{
                  lineColor: '#4285F4',
                  lineWidth: 4,
                  lineCap: 'round',
                  lineJoin: 'round',
                }}
              />
            </ShapeSource>
          )}
        </MapView>

        {/* Show error message if route fetching failed */}
        {routeError && <Text style={styles.errorText}>{routeError}</Text>}
      </View>
    </View>
  );
};

export default Map;
