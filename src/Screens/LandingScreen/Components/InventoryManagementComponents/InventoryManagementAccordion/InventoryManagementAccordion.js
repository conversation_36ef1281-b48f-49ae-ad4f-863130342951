import {
  StyleSheet,
  Text,
  View,
  <PERSON><PERSON><PERSON>iew,
  FlatList,
  useCallback,
  TouchableOpacity,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import Accordion from 'react-native-collapsible/Accordion';
import {useThemeStyles} from '../../../../../Constants/useThemeStyles';
import DownArrowSvg from '../../../../../assets/svgs/DownArrowSvg';

import {Fonts} from '../../../../../styles/fonts';
import RightArrowSvg from '../../../../../assets/svgs/RightArrowSvg';
import {Switch} from '@ant-design/react-native';
import EditSvg from '../../../../../assets/svgs/EditSvg';
import {formatTimestamp} from '../../../../../Constants/FormatTimestamp';
import {useNavigation} from '@react-navigation/native';

const InventoryManagementAccordion = () => {
  const themeStyles = useThemeStyles();
  const [activeSections, setActiveSections] = useState({
    general: [],
    solar: [],
    grid: [],
    genset: [],
    rectifier: [],
    battery: [],
  });
  const navigation = useNavigation();

  // Define different data for each device type
  const deviceData = {
    general: [
      {
        id: 1,
        name: 'General Information',
        ip: '***********',
        protocol_data: 'SNMP v2',
      },
    ],
    solar: [
      {
        id: 2,
        name: 'Solar',
        power_output: '5kW',
        efficiency: '95%',
      },
    ],
    grid: [
      {
        id: 3,
        name: 'Grid ',
        voltage: '220V',
        current: '10A',
      },
    ],
    genset: [
      {
        id: 4,
        name: 'Generator',
        fuel_level: '80%',
        runtime: '24hrs',
      },
    ],
    rectifier: [
      {
        id: 5,
        name: 'Rectifier',
        output_voltage: '48V',
        temperature: '35°C',
      },
    ],
    battery: [
      {
        id: 6,
        name: 'Battery',
        status: 'Active',
        charge_level: '85%',
      },
    ],
  };

  const renderDeviceContent = (section, type, isActive) => {
    switch (type) {
      case 'general':
        return (
          <>
            <View style={[styles.contentItemContainer]}>
              <Text style={[styles.contentItemText, {color: '#96999E'}]}>
                IP Address
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    width: '64%',
                    marginLeft: '8%',
                  },
                ]}>
                {section.ip || '---'}
              </Text>
            </View>
            <View style={styles.contentItemContainer}>
              <Text style={[styles.contentItemText, {color: '#96999E'}]}>
                Protocol Data
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    width: '64%',
                    marginLeft: '8%',
                  },
                ]}>
                {section.protocol_data || '---'}
              </Text>
            </View>
          </>
        );

      case 'battery':
        return (
          <>
            <View style={styles.contentItemContainer}>
              <Text style={[styles.contentItemText, {color: '#96999E'}]}>
                Status
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    width: '64%',
                    marginLeft: '8%',
                  },
                ]}>
                {section.status || '---'}
              </Text>
            </View>
            <View style={styles.contentItemContainer}>
              <Text style={[styles.contentItemText, {color: '#96999E'}]}>
                Charge Level
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    width: '64%',
                    marginLeft: '8%',
                  },
                ]}>
                {section.charge_level || '---'}
              </Text>
            </View>
          </>
        );

      case 'solar':
        return (
          <>
            <View style={styles.contentItemContainer}>
              <Text style={[styles.contentItemText, {color: '#96999E'}]}>
                Power Output
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    width: '64%',
                    marginLeft: '8%',
                  },
                ]}>
                {section.power_output || '---'}
              </Text>
            </View>
            <View style={styles.contentItemContainer}>
              <Text style={[styles.contentItemText, {color: '#96999E'}]}>
                Efficiency
              </Text>
              <Text
                style={[
                  styles.contentItemText,
                  {
                    color: themeStyles.textColor,
                    width: '64%',
                    marginLeft: '8%',
                  },
                ]}>
                {section.efficiency || '---'}
              </Text>
            </View>
          </>
        );

      // Add other cases as needed
      default:
        return null;
    }
  };

  const _updateSections = (type, sections) => {
    setActiveSections(prev => ({
      ...prev,
      [type]: sections,
    }));
  };

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        {Object.entries(deviceData).map(([type, data]) => (
          <View key={type} style={styles.accordionWrapper}>
            <Accordion
              sections={data}
              activeSections={activeSections[type]}
              renderHeader={(section, index, isActive) => (
                <View
                  style={[
                    styles.header,
                    {
                      backgroundColor: isActive
                        ? themeStyles.alarmsBackground
                        : 'transparent',
                    },
                  ]}>
                  <View style={styles.leftContent}>
                    <View style={styles.arrowWrapper}>
                      <View style={styles.arrowContainer}>
                        {isActive ? (
                          <DownArrowSvg color={'#FF7F02'} />
                        ) : (
                          <RightArrowSvg color={'#96999E'} />
                        )}
                      </View>
                    </View>
                    <Text
                      style={[
                        styles.key,
                        {color: isActive ? '#FF7F02' : '#96999E'},
                      ]}
                      numberOfLines={1}>
                      {section.name}
                    </Text>
                  </View>
                </View>
              )}
              renderContent={section => (
                <View
                  style={[
                    styles.content,
                    {backgroundColor: themeStyles.alarmsBackground},
                  ]}>
                  {renderDeviceContent(section, type)}
                </View>
              )}
              onChange={sections => _updateSections(type, sections)}
              underlayColor="transparent"
              sectionContainerStyle={styles.sectionContainer}
            />
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '96%',
  },
  sectionContainer: {},
  scrollContainer: {
    width: '100%',
  },
  header: {
    width: '100%',
    height: 41,
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  leftContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
  },
  arrowWrapper: {
    width: 32,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  key: {
    flex: 1,
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    paddingHorizontal: 8,
  },
  editContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: 100,
    marginLeft: 8,
  },
  switch: {
    transform: [{scale: 0.8}],
    marginRight: 12,
  },
  editButton: {
    width: 34,
    height: 34,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 16,
    paddingTop: 8,
  },
  contentItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  contentItemText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_Medium,
    width: '40%',
  },
  accordionWrapper: {
    marginBottom: 16,
  },
  deviceTypeHeader: {
    fontSize: 18,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#333',
    marginBottom: 8,
  },
});

export default InventoryManagementAccordion;
