import * as React from 'react';
import Svg, {<PERSON>, <PERSON>, Defs, ClipPath, Rect} from 'react-native-svg';
const ConfigurationSvg = ({color = '#C2C0BE', ...props}) => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <G clipPath="url(#clip0_658_1313)">
      <Path
        d="M1.69188 11.724C1.69188 11.724 1.89288 11.2867 2.24388 10.896L3.01713 10.0312C3.20613 9.79125 3.34038 9.375 3.34038 9C3.34038 8.625 3.20613 8.208 3.01788 7.96875L2.24313 7.104C1.89213 6.714 1.71663 6.51825 1.69113 6.276C1.66563 6.03375 1.79613 5.80575 2.05863 5.3505L2.42913 4.70775C2.70888 4.22175 2.84913 3.97875 3.08763 3.88275C3.32538 3.78525 3.59463 3.86175 4.13388 4.01475L5.04888 4.272C5.39313 4.3515 5.75388 4.3065 6.06738 4.14525L6.32013 3.99975C6.58953 3.82699 6.79662 3.5726 6.91113 3.27375L7.16163 2.526C7.32663 2.031 7.40913 1.7835 7.60488 1.641C7.80213 1.5 8.06238 1.5 8.58288 1.5H9.41913C9.93963 1.5 10.2006 1.5 10.3964 1.64175C10.5921 1.7835 10.6746 2.031 10.8389 2.526L11.0901 3.27375C11.2046 3.5726 11.4117 3.82699 11.6811 3.99975L11.9339 4.14525C12.2474 4.3065 12.6089 4.3515 12.9524 4.27275L13.8674 4.01475C14.4066 3.86175 14.6759 3.78525 14.9136 3.882C15.1521 3.9795 15.2924 4.22175 15.5721 4.70775L15.9419 5.3505C16.2044 5.80575 16.3356 6.033 16.3101 6.276C16.2846 6.519 16.1091 6.71325 15.7581 7.104L14.9849 7.96875C14.7959 8.208 14.6616 8.625 14.6616 9C14.6616 9.375 14.7959 9.792 14.9841 10.0312L15.7581 10.896C16.1091 11.286 16.2846 11.4817 16.3101 11.724C16.3356 11.9662 16.2051 12.1942 15.9426 12.6495L15.5721 13.2922C15.2924 13.7782 15.1521 14.0213 14.9136 14.1173C14.6759 14.2148 14.4066 14.1382 13.8674 13.9852L12.9524 13.728C12.6083 13.649 12.2474 13.6939 11.9331 13.8547L11.6811 14.0002C11.4111 14.1727 11.2041 14.4277 11.0901 14.7262L10.8396 15.474C10.6746 15.969 10.5921 16.2165 10.3964 16.359C10.2006 16.5 9.93963 16.5 9.41913 16.5H8.58288C8.06238 16.5 8 16.5 7.60563 16.5"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M2.05272 14.0849C2.86272 13.2749 5.61672 10.5479 5.88672 10.2329C6.17247 9.89985 5.94072 9.44985 6.07872 8.05485C6.14547 7.37985 6.29022 6.87435 6.70572 6.49785C7.20072 6.02985 7.60572 6.02985 9.00072 5.99835C10.2157 6.02985 10.3597 5.89485 10.4857 6.20985C10.5757 6.43485 10.3057 6.56985 9.98172 6.92985C9.26172 7.64985 8.83872 8.00985 8.79822 8.23485C8.50572 9.22485 9.65772 9.80985 10.2877 9.17985C10.5262 8.94135 11.6287 7.82985 11.7367 7.73985C11.8177 7.66785 12.012 7.67085 12.1057 7.78485C12.1867 7.86435 12.1957 7.87485 12.1867 8.23485C12.1792 8.56785 12.1822 9.04635 12.1837 9.53985C12.1845 10.1789 12.1507 10.8899 11.8807 11.2499C11.3407 12.0599 10.4407 12.1049 9.63072 12.1409C8.86572 12.1859 8.23572 12.1049 8.03772 12.2489C7.87572 12.3299 7.02072 13.2299 5.98572 14.2649L4.14072 16.1099C2.61072 17.3249 0.927718 15.4349 2.05272 14.0849Z"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_658_1313">
        <Rect width={18} height={18} fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default React.memo(ConfigurationSvg);
