import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useCallback, useEffect, useState, memo} from 'react';
import {
  DrawerContentScrollView,
  DrawerItemList,
} from '@react-navigation/drawer';
import Page<PERSON>ogoDark from '../../../assets/svgs/PageLogoDark';
import {Fonts} from '../../../styles/fonts';
import LogOutSvg from '../../../assets/svgs/LogOutSvg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import PageLogo from '../../../assets/svgs/PageLogo';
import {AUTHORIZE, LogoutApi, Request_Types} from '../../../api/uri';
import {ApiCaller} from '../../../middleWare/ApiCaller';

// Memoize the component to prevent unnecessary re-renders
const CustomDrawerContent = memo(props => {
  const {roles} = props; // Extract roles from props
  const isDark = false;

  const navigation = useNavigation();
  const logout = useCallback(async () => {
    try {
      const token = await AsyncStorage.getItem('Authorization');
      const payload = {
        token: token,
      };
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${LogoutApi}`,
        data: payload,
      });

      if (response.data.success) {
        await AsyncStorage.removeItem('Authorization');
        navigation.navigate('onBoarding');
      }
    } catch (error) {
      console.error('API Error:', error.response?.data || error.message);
    }
  });

  const background = isDark ? '#F1F1F1' : '#0C121D';
  const textColor = isDark ? '#0E121A' : '#FFFFFF';
  return (
    <View style={[styles.drawerContainer, {backgroundColor: background}]}>
      <DrawerContentScrollView
        {...props}
        bounces={false}
        alwaysBounceVertical={false}
        removeClippedSubviews={true}
        contentContainerStyle={{paddingBottom: 20}}
        showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          {isDark ? <PageLogo /> : <PageLogoDark />}

          <View style={styles.profileContainer}>
            {/* <View style={styles.profile}>
              <Image
                source={require('../../../assets/Images/Affan01.png')}
                style={styles.image}
              />
            </View> */}
            <View style={styles.profileDescription}>
              {roles ? ( // Conditional rendering check
                <>
                  <Text style={[styles.name, {color: textColor}]}>
                    {roles.name}
                  </Text>
                  <Text style={styles.designation}>{roles.role}</Text>
                </>
              ) : (
                <Text style={[styles.name, {color: textColor}]}>
                  Loading...
                </Text>
              )}
            </View>
          </View>
        </View>
        <DrawerItemList {...props} style={styles.drawerItem} />
        <TouchableOpacity
          style={styles.logOutContainer}
          onPress={() => {
            logout();
          }}>
          <LogOutSvg style={{marginLeft: '3%'}} />

          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
      </DrawerContentScrollView>
    </View>
  );
});

export default CustomDrawerContent;

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
    backgroundColor: '#0C121D',
  },
  header: {
    marginTop: '3%',
    marginLeft: '4%',
  },
  profileContainer: {
    marginTop: '11%',
    flexDirection: 'row',
  },
  profile: {
    height: 50,
    width: 50,
  },
  image: {
    height: 50,
    width: 50,
    borderRadius: 40,
  },
  profileDescription: {
    height: 50,
    width: '100%',
    marginLeft: '0%',
    marginBottom: '8%',
  },
  name: {
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FFFFFF',
  },
  designation: {
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#96999E',
  },
  logOutContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    width: '90%', // Same width as drawer items
    height: '7%', // Same height as drawer items
    marginLeft: '3%', // Align with drawer items
    paddingLeft: 10, // Adjust padding for alignment
    backgroundColor: 'transparent',
    marginTop: '2%', // Maintain consistency
  },
  signOutText: {
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: '#FF2C2C',
    marginLeft: '6%',
  },
});
