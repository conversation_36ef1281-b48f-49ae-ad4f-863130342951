import {StyleSheet, TouchableOpacity, View, Animated} from 'react-native';
import React, {useRef, useCallback} from 'react';
import {useNavigation} from '@react-navigation/native';
import DrawerIcon from '../../../assets/svgs/DrawerIcon';
import PageLogo from '../../../assets/svgs/PageLogo';

const LandingHeader = ({scrollY}) => {
  const navigation = useNavigation();
  const isTogglingRef = useRef(false);

  // Handle Drawer Toggle with debounce logic
  const handleToggleDrawer = useCallback(() => {
    if (!isTogglingRef.current) {
      isTogglingRef.current = true;
      navigation.toggleDrawer();

      // Reset after drawer animation completes
      setTimeout(() => {
        isTogglingRef.current = false;
      }, 500);
    }
  }, [navigation]);

  // Animated background color depending on scroll position
  const animatedBackground = {
    backgroundColor: scrollY.interpolate({
      inputRange: [0, 100],
      outputRange: ['transparent', '#0C121D'],
      extrapolate: 'clamp',
    }),
  };

  return (
    <Animated.View style={[styles.container, animatedBackground]}>
      {/* Drawer Icon */}
      <TouchableOpacity
        style={styles.drawerIcon}
        onPress={handleToggleDrawer}
        disabled={isTogglingRef.current}>
        <DrawerIcon color={'#FFFFFF'} style={{marginLeft: '13%'}} />
      </TouchableOpacity>

      {/* Page Logo */}
      <PageLogo style={styles.pageLogo} />

      {/* Empty right space */}
      <View style={{flex: 1}} />
    </Animated.View>
  );
};

export default React.memo(LandingHeader);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  pageLogo: {
    flex: 4,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  drawerIcon: {
    width: 50,
    height: 50,
    alignItems: 'flex-start',
    flex: 1,
    marginLeft: '0%',
    justifyContent: 'center',
    marginTop: '3%',
  },
});
