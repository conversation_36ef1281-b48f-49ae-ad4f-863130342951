import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  BackHandler,
  LogBox,
  ActivityIndicator,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Flex} from '@ant-design/react-native';
import {useThemeStyles} from '../../Constants/useThemeStyles';
import DropDown from './Components/SearchScreen/DropDown';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AlarmsComponent from './Components/AlarmsComponents/AlarmsComponent';
import {ApiCaller} from '../../middleWare/ApiCaller';
import {GET_SITE_ALARMS, Request_Types} from '../../api/uri';
import {useSelector, useDispatch} from 'react-redux';
import {useNavigation, StackActions} from '@react-navigation/native';

import {ImageBackground} from 'react-native';
import CrticalSvg from '../../assets/svgs/CrticalSvg';
import {Style} from '@rnmapbox/maps';
import {Fonts} from '../../styles/fonts';
import GreenDotSvg from '../../assets/svgs/GreenDotSvg';
import {AlarmsColorMaping} from '../../Constants/AlarmsColorMaping';
import {darkAlarmsBackground} from '../../assets/Images/CriticalAlarmBack.png';
import {lightAlarmsBackground} from '../../assets/Images/alarmsBackgroundDark.png';
import SearchIconDark from '../../assets/svgs/SearchIconDark';
import {GetsiteID} from '../../redux/Slices/SiteIdSlice';
import NoDataFoundMessageComponent from './Components/ParametersComponent.js/Common/NoDataFoundMessageComponent';

// Ignore specific navigation warnings that might be related to the delay
LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
]);

const Alarms = () => {
  const [siteId, setSiteId] = useState('');
  const [siteLoad, setSiteLoad] = useState([]);
  const [selectedAlarmTypes, setSelectedAlarmTypes] = useState(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const isSiteID = useSelector(state => state.siteId.siteId);
  const themeStyles = useThemeStyles();

  // Use a more direct navigation approach with StackActions
  const goToLandingScreen = useCallback(() => {
    // Use StackActions.replace for immediate replacement without animation
    navigation.dispatch(
      StackActions.replace('LandingScreen', {
        screen: 'Site Profile',
      }),
    );
    return true;
  }, [navigation]);

  // Handle hardware back button press
  useEffect(() => {
    const handleBackButtonPress = () => {
      goToLandingScreen();
      return true;
    };

    BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonPress,
      );
    };
  }, [goToLandingScreen]);

  // Simplify navigation event handling
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', event => {
      if (event.data.action.type === 'GO_BACK') {
        event.preventDefault();
        goToLandingScreen();
      }
    });

    return unsubscribe;
  }, [navigation, goToLandingScreen]);

  // Optimize data fetching to reduce potential delays
  const fetchSiteAlarms = useCallback(async id => {
    if (!id) return;
    setIsLoading(true);
    try {
      const token = await AsyncStorage.getItem('Authorization');
      const response = await ApiCaller({
        method: Request_Types.GET,
        url: `${GET_SITE_ALARMS(id)}`,
        headers: {Authorization: token},
      });
      setSiteLoad(response.data?.data || []);
    } catch (error) {
      console.error('Site Alarms Fetch Error:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Listen to Redux state changes
  useEffect(() => {
    if (isSiteID && isSiteID !== siteId) {
      setSiteId(isSiteID);
      fetchSiteAlarms(isSiteID);
    }
  }, [isSiteID, siteId, fetchSiteAlarms]);

  // Set up data refresh interval
  useEffect(() => {
    if (siteId) {
      // Initial fetch
      fetchSiteAlarms(siteId);

      // Set up interval for periodic updates
      const interval = setInterval(() => {
        fetchSiteAlarms(siteId);
      }, 5 * 60 * 1000);

      return () => clearInterval(interval);
    }
  }, [siteId, fetchSiteAlarms]);

  const handleSiteIdChange = useCallback(
    async newSiteId => {
      setSiteId(newSiteId);
      dispatch(GetsiteID(newSiteId));
      try {
        await AsyncStorage.setItem('SelectedSiteId', newSiteId);
        fetchSiteAlarms(newSiteId);
      } catch (error) {
        console.error('Error saving site ID:', error);
      }
    },
    [dispatch, fetchSiteAlarms],
  );

  const AlarmCategoryBox = ({color, label, value, level}) => {
    const isSelected = selectedAlarmTypes.has(level);

    return (
      <TouchableOpacity
        style={[
          styles.alarmsCategorisedContainer,
          {
            backgroundColor: themeStyles.alarmsBackground,
            borderWidth: isSelected ? 1 : 0,
            borderColor: isSelected ? color : 'transparent',
          },
        ]}
        onPress={() => {
          const newSelected = new Set(selectedAlarmTypes);
          if (isSelected) {
            newSelected.delete(level);
          } else {
            newSelected.add(level);
          }
          setSelectedAlarmTypes(newSelected);
        }}>
        <View style={styles.categoryRow}>
          <GreenDotSvg color={color} />
          <Text style={styles.categoryText}>{label}</Text>
        </View>
        <Text style={[styles.valueText, {color: themeStyles.textColor}]}>
          {value}
        </Text>
      </TouchableOpacity>
    );
  };

  // Filter alarms based on selected types
  const getFilteredAlarms = useCallback(() => {
    if (!siteLoad) return [];

    let filteredAlarms = siteLoad;

    // Filter by selected alarm types
    if (selectedAlarmTypes.size > 0) {
      filteredAlarms = filteredAlarms.filter(alarm => {
        const alarmName = alarm.name.toLowerCase();

        // Check if outage is selected
        const outageSelected = selectedAlarmTypes.has('outage');
        const isOutageAlarm =
          alarmName.includes('llvd1 ') ||
          alarmName.includes('blvd ') ||
          alarmName.includes('llvd2 ');

        // If outage is selected and this is an outage alarm, include it
        if (outageSelected && isOutageAlarm) {
          return true;
        }

        // Check other alarm types
        return selectedAlarmTypes.has(alarm.level);
      });
    }

    // Then filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filteredAlarms = filteredAlarms.filter(
        alarm =>
          alarm.name.toLowerCase().includes(query) ||
          alarm.fullname.toLowerCase().includes(query),
      );
    }

    return filteredAlarms;
  }, [siteLoad, selectedAlarmTypes, searchQuery]);

  // Add function to count alarms by level
  const getAlarmCountByLevel = useCallback(
    type => {
      if (!siteLoad) return 0;

      // Special handling for outage alarms
      if (type === 'outage') {
        return siteLoad.filter(alarm => {
          const alarmName = alarm.name.toLowerCase();
          return (
            alarmName.includes('llvd1 ') ||
            alarmName.includes('blvd ') ||
            alarmName.includes('llvd2 ')
          );
        }).length;
      }

      // Normal handling for other alarm levels
      return siteLoad.filter(alarm => alarm.level === type).length;
    },
    [siteLoad],
  );

  return (
    <View
      style={[
        styles.container,
        {backgroundColor: themeStyles.backgroundColorPages},
      ]}>
      <View style={styles.siteContainer}>
        <View style={[styles.siteIdContainer]}>
          <View style={styles.colorDot}></View>
          <TouchableOpacity>
            <DropDown
              onSiteIdChange={handleSiteIdChange}
              isIndependent={false}
              currentSiteId={siteId}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.totalAlarmsContainer}>
          <Text
            style={{
              fontSize: 20,
              fontFamily: Fonts.BaiJamjuree_Medium,
              color: themeStyles.textColor,
              textAlign: 'right',
              width: 'auto',
            }}>
            {siteLoad && siteLoad.length > 0 ? siteLoad.length : '0'}
          </Text>
          <Text
            style={{
              color: '#96999E',
              fontSize: 12,
              fontFamily: Fonts.BaiJamjuree_Medium,
              textAlign: 'right',
              marginTop: '-3%',
            }}>
            Total Alarms
          </Text>
        </View>
      </View>

      <View style={styles.alarmsCountContainer}>
        <View style={styles.alarmsCategoryContainer}>
          <AlarmCategoryBox
            color="#FF0000"
            label="Critical"
            value={getAlarmCountByLevel('1')}
            level="1"
          />
          <AlarmCategoryBox
            color="#FFC400"
            label="Warning"
            value={getAlarmCountByLevel('4')}
            level="4"
          />
        </View>
        <View style={styles.alarmsCategoryContainer}>
          <AlarmCategoryBox
            color="#126CFF"
            label="Major"
            value={getAlarmCountByLevel('2')}
            level="2"
          />
          <AlarmCategoryBox
            color="#A9009E"
            label="Outage"
            value={getAlarmCountByLevel('outage')}
            level="outage"
          />
        </View>
        <View style={styles.alarmsCategoryContainer}>
          <AlarmCategoryBox
            color="#FF7F02"
            label="Minor"
            value={getAlarmCountByLevel('3')}
            level="3"
          />
          <AlarmCategoryBox
            color="#00B7BD"
            label="SLA"
            value={getAlarmCountByLevel('5')}
            level="5"
          />
        </View>
      </View>
      <View
        style={[
          styles.searchContainer,
          {backgroundColor: themeStyles.alarmsBackground},
        ]}>
        <TextInput
          placeholder="Search"
          placeholderTextColor={themeStyles.dropDownTextColor}
          style={{
            fontSize: 15,
            marginLeft: '0%',
            width: '83%',
            color: themeStyles.dropDownTextColor,
          }}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
        />
        <SearchIconDark color={themeStyles.iconColor} />
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator animating size="large" color="#FF7F02" />
        </View>
      ) : getFilteredAlarms().length === 0 ? (
        <NoDataFoundMessageComponent searchQuery={searchQuery} />
      ) : (
        <AlarmsComponent siteLoad={getFilteredAlarms()} />
      )}
    </View>
  );
};

export default Alarms;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  colorDot: {
    height: 12,
    width: 12,
    backgroundColor: '#00EE5D',
    borderRadius: 40,
    elevation: 2,
    marginBottom: '2%',
  },
  siteIdContainer: {
    height: '61%',
    width: '50%',
    flexDirection: 'row',
    alignContent: 'center',
    overflow: 'visible',

    marginLeft: '0%',
    alignItems: 'center',
  },
  alarmsCountContainer: {
    width: '94%',
    height: '19%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '1%', // Reduce top margin to bring it closer to site container
  },
  totalContainer: {
    height: '100%',
    width: '100%',
    flexDirection: 'column',
    alignItems: 'center',
    flex: 1,
  },
  alarmsCategorisedContainer: {
    height: '47%',
    width: '100%',
    borderRadius: 6,
    alignItems: 'center',
    flexDirection: 'column',
  },
  alarmsCategoryContainer: {
    height: '68%',
    width: '32.3%',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categoryText: {
    fontSize: 12,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#96999E',
    alignSelf: 'flex-start',
  },
  valueText: {
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_Medium,
    color: '#FFFFFF',
    alignSelf: 'flex-start',
    marginLeft: '22%',
  },
  categoryRow: {
    width: '100%',
    height: '40%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  siteContainer: {
    width: '92%',
    height: '8%',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: '15%',
    flexDirection: 'row',
    marginBottom: '-6%', // Add small margin at bottom of site container
  },
  totalAlarmsContainer: {
    height: '43%',
    width: '50%',

    justifyContent: 'center',
    flexDirection: 'column',
  },
  searchContainer: {
    height: '5.6%',

    padding: 3,
    flexDirection: 'row',

    width: '94%',

    backgroundColor: '#FFFFFF1A',
    justifyContent: 'space-around',
    borderRadius: 6,
    alignItems: 'center',

    marginTop: '-4%',
  },
});
