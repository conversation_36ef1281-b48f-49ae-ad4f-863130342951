import {
  ActivityIndicator,
  Image,
  KeyboardAvoidingView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import BottomSheet, {BottomSheetView} from '@gorhom/bottom-sheet';

import {BlurView} from '@react-native-community/blur';

import {createStaticNavigation, useNavigation} from '@react-navigation/native';

import {
  AUTHORIZE,
  baseUrl,
  GET_SITES_LIST,
  LoginApi,
  Request_Types,
} from '../../api/uri';

import {Fonts} from '../../styles/fonts';
import {ApiCaller} from '../../middleWare/ApiCaller';
import {Colors} from '../../styles/colors';
import LinearGradient from 'react-native-linear-gradient';
import ShowPassword from '../../assets/svgs/ShowPassword';
import HidePassword from '../../assets/svgs/HidePassword';
import TickSvg from '../../assets/svgs/TickSvg';
import {useAuth} from '../../Context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

const BottomSheetCustom = ({setVisible, isDark}) => {
  const navigation = useNavigation();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const {setToken} = useAuth();
  var validRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;

  const [isLoading, setLoading] = useState(false);
  let otherScreen;
  const [passwordVisible, setPasswordVisible] = useState(true);
  const [isChecked, setChecked] = useState(true);
  const [isEmailFocused, setEmailFocused] = useState(false);
  const [isPasswordFocused, setPasswordFocused] = useState(false);

  const textBackground = isDark ? '#F4F7FB' : '#293243';
  const text = isDark ? '#28283D' : '#FFFFFF';
  const buttonText = !isDark ? '#28283D' : '#FFFFFF';
  const background = isDark ? '#0E121A' : '#FFFFFF';
  const gradientBackground = isDark ? '#F4F3EFE5' : '#0E121ACC';

  const snapPoints = useMemo(() => ['58%'], []);
  const payload = {
    username: username,
    userpassword: password,
  };
  const axiosCAll = async () => {
    try {
      const response = await ApiCaller({
        method: Request_Types.POST,
        url: `${LoginApi}`,
        data: payload,
      });
      const token = response.data?.data;

      setToken(token);

      if (response.data.success) {
        await AsyncStorage.setItem('Authorization', token);

        navigation.navigate('LandingScreen');
      } else {
        alert(response.data.message);
      }
    } catch (error) {
      console.error('API Error:', error.response?.data || error.message);
    }
  };

  const show = async () => {
    if (!username.trim() || !password.trim()) {
      alert('Please Enter Name');
    } else if (!validRegex.test(username)) {
      alert('Please Enter valid email');
    } else {
      setLoading(true);
      await axiosCAll();
      setLoading(false);
    }
  };

  return (
    <BottomSheet
      index={0}
      snapPoints={snapPoints}
      backgroundStyle={[
        styles.background,
        {
          backgroundColor: gradientBackground,
          borderColor: isDark ? '#BEC4CE' : '#404346',
          borderWidth: 2,
          borderRadius: 25,
        },
      ]}
      style={styles.mainContainer}
      enablePanDownToClose={true}
      handleIndicatorStyle={[styles.headerIndicator, {backgroundColor: text}]}
      onClose={() => {
        setVisible(false);
      }}>
      <BottomSheetView style={styles.contentContainer}>
        <View style={styles.Header}>
          <Text style={[styles.title, {color: text}]}>Sign In</Text>
          <Text style={[styles.description, {color: text}]}>
            Welcome! Please Sign in to your Thunder account
          </Text>
        </View>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={10}
          style={styles.inputContainer}>
          <View>
            <TextInput
              style={[
                styles.emailInput,
                {
                  backgroundColor: textBackground,
                  color: background,

                  borderColor: isDark
                    ? isEmailFocused
                      ? '#FF7F02'
                      : textBackground
                    : isEmailFocused
                    ? '#FF7F02'
                    : '#293243',
                },
              ]}
              onFocus={() => {
                setEmailFocused(true);
              }}
              onBlur={() => {
                setEmailFocused(false);
              }}
              placeholder="Enter Your Email"
              placeholderTextColor={background}
              keyboardType="email-address"
              onChangeText={value => setUsername(value)}
              autoComplete="email"
              autoCompleteType="off"
              autoCapitalize="none"

            />
          </View>
          <View
            style={[
              styles.passwordContainer,
              {
                backgroundColor: textBackground,

                borderWidth: 1,
                borderColor: isDark
                  ? isPasswordFocused
                    ? '#FF7F02'
                    : textBackground
                  : isPasswordFocused
                  ? '#FF7F02'
                  : '#293243',
              },
            ]}>
            <TextInput
              style={[styles.passwordInput, {color: background}]}
              placeholder="Enter Your Password"
              secureTextEntry={passwordVisible}
              placeholderTextColor={background}
              onChangeText={value => setPassword(value)}
              onFocus={() => {
                setPasswordFocused(true);
              }}
              onBlur={() => {
                setPasswordFocused(false);
              }}
            />
            <TouchableOpacity
              style={styles.image}
              onPress={() => setPasswordVisible(prev => !prev)}>
              {passwordVisible ? <ShowPassword /> : <HidePassword />}
            </TouchableOpacity>
          </View>
          <View style={styles.forgetContainer}>
            <View style={styles.signedIn}>
              <TouchableOpacity
                style={[
                  styles.checkBox,
                  {
                    borderColor: isChecked
                      ? isDark
                        ? '#F4F3EFE5'
                        : '#0E121ACC'
                      : isDark
                      ? '#0E121ACC'
                      : '#F4F3EFE5',
                  },
                  isChecked && styles.checked,
                ]}
                onPress={() => setChecked(prev => !prev)}>
                {isChecked && <TickSvg />}
              </TouchableOpacity>
              <Text style={[styles.signedInText, {color: text}]}>
                Keep me sign-in
              </Text>
            </View>
            <View style={[styles.forgetPassword]}>
              <Text style={[styles.signedInText, {color: text}]}>
                Forgot password?
              </Text>
            </View>
          </View>
        </KeyboardAvoidingView>
        <View style={styles.footer}>
          <LinearGradient
            colors={Colors.buttonGradient}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 1}}
            style={styles.button}>
            <TouchableOpacity
              style={styles.button}
              onPress={() => {
                show();
              }}>
              {!isLoading ? (
                <Text style={[styles.buttonText, {color: buttonText}]}>
                  Sign In
                </Text>
              ) : (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator
                    animating={isLoading}
                    size="large"
                    color="black"
                  />
                </View>
              )}
            </TouchableOpacity>
          </LinearGradient>
        </View>
        <Text style={[styles.copyright, {color: text}]}>
          Copyright © 2024 by Thunder Energy AI
        </Text>
      </BottomSheetView>
    </BottomSheet>
  );
};

export default BottomSheetCustom;

const styles = StyleSheet.create({
  container: {},
  content: {},
  mainContainer: {
    marginHorizontal: '2.5%',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  absolute: {
    width: 100,
  },
  background: {
    backgroundColor: '',
  },
  Header: {
    marginTop: '4%',
    alignItems: 'center',
    flex: 1,
  },
  text: {
    color: 'white',
  },
  title: {
    fontSize: 25,

    fontFamily: Fonts.BaiJamjuree_SemiBold,
  },
  description: {
    fontSize: 14,

    fontFamily: Fonts.BaiJamjuree_Regular,

    textAlign: 'center',
    width: 250,
    marginTop: '1%',
  },
  inputContainer: {
    flex: 1.5,
    width: '90%',
  },
  emailInput: {
    borderRadius: 8,
    height: 50,
    fontSize: 14,
    fontWeight: '400',
    paddingVertical: 14,
    paddingHorizontal: 10,
    borderColor: '#E6EDF4',
    color: '#000',
    fontFamily: Fonts.BaiJamjuree_Regular,

    borderWidth: 1,
  },
  passwordInput: {
    fontSize: 14,
    fontWeight: '400',
    fontFamily: Fonts.BaiJamjuree_Regular,

    width: '90%',
    borderColor: '#F4F7FB',
    height: 40,
    padding: 2,
  },
  passwordContainer: {
    flexDirection: 'row',
    backgroundColor: '#28283D',
    width: '100%',
    borderRadius: 8,
    fontFamily: Fonts.BaiJamjuree_Regular,
    height: 50,
    paddingVertical: 6,
    paddingHorizontal: 10,
    marginTop: '4%',
  },
  image: {
    justifyContent: 'center',
    alignContent: 'center',
  },
  forgetContainer: {
    flexDirection: 'row',
    width: '100%',
    marginTop: '3%',
    justifyContent: 'space-between',
  },

  checkBox: {
    height: 20,
    width: 20,
    borderWidth: 1,
    borderColor: 'white',
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '1%',
  },
  icon: {
    width: 8,
    height: 8,
    resizeMode: 'cover',
  },
  checked: {
    height: 20,
    width: 20,

    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',

    backgroundColor: '#FF7F02',
  },
  signedIn: {
    flexDirection: 'row',
  },
  signedInText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginLeft: '6%',
    fontFamily: Fonts.BaiJamjuree_Regular,
    marginTop: '1%',
  },
  button: {
    width: '100%',
    height: 46,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.buttonGradient,
  },
  footer: {
    width: '90%',
    flex: 0.37,
  },
  buttonText: {
    textAlign: 'center',

    fontSize: 18,
    fontWeight: '500',
    fontFamily: Fonts.BaiJamjuree_Regular,
  },
  copyright: {
    fontSize: 8,
    fontWeight: '400',
    textAlign: 'center',
    color: '#FFFFFF',
    marginBottom: '6%',
    marginTop: '4%',
    fontFamily: Fonts.BaiJamjuree_Regular,
  },
  headerIndicator: {
    height: 5,
    width: 44,
    marginTop: 8,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 0.4,
  },
});
