import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  ImageBackground,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useNavigation} from '@react-navigation/native';
import {Fonts} from '../../../styles/fonts';
import Carousel from 'react-native-reanimated-carousel';
import {useSelector} from 'react-redux';
import Logo from '../../../assets/svgs/Logo';
import {useThemeStyles} from '../../../Constants/useThemeStyles';
import GlobeSvg from '../../../assets/svgs/GlobeSvg';
import HealthSvg from '../../../assets/svgs/HealthSvg';
import MovementSvg from '../../../assets/svgs/MovementSvg';
import PerformanceSvg from '../../../assets/svgs/PerformanceSvg';
import TrackSvg from '../../../assets/svgs/TrackSvg';
import AnalyzeSvg from '../../../assets/svgs/AnalyzeSvg';
import ControlSvg from '../../../assets/svgs/ControlSvg';
import OptimizeSvg from '../../../assets/svgs/OptimizeSvg';
import LinearGradient from 'react-native-linear-gradient';
import {Colors} from '../../../styles/colors';

const TourComponent = () => {
  const navigation = useNavigation();
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;
  const [scrollIndex, setScrollIndex] = useState(0);
  const carouselRef = useRef(null);
  const istheme = useSelector(state => state.theme.theme);
  const [isDark, setIsDark] = useState(istheme === 'dark');
  const themeStyles = useThemeStyles();

  // Tour content with background images
  const content = [
    {
      title: 'Monitor Your Site’s Infrastructure And Performance Easily.',
      description: 'Discovery',
      background: require('../../../assets/Images/TourImages_01.png'),
      description_1: 'Discovery',
      description_2: 'Health',
      description_3: 'Movement',
      description_4: 'Performance',
      descriptionLogo_1: <GlobeSvg />,
      descriptionLogo_2: <HealthSvg />,
      descriptionLogo_3: <MovementSvg />,
      descriptionLogo_4: <PerformanceSvg />,
    },
    {
      title: 'Stay Connected to Network Insights in Real Time.',
      description: 'Insights',
      background: require('../../../assets/Images/TourImages_02.png'),
      description_1: 'Track',
      description_2: 'Analyze',
      description_3: 'Control',
      description_4: 'Optimize',
      descriptionLogo_1: <TrackSvg />,
      descriptionLogo_2: <AnalyzeSvg />,
      descriptionLogo_3: <ControlSvg />,
      descriptionLogo_4: <OptimizeSvg />,
    },
    {
      title: 'Make Data-Driven Decisions with KPI Visualizations',
      description: 'Analytics',
      background: require('../../../assets/Images/TourImages_03.png'),
      description_1: 'Dashboard',
      description_2: 'Actual',
      description_3: 'Predicted',
      description_4: 'Comparison',
      descriptionLogo_1: <TrackSvg />,
      descriptionLogo_2: <AnalyzeSvg />,
      descriptionLogo_3: <ControlSvg />,
      descriptionLogo_4: <OptimizeSvg />,
    },
    {
      title: 'Unlock Artificial Intelligence (AI) for Smart Operations',
      description: 'AI',
      background: require('../../../assets/Images/TourImages_04.png'),
      description_1: 'Predictions',
      description_2: 'Anomalies',
      description_3: 'Recommendations',
      description_4: 'Optimizations',
      descriptionLogo_1: <TrackSvg />,
      descriptionLogo_2: <AnalyzeSvg />,
      descriptionLogo_3: <ControlSvg />,
      descriptionLogo_4: <OptimizeSvg />,
    },
  ];

  useEffect(() => {
    // Mark tour as completed when component mounts
    const markTourCompleted = async () => {
      try {
        await AsyncStorage.setItem('hasCompletedTour', 'true');
      } catch (error) {
        console.error('Error marking tour as completed:', error);
      }
    };

    markTourCompleted();

    // Check theme
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');
        if (storedTheme !== null) {
          setIsDark(JSON.parse(storedTheme));
        } else {
          setIsDark(istheme === 'dark');
        }
      } catch (error) {
        console.error('Error fetching theme:', error);
      }
    };

    fetchTheme();
  }, [istheme]);

  const handleNext = () => {
    if (scrollIndex < content.length - 1) {
      // Go to next slide
      carouselRef.current?.scrollTo({
        index: scrollIndex + 1,
        animated: true,
      });
    } else {
      // On last slide, navigate to appropriate screen
      handleContinue();
    }
  };

  const handleContinue = async () => {
    try {
      // Check if user is authenticated
      const user = await AsyncStorage.getItem('Authorization');

      if (user) {
        // User is authenticated, go to main screen
        navigation.replace('LandingScreen');
      } else {
        // User is not authenticated, go to onboarding
        navigation.replace('onBoarding');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Default to onBoarding in case of error
      navigation.replace('onBoarding');
    }
  };

  // Paginator component
  const Paginator = ({data, scrollIndex, isDark}) => {
    return (
      <View style={styles.paginatorContainer}>
        {Array.from({length: data}).map((_, i) => (
          <View
            key={i.toString()}
            style={[
              styles.dot,
              {
                backgroundColor:
                  scrollIndex === i
                    ? '#FF7F02'
                    : isDark
                    ? '#333333'
                    : '#FFFFFF26',
              },
            ]}
          />
        ))}
      </View>
    );
  };

  const textBackground = isDark ? '#FFFFFF' : '#333333';

  // Get current background image
  const currentBackground = content[scrollIndex]?.background;

  return (
    <ImageBackground
      style={[styles.container, {backgroundColor: themeStyles.background}]}
      source={currentBackground}
      resizeMode="cover">
      <Paginator
        data={content.length}
        scrollIndex={scrollIndex}
        isDark={isDark}
      />
      <Carousel
        ref={carouselRef}
        data={content}
        horizontal
        width={screenWidth}
        height={screenHeight * 0.6}
        autoPlay={false}
        showsHorizontalScrollIndicator={false}
        bounces={false}
        pagingEnabled
        loop={false}
        onProgressChange={(progress, absoluteProgress) => {
          const index = Math.round(absoluteProgress) % content.length;
          setScrollIndex(index);
        }}
        renderItem={({item}) => (
          <ScrollView
            style={styles.slideContainer}
            contentContainerStyle={styles.slideContentContainer}
            showsVerticalScrollIndicator={false}>
            <View style={styles.header}>
              <Logo color={isDark ? '#2C2C2C' : '#FFFFFF'} />
              <Text
                style={[styles.skipText, {color: '#50555F'}]}
                onPress={handleContinue}>
                {scrollIndex === content.length - 1 ? '' : 'Skip all'}
              </Text>
            </View>
            <Text style={[styles.title, {color: '#FFC400'}]}>{item.title}</Text>
            <View style={styles.descriptionContainer}>
              <View style={styles.descriptionContent}>
                <View style={{width: 20, height: 20}}>
                  {item.descriptionLogo_1}
                </View>
                <Text style={[styles.description, {color: '#96999E'}]}>
                  {item.description_1}
                </Text>
              </View>
              <View style={styles.descriptionContent}>
                <View style={{width: 20, height: 20}}>
                  {item.descriptionLogo_2}
                </View>
                <Text style={[styles.description, {color: '#96999E'}]}>
                  {item.description_2}
                </Text>
              </View>
              <View style={styles.descriptionContent}>
                <View style={{width: 20, height: 20}}>
                  {item.descriptionLogo_3}
                </View>
                <Text style={[styles.description, {color: '#96999E'}]}>
                  {item.description_3}
                </Text>
              </View>
              <View style={styles.descriptionContent}>
                <View style={{width: 20, height: 20}}>
                  {item.descriptionLogo_4}
                </View>
                <Text style={[styles.description, {color: '#96999E'}]}>
                  {item.description_4}
                </Text>
              </View>
            </View>
          </ScrollView>
        )}
      />

      <LinearGradient
        colors={Colors.buttonGradient}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 1}}
        style={{
          height: '6%',

          width: '100%',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 6,
          maarginBottom: '1%',
        }}>
        <TouchableOpacity onPress={handleNext} style={styles.button}>
          <Text style={[styles.buttonText, {color: '#0C121D'}]}>
            {scrollIndex === content.length - 1 ? 'Get Started' : 'Next'}
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    </ImageBackground>
  );
};

export default TourComponent;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  slideContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  slideContentContainer: {
    paddingBottom: 20,
  },
  header: {
    width: '98%',
    height: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 30,
    fontFamily: Fonts.BaiJamjuree_Regular,
    marginTop: '2%',
  },
  descriptionContainer: {
    flex: 1,
    width: '60%',
    minHeight: 200,
    flexDirection: 'column',
    marginTop: '5%',
  },
  description: {
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_Regular,
    lineHeight: 24,
    textAlign: 'left',
    width: '73%',
    marginLeft: '6%',
  },
  paginatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 15,
  },
  dot: {
    width: 84,
    height: 5,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  button: {
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    height: '100%',
    justifyContent: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
  },
  skipText: {
    fontSize: 15,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    alignSelf: 'flex-end',

    width: 70,
    textDecorationLine: 'underline',
    textAlign: 'right',
  },
  descriptionContent: {
    width: '100%',
    height: '17%',
    flexDirection: 'row',
    alignItems: 'center',
  },
});
