import React, {useEffect, useRef, useState} from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {Dimensions} from 'react-native';

import Logo from '../../assets/svgs/Logo.js';
import LinearGradient from 'react-native-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';

import BottomSheetCustom from './BottomSheetCustom.js';

import {Colors} from '../../styles/colors.js';
import {Fonts} from '../../styles/fonts.js';
import lightBackground from '../../assets/Images/NVDVBGMobileThunder01.png';
import darkBackground from '../../assets/Images/NVDVBGMobileThunder02.png';
import Carousel from 'react-native-reanimated-carousel';
import {useDispatch, useSelector} from 'react-redux';
import {changeTheme} from '../../redux/Slices/themeSlice.js';

import AsyncStorage from '@react-native-async-storage/async-storage';

const content = [
  {
    id: '1',
    title: 'Single Site View',
    description:
      ' Allows operators to manage and view detailed information about individual sites,including power consumption, health status and monitoring data.',
  },
  {
    id: '2',
    title: 'Integration',
    description:
      ' Integrate with site power equipment like Transformers, DGs, solar panels, and batteries. Gain real-time insights for predictive maintenance and performance optimization',
  },
  {
    id: '3',
    title: 'Alarms',
    description:
      ' Monitor site performance and network health with proactive alerts. Ensure uninterrupted operations with 24/7 tracking and issue resolution.',
  },
  {
    id: '4',
    title: 'OnBoarding',
    description:
      ' Maintain a complete inventory of site equipment and assets. Ensure seamless tracking, maintenance, and lifecycle management.',
  },
  {
    id: '5',
    title: 'Configuration',
    description:
      ' Make remote adjustments to site settings and equipment parameters. Deploy updates without on-site visits for greater efficiency.',
  },
  {
    id: '6',
    title: 'Eson',
    description:
      ' Automate energy management with AI-powered self-optimization mechanisms. Reduce downtime and operational costs with smart efficiency controls.',
  },
  {
    id: '7',
    title: 'Impact Meter',
    description:
      'Analyze the impact of ESON features and calculate their OPEX & CO2 reductions. Make data-driven decisions to improve efficiency and reduce waste',
  },
];

const Paginator = ({data, scrollIndex, isDark}) => {
  const activeColor = '#FF7F02';
  const inactiveColor = isDark ? '#17172A1A' : '#FFFFFF1A';

  return (
    <View style={styles.paginatorView}>
      {[...Array(data)].map((_, i) => (
        <View
          key={i.toString()}
          style={[
            styles.tab,
            {
              width: 21,
              backgroundColor: scrollIndex === i ? activeColor : inactiveColor,
              height: 5,
              marginHorizontal: 4,
              borderRadius: 6,
            },
          ]}
        />
      ))}
    </View>
  );
};

const MainComponent = ({setVisible, isDark}) => {
  const screenWidth = Dimensions.get('window').width;
  const [scrollIndex, setScrollIndex] = useState(0);
  const scrollIndexRef = useRef(0);

  const textBackground = isDark ? '#1B1814' : '#FFFFFF';
  const buttonText = !isDark ? '#28283D' : '#FFFFFF';

  return (
    <View style={[styles.endView]}>
      <Carousel
        data={content}
        horizontal
        scrollAnimationDuration={3000}
        width={screenWidth}
        height={screenWidth / 2}
        autoPlay={true}
        autoPlayInterval={3000}
        showsHorizontalScrollIndicator={false}
        bounces={false}
        pagingEnabled
        onProgressChange={(progress, absoluteProgress) => {
          const index = Math.round(absoluteProgress) % content.length;
          if (scrollIndexRef.current !== index) {
            scrollIndexRef.current = index;
            setScrollIndex(index);
          }
        }}
        renderItem={({item}) => (
          <View
            style={{justifyContent: 'center', alignItems: 'center', flex: 1}}>
            <View>
              <LinearGradient
                colors={['#FFC400', '#FF7F02']}
                style={styles.card}>
                {isDark ? (
                  <Image
                    source={require('../../assets/Images/Group2.png')}
                    style={{height: 18, width: 18}}
                  />
                ) : (
                  <Image
                    source={require('../../assets/Images/Group.png')}
                    style={{height: 18, width: 18}}
                  />
                )}
              </LinearGradient>
            </View>
            <Text style={[styles.siteViewText, {color: textBackground}]}>
              {item.title}
            </Text>
            <Text style={[styles.descriptionText, {color: textBackground}]}>
              {item.description}
            </Text>
          </View>
        )}
      />

      <Paginator
        data={content.length}
        scrollIndex={scrollIndex}
        isDark={isDark}
      />

      <View style={styles.footer}>
        <LinearGradient
          colors={Colors.buttonGradient}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
          style={styles.button}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setVisible(true)}>
            <Text style={[styles.buttonText, {color: buttonText}]}>
              Experience AI
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>
      <Text style={[styles.copyright, {color: textBackground}]}>
        Copyright © 2024 by Thunder Energy AI
      </Text>
    </View>
  );
};

const OnBoardingScreen = () => {
  const istheme = useSelector(state => state.theme.theme);

  useEffect(() => {
    const fetchTheme = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem('Theme');
        if (storedTheme !== null) {
          setIsDark(JSON.parse(storedTheme));
          dispatch(changeTheme(JSON.parse(storedTheme) ? 'dark' : 'light')); // Sync Redux
        } else {
          // Set initial theme if no theme is stored
          const initialTheme = istheme === 'dark'; // Use Redux theme as initial
          setIsDark(initialTheme);
          dispatch(changeTheme(initialTheme ? 'dark' : 'light'));
          await AsyncStorage.setItem('Theme', JSON.stringify(initialTheme)); // Store initial theme
        }
      } catch (error) {
        console.error('Error fetching/setting theme:', error);
        // Handle error, maybe set a default theme
        const initialTheme = istheme === 'dark'; // Use Redux theme as initial
        setIsDark(initialTheme);
        dispatch(changeTheme(initialTheme ? 'dark' : 'light'));
        await AsyncStorage.setItem('Theme', JSON.stringify(initialTheme)); // Store initial theme
      }
    };

    fetchTheme();
  }, [istheme, dispatch]); // Add istheme to the dependency array

  const checkTheme = async () => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    dispatch(changeTheme(newTheme ? 'dark' : 'light'));
    try {
      await AsyncStorage.setItem('Theme', JSON.stringify(newTheme));
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  const dispatch = useDispatch();
  const [isDark, setIsDark] = useState(istheme === 'dark');
  const [isVisible, setVisible] = useState(false);
  const backgroundImage = isDark ? lightBackground : darkBackground;
  const gradientBackground = isDark
    ? ['#E6EFFF', '#FFF2E5']
    : ['#161628', '#3D3D55'];

  // const changeTheme = () => {
  //   if (isDark) {
  //     return false;
  //   } else {
  //     return true;
  //   }
  // };
  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <LinearGradient colors={gradientBackground} style={styles.container}>
        <ImageBackground source={backgroundImage} style={styles.background}>
          <View style={styles.header}>
            {/* <View style={[styles.themeView]}>
              <TouchableOpacity
                style={[styles.theme, {backgroundColor: background}]}
                onPress={() => checkTheme()}>
                {isDark ? <DarkSvg /> : <LightSvg />}
              </TouchableOpacity>
            </View> */}

            <Logo style={styles.logo} color={isDark ? '#2C2C2C' : '#FFFFFF'} />

            <View style={styles.gradientText}>
              <Text style={styles.text}>Energy Analytics Platform</Text>
            </View>
          </View>
          <View style={styles.emptyView}></View>
          <View style={styles.lastView}>
            {!isVisible && (
              <MainComponent setVisible={setVisible} isDark={isDark} />
            )}
          </View>
          {isVisible && (
            <BottomSheetCustom setVisible={setVisible} isDark={isDark} />
          )}
        </ImageBackground>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
};

export default OnBoardingScreen;

const styles = StyleSheet.create({
  background: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  logo: {},
  text: {
    fontSize: 20,
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    color: Colors.buttonColor,
    lineHeight: 22,
  },
  gradientText: {
    marginTop: '3%',
  },
  header: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 0.8,
    marginTop: 60,
  },
  emptyView: {
    flex: 4.4,
  },
  endView: {
    flex: 5.9,
    justifyContent: 'center',
    alignItems: 'center',
    width: '90%',
  },
  siteViewText: {
    fontSize: 20,
    color: '#FFFFFF',
    fontFamily: Fonts.BaiJamjuree_SemiBold,
    marginTop: '2%',
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: Fonts.BaiJamjuree_Regular,
    fontWeight: '400',
    padding: 6,
    textAlign: 'center',
    color: '#FFFFFF',
    width: 320,
  },
  paginatorView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: '15%',
  },
  tab: {
    height: 5,
    marginHorizontal: 4,
    borderRadius: 6,
  },
  slidingTab: {
    width: 10,
    height: 20,
    backgroundColor: 'transparent',
    borderRadius: 5,
    position: 'absolute',
    left: 0,
  },
  button: {
    width: 299,
    height: 46,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer: {
    flex: 0.33,
  },
  buttonText: {
    fontSize: 18,
    fontFamily: Fonts.BaiJamjuree_Regular,
    fontWeight: '500',
    lineHeight: 22,
    textAlign: 'center',
  },
  card: {
    width: 38,
    height: 38,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
  },
  copyright: {
    fontSize: 8,
    fontWeight: '400',
    textAlign: 'center',
    color: '#FFFFFF',
    marginBottom: '2%',
    fontFamily: Fonts.BaiJamjuree_Regular,
  },
  modalContainer: {
    flex: 4.6,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  sheetContainer: {},
  lastView: {
    flex: 5,
  },
  theme: {
    height: 22,
    width: 22,
    backgroundColor: 'black',
    borderRadius: 20,
    resizeMode: 'cover',
    justifyContent: 'center',
    alignItems: 'center',
  },
  themeView: {
    marginBottom: '6%',
    width: '100%',
    marginLeft: '85%',
  },
});
