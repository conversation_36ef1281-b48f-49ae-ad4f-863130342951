export const baseUrl = 'https://dev.apis.thunder.softoo.co';
export const Request_Types = {
  POST: 'POST',
  DELETE: 'DELETE',
  GET: 'GET',
  PUT: 'PUT',
  PATCH: 'PATCH',
};

export const AUTHORIZE = `${baseUrl}/user/authorize`;
export const LoginApi = `${baseUrl}/user/auth/login?platform=mobile`;
export const LogoutApi = `${baseUrl}/user/auth/logout`;
export const APPLY_CHANGES_CONFIG = `https://config.apis.thunderenergy.ai/api/parametersconfiguration/operations/apply`;
export const GET_SITES_LIST = `${baseUrl}/site`;

export const GET_INTEGRATION_DROPDOWNS = `${baseUrl}/sites-onboarding/add-device-dropdowns`;

export const GET_SITE_INFO = siteId => {
  console.log('siteId', siteId);
  return `${baseUrl}/site-info/lat-long/${siteId}`;
};
export const GET_POWERFLOW = siteId => {
  return `${baseUrl}/snapshot/power-flow/${siteId}`;
};
export const GET_ENERGY_CONSUMPTIONS = (siteId, startDate, endDate) => {
  const encode = encodeURIComponent;
  return `${baseUrl}/chart/test/${siteId}?startDate=${encode(
    startDate,
  )}&endDate=${encode(endDate)}`;
};
export const GET_SITE_AVAILABILITY = (siteId, startDate, endDate) => {
  return `${baseUrl}/site/${siteId}/availability/${startDate}/${endDate}`;
};
export const GET_SOURCE_UTILIZATION = (siteId, endDate, startDate, type) => {
  return `${baseUrl}/chart/psdc/${siteId}/${startDate}/${endDate}?type=${type}`;
};
export const GET_SITE_LOAD_TREND = (siteId, startDate, endDate) => {
  return `${baseUrl}/site/${siteId}/load?startDate=${startDate}&endDate=${endDate}`;
};

export const GET_WEATHER_DETAILS = (latitude, longitude) => {
  return `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&daily=sunrise,sunset&current=temperature_2m,wind_direction_10m,weather_code,wind_speed_10m,precipitation,relative_humidity_2m,cloud_cover&wind_speed_unit=mph&timezone=Asia/Karachi`;
};
export const GET_SITE_ALARMS = siteId => {
  return `${baseUrl}/snapshot/alarms/${siteId}`;
};
export const GET_LATEST_PACKET = siteId => {
  return `${baseUrl}/site-param/last-packet/${siteId}`;
};
export const GET_INFRA_DETAILS = siteId => {
  return `${baseUrl}/infrastructure-detail/${siteId}`;
};

export const GET_PARAMETERS = (siteId, category, subCategory) => {
  return `${baseUrl}/parameters-config/parameters-values?sitecode=${siteId}&category=${category}&subCategory=${subCategory}`;
};

export const GET_ALL_PARAMS = siteId => {
  return `${baseUrl}/parameters-config/parameters?sitecode=${siteId}`;
};

export const GET_USER_DEFINED_PARAMS = (siteId, params, subCategory) => {
  return `${baseUrl}/parameters-config/parameters-values?sitecode=${siteId}&parameters=${params}&subCategory=${subCategory}`;
};

export const GET_CONFIGURATIONS = (siteId, category, subCategory) => {
  return `${baseUrl}/parameters-config/configurations-values?sitecode=${siteId}&category=${category}&subCategory=Config`;
};

export const EDIT_CONFIGURATIONS = (siteId, params, category) => {
  return `${baseUrl}/parameters-config/site-configuration-values?sitecode=${siteId}&category=${category}&subCategory=Config&parameters=${params}`;
};

export const GET_CONFIG_LOGS = (siteId, active, startDate, endDate) => {
  return `${baseUrl}/parameters-config/configuration-logs?sitecode=${siteId}&active=${active}&startDate=${startDate}&endDate=${endDate}`;
};
export const GET_DROPDOWN_CATEGORY = siteId => {
  return `${baseUrl}/parameters-config/parameters?sitecode=${siteId}`;
};
export const GET_USER_DEFINED_CONFIGS = (siteId, params) => {
  return `${baseUrl}/parameters-config/configurations-values?sitecode=${siteId}&parameters=${params}&subCategory=Config`;
};

export const GET_SITE_DEVICE_INFORMATION = siteId => {
  return `${baseUrl}/sites-onboarding/integration/list?sitecode=${siteId}`;
};

export const UPDATE_SITE_DEVICE_INFORMATION = (siteId, deviceId) => {
  return `${baseUrl}/sites-onboarding/list/snmp-config/${siteId}/${deviceId}`;
};

export const ADD_NEW_SITE_DEVICE = (siteId, deviceId) => {
  return `${baseUrl}/sites-onboarding/list-adding/snmp-config/${siteId}`;
};

export const SEARCH_SITE_DEVICE = (siteId, device) => {
  return `${baseUrl}/sites-onboarding/search-site/${siteId}/${device}`;
};
