{"name": "Thunder", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@ant-design/react-native": "^5.3.2", "@gorhom/bottom-sheet": "^5", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.13", "@react-navigation/native-stack": "^7.1.14", "@reduxjs/toolkit": "^2.4.0", "@rneui/base": "^4.0.0-rc.7", "@rneui/themed": "^4.0.0-rc.8", "@rnmapbox/maps": "^10.1.33", "@wuba/react-native-echarts": "^2.0.2", "axios": "^1.7.9", "echarts": "^5.6.0", "moment": "^2.30.1", "qs": "^6.14.0", "react": "18.3.1", "react-native": "0.76.3", "react-native-autocomplete-dropdown": "^4.3.1", "react-native-collapsible": "^1.6.2", "react-native-date-picker": "^5.0.8", "react-native-draggable-flatlist": "^4.0.3", "react-native-draggable-grid": "^2.2.2", "react-native-echarts-wrapper": "^2.0.0", "react-native-element-dropdown": "^2.12.4", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.20.2", "react-native-linear-gradient": "^2.8.3", "react-native-modal-datetime-picker": "^18.0.0", "react-native-modern-datepicker": "^1.0.0-beta.91", "react-native-orientation-locker": "^1.7.0", "react-native-plotly": "^6.0.0", "react-native-reanimated": "^3.16.3", "react-native-reanimated-carousel": "^3.5.1", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.9.2", "react-native-snap-carousel": "^1.3.1", "react-native-svg": "^15.10.0", "react-native-toast-message": "^2.2.1", "react-native-ui-datepicker": "^3.1.2", "react-native-webview": "^13.12.3", "react-redux": "^9.2.0", "rn-tourguide": "^3.3.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.3", "@react-native/eslint-config": "0.76.3", "@react-native/gradle-plugin": "^0.76.5", "@react-native/metro-config": "0.76.3", "@react-native/typescript-config": "0.76.3", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}